# 更新已实例化的图

在许多实际应用中，一个计算工作流的拓扑结构（即操作的类型和它们之间的依赖关系）是固定不变的，但每次执行时所使用的具体参数（例如，内核的输入/输出数据地址、某个计算的系数等）可能会发生变化。

如果每次参数变化都重新构建并实例化整个图，那么实例化所带来的开销可能会抵消图执行所节省的 CPU 开销。为了解决这个问题，CUDA 提供了高效的**图更新 (Graph Update)** 机制，允许在不重新实例化的情况下，直接修改一个已实例化的图 (`cudaGraphExec_t`) 的参数。

更新操作将在下一次图启动时生效，不会影响任何已经在运行中的图。

## 1. 全图更新 (Whole Graph Update)

`cudaGraphExecUpdate()` API 允许您使用一个全新的、拓扑相同的图来更新一个已实例化的图。

### 工作流程

1.  像往常一样，创建、实例化并启动您的第一个图。
2.  当参数需要变更时，创建一个新的 `cudaGraph_t` 对象。这个新图的**拓扑结构必须与原始图完全相同**。
3.  调用 `cudaGraphExecUpdate()`，传入已实例化的图和这个新的模板图。
4.  如果更新成功，下一次启动该可执行图时，它将使用新的参数。如果更新失败（例如，因为拓扑发生了变化），API 会返回错误，此时您需要销毁旧的可执行图并从新图重新实例化。

### 示例代码

```cpp
cudaGraphExec_t graphExec = NULL;

for (int i = 0; i < 10; i++) {
    cudaGraph_t graph;
    cudaGraphExecUpdateResult updateResult;
    cudaGraphNode_t errorNode;

    // 使用流捕获或手动 API 创建一个包含新参数的图
    // (这里的 do_cuda_work 每次可能会使用不同的数据指针)
    cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal);
    do_cuda_work(stream, new_data_ptr);
    cudaStreamEndCapture(stream, &graph);

    if (graphExec != NULL) {
        // 如果已经有实例化的图，尝试更新它
        cudaGraphExecUpdate(graphExec, graph, &errorNode, &updateResult);
    }

    // 如果是第一次迭代，或者更新失败，则重新实例化
    if (graphExec == NULL || updateResult != cudaGraphExecUpdateSuccess) {
        if (graphExec != NULL) {
            cudaGraphExecDestroy(graphExec);
        }
        cudaGraphInstantiate(&graphExec, graph, NULL, NULL, 0);
    }

    cudaGraphDestroy(graph); // 模板图可以销毁了
    cudaGraphLaunch(graphExec, stream);
    cudaStreamSynchronize(stream);
}
```

### 拓扑一致性要求

要使 `cudaGraphExecUpdate` 成功，新旧图的拓扑必须严格匹配，包括：
-   节点的数量和类型。
-   节点之间的依赖关系（边的连接方式）。
-   依赖关系的指定顺序。
-   汇节点（没有出度/依赖者的节点）的顺序。

## 2. 单个节点更新 (Individual Node Update)

当只有少数节点的参数需要更新时，使用单个节点更新的 API 会更加高效，因为它避免了创建和比较整个新图的开销。

CUDA 为此提供了一系列 `cudaGraphExec*NodeSetParams()` 函数。

### API 示例

```cpp
// 假设 graphExec 是一个已实例化的图，
// kernelNode 和 memcpyNode 是从原始图中获取的节点句柄。

// 更新一个内核节点的参数
cudaKernelNodeParams newKernelParams = { ... };
cudaGraphExecKernelNodeSetParams(graphExec, kernelNode, &newKernelParams);

// 更新一个 1D 内存拷贝节点的参数
cudaMemcpy3DParms newMemcpyParams = { ... };
// (注意：即使是 1D 拷贝，也通常通过 3D 参数结构体来更新)
cudaGraphExecMemcpyNodeSetParams(graphExec, memcpyNode, &newMemcpyParams);

// 更新一个内存设置节点的参数
cudaMemsetParams newMemsetParams = { ... };
cudaGraphExecMemsetNodeSetParams(graphExec, memsetNode, &newMemsetParams);
```

### 限制

图更新并非万能，它存在一些限制，例如：
-   不能改变内核节点的函数本身或其动态并行属性。
-   不能改变内存操作所涉及的设备或内存类型（例如，从设备内存变为固定内存）。
-   目前只支持更新 1D 的 `cudaMemset` 和 `cudaMemcpy` 节点。

## 3. 启用/禁用节点 (Individual Node Enable)

除了更新参数，您还可以动态地启用或禁用一个已实例化图中的特定节点。

-   `cudaGraphNodeSetEnabled(node, 0);` // 禁用节点
-   `cudaGraphNodeSetEnabled(node, 1);` // 启用节点

一个被禁用的节点在功能上等同于一个**空节点 (Empty Node)**。它不会执行任何操作，但图的依赖关系依然存在。这允许您创建一个包含所有可能操作的“超级图”，然后根据每次运行的具体需求，灵活地启用或禁用其中的一部分功能，而无需改变图的拓扑结构。
