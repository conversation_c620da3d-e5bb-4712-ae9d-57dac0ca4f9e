# 创建 CUDA Graph 的两种机制

CUDA 提供了两种主要的方式来创建图：**使用图 API 手动创建 (Explicit API)** 和 **使用流捕获 (Stream Capture)**。

## 1. 使用流捕获 (Stream Capture) - 推荐方法

流捕获是创建图最常用、最便捷的方法。它允许您重用现有的、基于流的 CUDA 代码，并将其无缝转换为一个图对象。

### 工作原理

其核心思想是，将一段正常的 CUDA 操作序列“录制”下来。

1.  调用 `cudaStreamBeginCapture()`，将一个指定的流置于“捕获模式”。
2.  在此之后，任何启动到该流中的工作（如内核、内存拷贝）都不会被立即分派到 GPU 执行，而是被追加到一个正在内部构建的图中。
3.  调用 `cudaStreamEndCapture()`，结束捕获模式，并返回一个包含所有被捕获操作的、完整的 `cudaGraph_t` 对象。

### 代码示例

```cpp
cudaGraph_t graph;
cudaStream_t stream;
cudaStreamCreate(&stream);

// 1. 将流置于捕获模式
cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal);

// 2. 像往常一样启动 CUDA 工作
kernel_A<<<..., stream>>>(...);
kernel_B<<<..., stream>>>(...);
my_library_call(stream); // 也可以是调用某个封装了 CUDA 操作的库函数
kernel_C<<<..., stream>>>(...);

// 3. 结束捕获并获取图对象
cudaStreamEndCapture(stream, &graph);

// 此时，graph 对象就包含了 kernel_A, kernel_B, libraryCall, kernel_C
// 以及它们之间的隐式依赖关系。
```

### 跨流依赖与事件

流捕获的强大之处在于它能够处理通过 CUDA 事件 (`cudaEvent_t`) 定义的跨流依赖关系。

-   当一个处于捕获模式的流记录一个事件 (`cudaEventRecord`) 时，这个事件就成了一个“捕获的事件”，它代表了图中在此之前的所有节点。
-   当另一个流等待这个“捕获的事件” (`cudaStreamWaitEvent`) 时，这个流也会自动进入捕获模式，并将其后续的操作连接到同一个图中，同时建立起正确的依赖关系。

**重要**: 所有参与捕获的流，最终都必须通过事件依赖关系重新“汇合”到最初开始捕获的那个原始流上，然后才能在原始流上调用 `cudaStreamEndCapture()` 来结束整个捕获过程。

### 限制与错误

-   **禁止同步**: 在捕获期间，严禁调用任何形式的同步 API（如 `cudaStreamSynchronize`），因为被捕获的操作并未实际执行。
-   **无效操作**: 任何无效的操作（如等待一个非捕获的事件）都会导致整个捕获过程失败，`cudaStreamEndCapture` 会返回错误和 `NULL` 图。

## 2. 使用图 API 手动创建 (Explicit API)

在某些需要对图的结构进行更精细、更动态控制的场景下，可以使用 CUDA 提供的图 API 来逐个添加节点和依赖关系。

### 适用场景

-   图的结构需要在运行时动态生成。
-   需要对图的拓扑进行非常规的、复杂的修改。
-   从一个现有的图中精确地移除或替换某个节点。

### API 示例

以下代码演示了如何手动创建一个 `A -> B, A -> C, B -> D, C -> D` 结构的图。

```cpp
cudaGraph_t graph;
cudaGraphCreate(&graph, 0); // 创建一个空图

cudaGraphNode_t a, b, c, d;
cudaKernelNodeParams nodeParams = { ... }; // 定义内核参数

// 1. 分别创建所有节点
cudaGraphAddKernelNode(&a, graph, NULL, 0, &nodeParams);
cudaGraphAddKernelNode(&b, graph, NULL, 0, &nodeParams);
cudaGraphAddKernelNode(&c, graph, NULL, 0, &nodeParams);
cudaGraphAddKernelNode(&d, graph, NULL, 0, &nodeParams);

// 2. 添加节点之间的依赖关系（边）
cudaGraphAddDependencies(graph, &b, &a, 1); // A 是 B 的依赖，即 A -> B
cudaGraphAddDependencies(graph, &c, &a, 1); // A 是 C 的依赖，即 A -> C
cudaGraphAddDependencies(graph, &d, &b, 1); // B 是 D 的依赖，即 B -> D
cudaGraphAddDependencies(graph, &d, &c, 1); // C 是 D 的依赖，即 C -> D
```

虽然这种方法比流捕获更繁琐，但它提供了无与伦比的灵活性。
