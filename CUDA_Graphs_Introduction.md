# CUDA Graphs 简介

## 核心概念：一种新的工作提交模型

CUDA Graphs 为在 CUDA 中提交工作提供了一种全新的模型。其核心思想是将一个**工作流 (workflow)**——即一系列的操作（如内核启动、内存拷贝）及其相互之间的依赖关系——预先**定义**为一个独立的**图 (Graph)** 对象，使其与后续的**执行**过程完全分离。

一个图可以被定义一次，然后被反复、高效地启动执行任意多次。

## 主要优势：为何选择 Graphs？

与传统的、基于流 (Stream) 的逐个任务提交方式相比，图模型带来了两大核心优势：

### 1. 显著降低 CPU 启动开销

在流模型中，每当您向流中放入一个内核（`kernel_A<<<...>>>`），CPU 端的驱动程序都必须执行一系列的准备和验证工作来设置和启动该内核。对于那些在 GPU 上执行时间本身很短的内核来说，这种来自 CPU 的重复性开销（overhead）可能会占到整个端到端执行时间的很大一部分，从而成为性能瓶颈。

图模型通过将工作提交分为三个独立的阶段，从根本上解决了这个问题：

1.  **定义 (Definition)**: 在此阶段，程序创建一个描述所有操作及其依赖关系的“蓝图”。这部分工作只做一次。
2.  **实例化 (Instantiation)**: 运行时获取这个图的蓝图，对其进行一次性的验证、分析和深度优化，然后创建一个高度优化的、可直接执行的**可执行图 (Executable Graph)**。绝大部分的设置和初始化成本都在这个阶段被一次性支付。
3.  **执行 (Execution)**: 在需要时，以极低的 CPU 开销将这个可执行图提交到流中执行。由于几乎所有的准备工作都已提前完成，这个启动过程非常快。

通过这种方式，定义和实例化的成本可以被分摊到成千上万次的执行中，从而极大地提升了重复性工作流的性能。

### 2. 实现更深度的全局优化

在流模型中，CUDA 驱动程序一次只能看到一个或少数几个即将执行的任务，它缺乏对整个工作流的全局视野。

而图模型将整个工作流的拓扑结构完整地呈现给 CUDA 系统。这使得 CUDA 可以在实例化的过程中，从全局的视角去发现并应用那些在零散的任务提交模式下不可能实现的优化策略，从而进一步提升 GPU 的执行效率。

## 总结

总而言之，CUDA Graphs 通过**“一次定义，多次执行”**的模式，将繁重的设置工作与轻量级的启动过程分离，是优化那些具有固定或准固定模式的、重复性计算工作流的强大工具，尤其适用于深度学习推理、科学计算模拟和信号处理等领域。
