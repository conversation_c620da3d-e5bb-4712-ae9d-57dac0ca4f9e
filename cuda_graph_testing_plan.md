# CUDA Graph 性能测试方案

## 1. 核心目标

本方案旨在通过精确的性能度量，清晰地量化并展示在以下两个维度上使用 CUDA Graph 带来的性能收益：

*   **Host 端收益 (CPU Launch Time)**: 衡量因减少 CUDA API 调用（如 `cudaLaunchKernel`）而节省的 CPU 开销和时间。这反映了 CUDA Graph 在降低驱动程序交互开销方面的优势。
*   **Device 端收益 (GPU Execution Time)**: 衡量因 CUDA Driver 对整个计算图进行整体优化（如减少核函数间隙、优化资源调度）而缩短的 GPU 实际执行时间。这反映了 Graph 在硬件执行层面的优化能力。

## 2. 性能度量策略

为了精确分离 Host 和 Device 的耗时，我们采用两种不同的计时方法：

*   **Host 端计时**:
    *   **方法**: 使用 C++ 的 `std::chrono::high_resolution_clock`。
    *   **测量方式**: 计时器包裹住提交 CUDA 任务的循环（例如，包含 `cudaLaunchKernel` 或 `cudaGraphLaunch` 的循环）。
    *   **关键点**: 计时代码块内不包含任何同步操作（如 `cudaStreamSynchronize`）。同步操作会在此之后执行，以确保 GPU 完成任务，但这部分等待时间不计入 Host 端开销。此方法可以精确测量 CPU 提交所有任务所需的时间。

*   **Device 端计时**:
    *   **方法**: 使用 CUDA 的 `cudaEvent_t` API，这是测量 GPU 执行时间的标准方法。
    *   **测量方式**: 在要测量的一系列操作之前和之后分别记录 `start` 和 `stop` 事件。
    *   **关键点**: 使用 `cudaEventElapsedTime()` 计算两个事件之间在 GPU 上流逝的精确时间。这可以准确反映 GPU 从开始执行第一个任务到完成最后一个任务的实际繁忙时间。

## 3. 测试实现

我们将在新的测试文件 `tests/test_perf_detailed.cpp` 中实现此方案。每个测试用例将输出四个核心指标：

1.  **Baseline Host Time**: 在不使用 Graph 的情况下，CPU 提交所有任务的耗时。
2.  **Baseline Device Time**: 在不使用 Graph 的情况下，GPU 执行所有任务的耗时。
3.  **Graph Host Time**: 使用 Graph 时，CPU 提交所有任务的耗时。
4.  **Graph Device Time**: 使用 Graph 时，GPU 执行所有任务的耗时。

通过对比这些指标，可以清晰地计算出 Host 和 Device 各自的性能提升百分比。

## 4. 如何解读结果

*   **Host 端性能提升**: `(Baseline Host Time - Graph Host Time) / Baseline Host Time`
    *   这个指标主要反映了 CUDA Graph 在减少 CPU 与驱动交互方面的优势。在有大量小核函数调度的场景下，这个提升会非常显著。

*   **Device 端性能提升**: `(Baseline Device Time - Graph Device Time) / Baseline Device Time`
    *   这个指标反映了 CUDA 驱动对整个工作流的优化程度。当工作流包含复杂的依赖关系时（例如，核函数之间、核函数与内存拷贝之间），这个提升会更加明显，因为它减少了 GPU 的空闲等待时间。
