# CUDA Graph Error Handling Test Coverage Analysis

This document analyzes the current state of the error handling tests for the CUDA Graph APIs and proposes a plan to improve coverage.

## 1. Summary of Existing Test Cases

The current test suite (`tests/test_error_handling.cpp`) covers the following categories of errors:

### Graph & Node Structure
- **`InstantiateGraphWithCycle`**: Ensures that instantiating a graph with a multi-node dependency cycle fails.
- **`InvalidNodeDependency`**: Prevents creating dependencies between nodes belonging to different graphs.
- **`AddCrossGraphStreamDependency`**: A specific test to ensure dependencies are not created across different graphs.

### API Argument Validation
- **`NullGraphHandle`**: Tests `cudaGraphAddEmptyNode` with a `NULL` graph handle.
- **`AddNodeToNullGraph`**: Tests `cudaGraphAddKernelNode` with a `NULL` graph handle.
- **`LaunchNullExec`**: Tests `cudaGraphLaunch` with a `NULL` executable graph.
- **`GetNodesWithNullGraph`**: Tests `cudaGraphGetNodes` with a `NULL` graph.
- **`GetDependenciesWithNullNode`**: Tests `cudaGraphNodeGetDependencies` with a `NULL` node handle.
- **`GetTypeWithNullNode`**: Tests `cudaGraphNodeGetType` with a `NULL` node handle.

### State Violations
- **`AddNodeToInstantiatedGraph`**: Ensures nodes cannot be added to a graph that has already been instantiated.
- **`DestroyNodeFromWrongGraph`**: Tests using a node in a graph it doesn't belong to and ensures destroying a node twice fails.

### Graph Update Errors (`cudaGraphExecUpdate`)
- **`UpdateExecWithIncompatibleTopology`**: Fails when the updated graph has a different number of nodes.
- **`UpdateExecWithRemovedNode`**: Fails when a node has been removed in the updated graph.
- **`UpdateParameterForNodeNotInGraph`**: Fails when setting parameters for a node from a different graph.
- **`UpdateNonExistentNode`**: Fails when setting parameters for an invalid node handle.

### Dependency Management
- **`RemoveNonExistentDependency`**: Fails when trying to remove a dependency that does not exist.

### Event & Stream Errors
- **`WaitOnInvalidEventInGraph`**: Fails when adding a wait node for a `NULL` or destroyed event.
- **`LaunchGraphAndDestroyStreamThenSync`**: Checks for failure when synchronizing an event on a stream that was destroyed after a graph launch.

## 2. Gaps in Test Coverage

While the existing tests provide a good foundation, several APIs and error scenarios remain untested.

### Untested APIs
- **`cudaGraphAddChildGraphNode`**: No tests for adding child graphs (e.g., `NULL` handle, cycle creation).
- **`cudaGraphAddMemcpyNode`**: No specific error tests (e.g., invalid pointers, zero-size copies).
- **`cudaGraphAddMemsetNode`**: No specific error tests (e.g., invalid pointers).
- **`cudaGraphClone`**: No error tests (e.g., cloning a `NULL` graph).
- **`cudaGraphNodeFindInClone`**: No error tests (e.g., finding a non-existent node).
- **`cudaGraphExec...SetParams`**: Only `KernelNode` is tested. Other node types like `MemcpyNode` and `MemsetNode` are not covered.
- **`cudaGraphUpload`**: No tests for uploading a graph to a stream.
- **Stream Capture APIs** (`cudaStreamBeginCapture`, `cudaStreamEndCapture`): No error handling tests for incorrect capture sequences or capturing illegal operations.

### Untested Scenarios
- **Parameter Validation**: More robust checks for `NULL` pointers in output arguments.
- **Resource Management**: Using graph or node handles after they have been destroyed.
- **Complex Updates**: More complex topology changes for `cudaGraphExecUpdate` (e.g., changing a node's type).
- **Multi-GPU Errors**: Scenarios involving dependencies or memory operations across different devices.

## 3. Plan for Improving Test Coverage

To address these gaps, I propose adding the following test cases.

### Phase 1: Core API Additions

1.  **`TEST_F(ErrorHandlingTest, AddChildGraphWithCycle)`**: Create a child graph that, when added to the parent, forms a dependency cycle.
2.  **`TEST_F(ErrorHandlingTest, AddChildGraphWithNullHandle)`**: Test `cudaGraphAddChildGraphNode` with a `NULL` child graph handle.
3.  **`TEST_F(ErrorHandlingTest, AddMemcpyNodeWithInvalidArgs)`**: Test `cudaGraphAddMemcpyNode` with `NULL` pointers for source or destination.
4.  **`TEST_F(ErrorHandlingTest, AddMemsetNodeWithInvalidArgs)`**: Test `cudaGraphAddMemsetNode` with a `NULL` destination pointer.
5.  **`TEST_F(ErrorHandlingTest, CloneNullGraph)`**: Test `cudaGraphClone` with a `NULL` source graph.
6.  **`TEST_F(ErrorHandlingTest, UpdateMemcpyNodeParamsWithInvalidNode)`**: Test `cudaGraphExecMemcpyNodeSetParams` with a node handle not in the graph.

### Phase 2: Stream Capture and Advanced Scenarios

7.  **`TEST_F(ErrorHandlingTest, StreamCaptureEndWithoutBegin)`**: Call `cudaStreamEndCapture` on a stream where capture was never started.
8.  **`TEST_F(ErrorHandlingTest, StreamCaptureNestedBegin)`**: Call `cudaStreamBeginCapture` on a stream that is already in capture mode.
9.  **`TEST_F(ErrorHandlingTest, StreamCaptureIllegalSync)`**: Attempt to capture `cudaStreamSynchronize`, which is an illegal operation.
10. **`TEST_F(ErrorHandlingTest, UseDestroyedGraph)`**: Instantiate a graph, destroy it, and then attempt to launch the executable.
11. **`TEST_F(ErrorHandlingTest, UseDestroyedNode)`**: Add a node, destroy it with `cudaGraphDestroyNode`, and then try to add a dependency to it.

## 4. Build Environment Issues

Before adding new tests, the current build environment must be fixed. The CMake configuration is failing because it cannot locate the `nvcc` compiler. I will need to find the correct path to the CUDA Toolkit and update the build configuration.
