# NVIDIA CUDA Graph 核心功能大纲

[TOC]

---



### 1. CUDA Graphs 简介
- **1.1. 核心思想**: 将一系列操作（如内核启动）及其依赖关系定义为“图”，与执行分离。
- **1.2. 主要优势**:
    - **降低 CPU 启动开销**: 大部分设置工作在图“实例化”阶段提前完成。
    - **全局优化**: CUDA 系统可以完整地看到整个工作流，从而进行更深度的优化。
- **1.3. 三个阶段**:
    - **定义 (Definition)**: 创建图的拓扑结构。
    - **实例化 (Instantiation)**: 验证图，并创建可执行版本。
    - **执行 (Execution)**: 在流上高效地启动可执行图。

### 2. 图的结构
- **2.1. 基本组成**:
    - **节点 (Node)**: 代表一个操作。
    - **边 (Edge)**: 代表节点之间的依赖关系。
- **2.2. 节点类型**:
    - 内核 (kernel)
    - CPU 函数调用 (CPU function call)
    - 内存拷贝 (memory copy)
    - 内存设置 (memset)
    - 空节点 (empty node)
    - 事件操作 (waiting on/recording an event)
    - 外部信号量操作 (signalling/waiting on an external semaphore)
    - 子图 (child graph)

### 3. 创建图的两种机制
- **3.1. 使用图 API 手动创建 (Explicit API)**:
    - 通过 `cudaGraphAddKernelNode`, `cudaGraphAddDependencies` 等函数逐个添加节点和边。
    - 适用于需要动态、精细控制图结构的场景。
- **3.2. 使用流捕获创建 (Stream Capture)**:
    - 将现有基于流的代码用 `cudaStreamBeginCapture()` 和 `cudaStreamEndCapture()` 包围起来。
    - 运行时自动将流中的操作转换为图。
    - **跨流依赖**: 支持使用 `cudaEventRecord()` 和 `cudaStreamWaitEvent()` 捕获跨流的依赖关系。
    - **限制与错误**:
        - 禁止在捕获期间进行同步操作（如 `cudaStreamSynchronize`）。
        - 捕获的流必须重新汇合到起始流。
        - 无效操作会导致捕获失败并使图失效。

### 4. CUDA 用户对象 (User Objects)
- **4.1. 目的**: 帮助管理图执行期间所需资源的生命周期。
- **4.2. 工作原理**:
    - 类似于 C++ 的 `shared_ptr`，将用户数据与一个引用计数和析构函数关联。
    - 图可以持有用户对象的引用。
    - 当所有引用（包括图持有的引用）都被释放后，自动调用用户提供的析构函数来清理资源。
- **4.3. 使用流程**:
    - `cudaUserObjectCreate()`: 创建对象。
    - `cudaGraphRetainUserObject()`: （概念上）让图持有引用。
    - `cudaUserObjectRelease()`: 释放用户持有的引用。

### 5. 更新已实例化的图
- **5.1. 目的**: 在不重新实例化整个图的情况下，修改已实例化图的参数，以降低开销。
- **5.2. 更新机制**:
    - **全图更新 (`cudaGraphExecUpdate`)**:
        - 提供一个拓扑结构相同但参数已更新的新图。
        - 适用于大量节点参数变更的场景。
    - **单个节点更新 (`cudaGraphExec*NodeSetParams`)**:
        - 直接修改特定节点的参数（如内核参数、`memcpy` 地址等）。
        - 适用于少量节点变更的场景，效率更高。
- **5.3. 启用/禁用节点 (`cudaGraphNodeSetEnabled`)**:
    - 动态地启用或禁用图中的某个节点。
    - 被禁用的节点功能上等同于空节点。

### 6. 从设备端启动图 (Device Graph Launch)
- **6.1. 目的**: 允许在设备端（即在 CUDA 内核中）启动图，实现数据依赖的动态控制流，避免与主机间的往返通信。
- **6.2. 创建与要求**:
    - 实例化时需使用 `cudaGraphInstantiateFlagDeviceLaunch` 标志。
    - 图必须先上传到设备 (`cudaGraphUpload`) 或通过主机端首次启动来隐式上传。
    - 对图中包含的节点类型有限制（如不能包含主机节点）。
- **6.3. 启动模式**:
    - **Fire and Forget (`cudaStreamGraphFireAndForget`)**: 异步启动一个子图，父图不等待其完成。
    - **Tail Launch (`cudaStreamGraphTailLaunch`)**: 在当前图及其所有子任务完成后，串行启动下一个图，用于实现设备端同步。
    - **Sibling Launch (`cudaStreamGraphFireAndForgetAsSibling`)**: 作为父图的“兄弟”而非“孩子”来启动，不影响父图的完成状态。
