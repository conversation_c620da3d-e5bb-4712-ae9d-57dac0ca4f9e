#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <cudnn.h>
#include <vector>
#include <iostream>

#define CUDA_CHECK(err) { \
    cudaError_t err_ = (err); \
    if (err_ != cudaSuccess) { \
        FAIL() << "CUDA error at " << __FILE__ << ":" << __LINE__ << ": " << cudaGetErrorString(err_); \
    } \
}

#define CUDNN_CHECK(err) { \
    cudnnStatus_t err_ = (err); \
    if (err_ != CUDNN_STATUS_SUCCESS) { \
        FAIL() << "cuDNN error at " << __FILE__ << ":" << __LINE__ << ": " << cudnnGetStatusString(err_); \
    } \
}

// Test fixture for cuDNN operations in graph capture
class CudnnGraphCaptureTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaStreamCreate(&stream));
        CUDNN_CHECK(cudnnCreate(&cudnn_handle));
        CUDNN_CHECK(cudnnSetStream(cudnn_handle, stream));

        // Set up tensor dimensions: NCHW format
        batch_size = 1;
        channels = 3;
        height = 8;
        width = 8;

        // Input tensor
        CUDNN_CHECK(cudnnCreateTensorDescriptor(&input_desc));
        CUDNN_CHECK(cudnnSetTensor4dDescriptor(input_desc, CUDNN_TENSOR_NCHW, CUDNN_DATA_FLOAT, batch_size, channels, height, width));

        // Output tensor
        CUDNN_CHECK(cudnnCreateTensorDescriptor(&output_desc));
        CUDNN_CHECK(cudnnSetTensor4dDescriptor(output_desc, CUDNN_TENSOR_NCHW, CUDNN_DATA_FLOAT, batch_size, channels, height, width));

        // Allocate device memory
        input_bytes = batch_size * channels * height * width * sizeof(float);
        CUDA_CHECK(cudaMalloc(&d_input, input_bytes));
        CUDA_CHECK(cudaMalloc(&d_output, input_bytes));

        // Initialize input data
        std::vector<float> h_input(batch_size * channels * height * width);
        for (size_t i = 0; i < h_input.size(); ++i) {
            h_input[i] = static_cast<float>(i % 32);
        }
        CUDA_CHECK(cudaMemcpy(d_input, h_input.data(), input_bytes, cudaMemcpyHostToDevice));
    }

    void TearDown() override {
        CUDNN_CHECK(cudnnDestroyTensorDescriptor(input_desc));
        CUDNN_CHECK(cudnnDestroyTensorDescriptor(output_desc));
        if (conv_desc) CUDNN_CHECK(cudnnDestroyConvolutionDescriptor(conv_desc));
        if (filter_desc) CUDNN_CHECK(cudnnDestroyFilterDescriptor(filter_desc));
        if (pooling_desc) CUDNN_CHECK(cudnnDestroyPoolingDescriptor(pooling_desc));
        if (activation_desc) CUDNN_CHECK(cudnnDestroyActivationDescriptor(activation_desc));
        
        CUDA_CHECK(cudaFree(d_input));
        CUDA_CHECK(cudaFree(d_output));
        if (d_filter) CUDA_CHECK(cudaFree(d_filter));
        if (d_workspace) CUDA_CHECK(cudaFree(d_workspace));

        CUDNN_CHECK(cudnnDestroy(cudnn_handle));
        CUDA_CHECK(cudaStreamDestroy(stream));
    }

    cudaStream_t stream;
    cudnnHandle_t cudnn_handle;
    cudnnTensorDescriptor_t input_desc, output_desc;
    cudnnConvolutionDescriptor_t conv_desc = nullptr;
    cudnnFilterDescriptor_t filter_desc = nullptr;
    cudnnPoolingDescriptor_t pooling_desc = nullptr;
    cudnnActivationDescriptor_t activation_desc = nullptr;

    float *d_input = nullptr, *d_output = nullptr, *d_filter = nullptr, *d_workspace = nullptr;
    int batch_size, channels, height, width;
    size_t input_bytes, workspace_bytes = 0;
};

// Test capturing a cuDNN convolution forward pass
TEST_F(CudnnGraphCaptureTest, CaptureCudnnConvolution) {
    // --- Setup Convolution ---
    const int out_channels = 3, kernel_height = 3, kernel_width = 3;
    CUDNN_CHECK(cudnnCreateConvolutionDescriptor(&conv_desc));
    CUDNN_CHECK(cudnnSetConvolution2dDescriptor(conv_desc, 1, 1, 1, 1, 1, 1, CUDNN_CONVOLUTION, CUDNN_DATA_FLOAT));

    CUDNN_CHECK(cudnnCreateFilterDescriptor(&filter_desc));
    CUDNN_CHECK(cudnnSetFilter4dDescriptor(filter_desc, CUDNN_DATA_FLOAT, CUDNN_TENSOR_NCHW, out_channels, channels, kernel_height, kernel_width));

    // Allocate and initialize filter
    size_t filter_bytes = out_channels * channels * kernel_height * kernel_width * sizeof(float);
    CUDA_CHECK(cudaMalloc(&d_filter, filter_bytes));
    std::vector<float> h_filter(out_channels * channels * kernel_height * kernel_width, 0.1f);
    CUDA_CHECK(cudaMemcpy(d_filter, h_filter.data(), filter_bytes, cudaMemcpyHostToDevice));

    // Get workspace size
    cudnnConvolutionFwdAlgo_t algo;
    CUDNN_CHECK(cudnnGetConvolutionForwardAlgorithm(cudnn_handle, input_desc, filter_desc, conv_desc, output_desc, CUDNN_CONVOLUTION_FWD_PREFER_FASTEST, 0, &algo));
    CUDNN_CHECK(cudnnGetConvolutionForwardWorkspaceSize(cudnn_handle, input_desc, filter_desc, conv_desc, output_desc, algo, &workspace_bytes));
    if (workspace_bytes > 0) {
        CUDA_CHECK(cudaMalloc(&d_workspace, workspace_bytes));
    }

    const float alpha = 1.0f, beta = 0.0f;
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUDNN_CHECK(cudnnConvolutionForward(cudnn_handle, &alpha, input_desc, d_input, filter_desc, d_filter, conv_desc, algo, d_workspace, workspace_bytes, &beta, output_desc, d_output));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification (simple check for non-zero output) ---
    std::vector<float> h_output(batch_size * channels * height * width, 0.0f);
    CUDA_CHECK(cudaMemcpy(h_output.data(), d_output, input_bytes, cudaMemcpyDeviceToHost));
    bool has_non_zero = false;
    for (float val : h_output) {
        if (val != 0.0f) {
            has_non_zero = true;
            break;
        }
    }
    ASSERT_TRUE(has_non_zero);

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing a cuDNN activation forward pass
TEST_F(CudnnGraphCaptureTest, CaptureCudnnActivation) {
    CUDNN_CHECK(cudnnCreateActivationDescriptor(&activation_desc));
    CUDNN_CHECK(cudnnSetActivationDescriptor(activation_desc, CUDNN_ACTIVATION_RELU, CUDNN_NOT_PROPAGATE_NAN, 0.0));

    const float alpha = 1.0f, beta = 0.0f;
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUDNN_CHECK(cudnnActivationForward(cudnn_handle, activation_desc, &alpha, input_desc, d_input, &beta, output_desc, d_output));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification ---
    std::vector<float> h_output(batch_size * channels * height * width);
    CUDA_CHECK(cudaMemcpy(h_output.data(), d_output, input_bytes, cudaMemcpyDeviceToHost));
    // ReLU should make negative values zero. Since input is all positive, output should be identical.
    for (int i = 0; i < h_output.size(); ++i) {
        ASSERT_FLOAT_EQ(h_output[i], static_cast<float>(i % 32));
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing a cuDNN pooling forward pass
TEST_F(CudnnGraphCaptureTest, CaptureCudnnPooling) {
    CUDNN_CHECK(cudnnCreatePoolingDescriptor(&pooling_desc));
    CUDNN_CHECK(cudnnSetPooling2dDescriptor(pooling_desc, CUDNN_POOLING_MAX, CUDNN_NOT_PROPAGATE_NAN, 2, 2, 0, 0, 2, 2));

    const float alpha = 1.0f, beta = 0.0f;
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUDNN_CHECK(cudnnPoolingForward(cudnn_handle, pooling_desc, &alpha, input_desc, d_input, &beta, output_desc, d_output));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification (simple check for non-zero output) ---
    std::vector<float> h_output(batch_size * channels * height * width, 0.0f);
    CUDA_CHECK(cudaMemcpy(h_output.data(), d_output, input_bytes, cudaMemcpyDeviceToHost));
    bool has_non_zero = false;
    for (float val : h_output) {
        if (val != 0.0f) {
            has_non_zero = true;
            break;
        }
    }
    ASSERT_TRUE(has_non_zero);

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing a chain of convolution -> activation -> pooling
TEST_F(CudnnGraphCaptureTest, CaptureConvolutionActivationPoolingChain) {
    // --- Setup Convolution ---
    const int out_channels = 3, kernel_height = 3, kernel_width = 3;
    CUDNN_CHECK(cudnnCreateConvolutionDescriptor(&conv_desc));
    CUDNN_CHECK(cudnnSetConvolution2dDescriptor(conv_desc, 1, 1, 1, 1, 1, 1, CUDNN_CONVOLUTION, CUDNN_DATA_FLOAT));
    CUDNN_CHECK(cudnnCreateFilterDescriptor(&filter_desc));
    CUDNN_CHECK(cudnnSetFilter4dDescriptor(filter_desc, CUDNN_DATA_FLOAT, CUDNN_TENSOR_NCHW, out_channels, channels, kernel_height, kernel_width));
    size_t filter_bytes = out_channels * channels * kernel_height * kernel_width * sizeof(float);
    CUDA_CHECK(cudaMalloc(&d_filter, filter_bytes));
    std::vector<float> h_filter(filter_bytes, 0.1f);
    CUDA_CHECK(cudaMemcpy(d_filter, h_filter.data(), filter_bytes, cudaMemcpyHostToDevice));
    cudnnConvolutionFwdAlgo_t algo;
    CUDNN_CHECK(cudnnGetConvolutionForwardAlgorithm(cudnn_handle, input_desc, filter_desc, conv_desc, output_desc, CUDNN_CONVOLUTION_FWD_PREFER_FASTEST, 0, &algo));
    CUDNN_CHECK(cudnnGetConvolutionForwardWorkspaceSize(cudnn_handle, input_desc, filter_desc, conv_desc, output_desc, algo, &workspace_bytes));
    if (workspace_bytes > 0) CUDA_CHECK(cudaMalloc(&d_workspace, workspace_bytes));

    // --- Setup Activation ---
    CUDNN_CHECK(cudnnCreateActivationDescriptor(&activation_desc));
    CUDNN_CHECK(cudnnSetActivationDescriptor(activation_desc, CUDNN_ACTIVATION_RELU, CUDNN_NOT_PROPAGATE_NAN, 0.0));

    // --- Setup Pooling ---
    CUDNN_CHECK(cudnnCreatePoolingDescriptor(&pooling_desc));
    CUDNN_CHECK(cudnnSetPooling2dDescriptor(pooling_desc, CUDNN_POOLING_MAX, CUDNN_NOT_PROPAGATE_NAN, 2, 2, 0, 0, 2, 2));

    const float alpha = 1.0f, beta = 0.0f;
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // Intermediate buffer
    float* d_intermediate;
    CUDA_CHECK(cudaMalloc(&d_intermediate, input_bytes));

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    // 1. Convolution
    CUDNN_CHECK(cudnnConvolutionForward(cudnn_handle, &alpha, input_desc, d_input, filter_desc, d_filter, conv_desc, algo, d_workspace, workspace_bytes, &beta, output_desc, d_intermediate));
    // 2. Activation (in-place on intermediate buffer)
    CUDNN_CHECK(cudnnActivationForward(cudnn_handle, activation_desc, &alpha, output_desc, d_intermediate, &beta, output_desc, d_intermediate));
    // 3. Pooling
    CUDNN_CHECK(cudnnPoolingForward(cudnn_handle, pooling_desc, &alpha, output_desc, d_intermediate, &beta, output_desc, d_output));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification (simple check for non-zero output) ---
    std::vector<float> h_output(batch_size * channels * height * width, 0.0f);
    CUDA_CHECK(cudaMemcpy(h_output.data(), d_output, input_bytes, cudaMemcpyDeviceToHost));
    bool has_non_zero = false;
    for (float val : h_output) {
        if (val != 0.0f) {
            has_non_zero = true;
            break;
        }
    }
    ASSERT_TRUE(has_non_zero);

    // --- Cleanup ---
    CUDA_CHECK(cudaFree(d_intermediate));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test that capture fails correctly with invalid arguments to cuDNN
TEST_F(CudnnGraphCaptureTest, CaptureCudnnWithInvalidArgsFails) {
    cudaGraph_t graph = nullptr;
    const float alpha = 1.0f, beta = 0.0f;

    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));

    // Invalid call: null descriptor
    cudnnStatus_t status = cudnnActivationForward(cudnn_handle, nullptr, &alpha, input_desc, d_input, &beta, output_desc, d_output);
    ASSERT_EQ(status, CUDNN_STATUS_BAD_PARAM);

    // End capture, which should fail because the stream is in an error state
    cudaError_t end_err = cudaStreamEndCapture(stream, &graph);
    ASSERT_NE(end_err, cudaSuccess);
    ASSERT_EQ(graph, nullptr);

    // Cleanup error state
    (void)cudaGetLastError();
}
