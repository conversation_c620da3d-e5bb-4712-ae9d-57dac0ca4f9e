#ifndef COMMON_H
#define COMMON_H

#include <iostream>
#include <vector>
#include <string>
#include <chrono>
#include <numeric>
#include <algorithm>
#include <cmath>
#include <iomanip>
#include <cstdio>
#include <cstdlib>
#include <cuda_runtime.h>

#define CHECK_CUDA(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        fprintf(stderr, "CUDA Error: %s:%d, %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
        exit(EXIT_FAILURE); \
    } \
} while (0)

__global__ void dummy_kernel(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = tan(data[idx] * 0.5f) * tanh(data[idx] * 2.0f);
    }
}

class Timer {
public:
    void start() {
        start_time = std::chrono::high_resolution_clock::now();
    }

    void stop() {
        end_time = std::chrono::high_resolution_clock::now();
    }

    double elapsed_ms() {
        return std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time).count() / 1000.0;
    }

private:
    std::chrono::time_point<std::chrono::high_resolution_clock> start_time, end_time;
};

// Placeholder for CPU usage. Implementation is OS-specific.
double get_cpu_usage() {
    // On Linux, this could be implemented by reading /proc/stat
    // On Windows, by using GetSystemTimes
    // For now, returning a placeholder.
    return 0.0;
}

void print_stats(const std::string& name, const std::vector<double>& timings, int batch_size) {
    if (timings.empty()) {
        std::cout << name << ": No data" << std::endl;
        return;
    }

    double sum = std::accumulate(timings.begin(), timings.end(), 0.0);
    double mean = sum / timings.size();
    
    double sq_sum = std::inner_product(timings.begin(), timings.end(), timings.begin(), 0.0);
    double stddev = std::sqrt(sq_sum / timings.size() - mean * mean);

    std::vector<double> sorted_timings = timings;
    std::sort(sorted_timings.begin(), sorted_timings.end());
    
    double p50 = sorted_timings[sorted_timings.size() * 0.50];
    double p99 = sorted_timings[sorted_timings.size() * 0.99];

    double qps = (1000.0 / mean) * batch_size;

    std::cout << std::fixed << std::setprecision(3);
    std::cout << name << ":" << std::endl;
    std::cout << "  Batch Size: " << batch_size << std::endl;
    std::cout << "  Avg Latency: " << mean << " ms" << std::endl;
    std::cout << "  StdDev: " << stddev << " ms" << std::endl;
    std::cout << "  P50 Latency: " << p50 << " ms" << std::endl;
    std::cout << "  P99 Latency: " << p99 << " ms" << std::endl;
    std::cout << "  Throughput (QPS): " << qps << std::endl;
}

void warmup_gpu() {
    float* d_data;
    const int n = 1024 * 1024;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(float)));
    dummy_kernel<<<1, 256>>>(d_data, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    CHECK_CUDA(cudaFree(d_data));
}

#endif // COMMON_H
