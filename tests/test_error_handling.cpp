#include <gtest/gtest.h>
#include <cuda_runtime.h>

class ErrorHandlingTest : public ::testing::Test {};

TEST_F(ErrorHandlingTest, InstantiateGraphWithCycle) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);

    // Create three nodes
    cudaGraphNode_t nodeA, nodeB, nodeC;
    cudaKernelNodeParams params = {0};
    params.func = (void*)([] __global__ () {}); // Empty kernel
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    cudaGraphAddKernelNode(&nodeA, graph, nullptr, 0, &params);
    cudaGraphAddKernelNode(&nodeB, graph, nullptr, 0, &params);
    cudaGraphAddKernelNode(&nodeC, graph, nullptr, 0, &params);
    
    // Create a dependency cycle: A -> B -> C -> A
    cudaGraphAddDependencies(graph, &nodeB, &nodeA, 1); // A -> B
    cudaGraphAddDependencies(graph, &nodeC, &nodeB, 1); // B -> C
    cudaGraphAddDependencies(graph, &nodeA, &nodeC, 1); // C -> A (this creates the cycle)

    cudaGraphExec_t exec;
    // The instantiation should detect the cycle and fail.
    // CUDA documentation states that cycles result in cudaErrorInvalidValue.
    cudaError_t err = cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);
    
    ASSERT_EQ(err, cudaErrorInvalidValue);

    cudaGraphDestroy(graph);
}

TEST_F(ErrorHandlingTest, InvalidNodeDependency) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);

    cudaGraphNode_t nodeA;
    cudaKernelNodeParams params = {0};
    params.func = (void*)([] __global__ () {});
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    cudaGraphAddKernelNode(&nodeA, graph, nullptr, 0, &params);
    
    // Create a second, unrelated graph
    cudaGraph_t graph2;
    cudaGraphCreate(&graph2, 0);
    cudaGraphNode_t nodeB; // Belongs to graph2
    cudaGraphAddKernelNode(&nodeB, graph2, nullptr, 0, &params);

    // Try to add a dependency between nodes from different graphs
    cudaError_t err = cudaGraphAddDependencies(graph, &nodeA, &nodeB, 1);
    
    // This is an invalid operation and should return an error.
    ASSERT_EQ(err, cudaErrorInvalidValue);

    cudaGraphDestroy(graph);
    cudaGraphDestroy(graph2);
}

// --- API Argument Tests ---

TEST_F(ErrorHandlingTest, NullGraphHandle) {
    ASSERT_NE(cudaGraphAddEmptyNode(nullptr, nullptr, nullptr, 0), cudaSuccess);
}

TEST_F(ErrorHandlingTest, AddNodeToNullGraph) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {};
    ASSERT_NE(cudaGraphAddKernelNode(&node, nullptr, nullptr, 0, &params), cudaSuccess);
}

TEST_F(ErrorHandlingTest, LaunchNullExec) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);
    ASSERT_NE(cudaGraphLaunch(nullptr, stream), cudaSuccess);
    cudaStreamDestroy(stream);
}

// --- State Violation Tests ---

TEST_F(ErrorHandlingTest, AddNodeToInstantiatedGraph) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    // Should fail to add a node after instantiation
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {};
    ASSERT_NE(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params), cudaSuccess);
    
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
}

// --- Update Error Tests ---

TEST_F(ErrorHandlingTest, UpdateExecWithIncompatibleTopology) {
    cudaGraphExec_t exec = nullptr;
    
    // Graph 1: One kernel
    cudaGraph_t graph1;
    cudaGraphCreate(&graph1, 0);
    cudaKernelNodeParams params1 = {};
    params1.func = (void*)([] __global__ () {});
    params1.gridDim={1,1,1}; params1.blockDim={1,1,1};
    cudaGraphAddKernelNode(nullptr, graph1, nullptr, 0, &params1);
    cudaGraphInstantiate(&exec, graph1, nullptr, nullptr, 0);

    // Graph 2: Two kernels (incompatible)
    cudaGraph_t graph2;
    cudaGraphCreate(&graph2, 0);
    cudaGraphNode_t node1;
    cudaGraphAddKernelNode(&node1, graph2, nullptr, 0, &params1);
    cudaGraphAddKernelNode(nullptr, graph2, &node1, 1, &params1);

    // Update should fail because the topology (number of nodes) is different.
    cudaGraphExecUpdateResult update_result;
    cudaError_t update_err;
    cudaGraphExecUpdate(exec, graph2, nullptr, &update_result, &update_err);
    
    ASSERT_EQ(update_result, cudaGraphExecUpdateErrorTopologyChanged);

    cudaGraphDestroy(graph1);
    cudaGraphDestroy(graph2);
    cudaGraphExecDestroy(exec);
}

TEST_F(ErrorHandlingTest, UpdateExecWithRemovedNode) {
    cudaGraphExec_t exec = nullptr;
    
    // Graph 1: A -> B
    cudaGraph_t graph1;
    cudaGraphCreate(&graph1, 0);
    cudaKernelNodeParams params = {};
    params.func = (void*)([] __global__ () {});
    params.gridDim={1,1,1}; params.blockDim={1,1,1};
    cudaGraphNode_t nodeA, nodeB;
    cudaGraphAddKernelNode(&nodeA, graph1, nullptr, 0, &params);
    cudaGraphAddKernelNode(&nodeB, graph1, &nodeA, 1, &params);
    cudaGraphInstantiate(&exec, graph1, nullptr, nullptr, 0);

    // Graph 2: A (node B is removed)
    cudaGraph_t graph2;
    cudaGraphCreate(&graph2, 0);
    cudaGraphAddKernelNode(nullptr, graph2, nullptr, 0, &params);

    // Update should fail because a node was removed.
    cudaGraphExecUpdateResult update_result;
    cudaError_t update_err;
    cudaGraphExecUpdate(exec, graph2, nullptr, &update_result, &update_err);
    
    ASSERT_EQ(update_result, cudaGraphExecUpdateErrorTopologyChanged);

    cudaGraphDestroy(graph1);
    cudaGraphDestroy(graph2);
    cudaGraphExecDestroy(exec);
}

TEST_F(ErrorHandlingTest, UpdateParameterForNodeNotInGraph) {
    cudaGraphExec_t exec = nullptr;
    
    // Graph 1 (will be instantiated)
    cudaGraph_t graph1;
    cudaGraphCreate(&graph1, 0);
    cudaKernelNodeParams params = {};
    params.func = (void*)([] __global__ () {});
    params.gridDim={1,1,1}; params.blockDim={1,1,1};
    cudaGraphAddKernelNode(nullptr, graph1, nullptr, 0, &params);
    cudaGraphInstantiate(&exec, graph1, nullptr, nullptr, 0);

    // Graph 2 (provides the invalid node handle)
    cudaGraph_t graph2;
    cudaGraphCreate(&graph2, 0);
    cudaGraphNode_t node_from_other_graph;
    cudaGraphAddKernelNode(&node_from_other_graph, graph2, nullptr, 0, &params);

    // Try to update the exec with a node handle from another graph.
    // This should fail with cudaErrorInvalidValue.
    ASSERT_EQ(cudaGraphExecKernelNodeSetParams(exec, node_from_other_graph, &params), cudaErrorInvalidValue);
    
    cudaGraphDestroy(graph1);
>>>>>>> REPLACE
    cudaGraphDestroy(graph2);
    cudaGraphExecDestroy(exec);
}

TEST_F(ErrorHandlingTest, UpdateNonExistentNode) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    // A node handle that was never added to the graph
    cudaGraphNode_t fake_node = (cudaGraphNode_t)0xDEADBEEF;
    cudaKernelNodeParams params = {};

    ASSERT_NE(cudaGraphExecKernelNodeSetParams(exec, fake_node, &params), cudaSuccess);

    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
}

// --- Graph Inspection and Modification Error Tests ---

TEST_F(ErrorHandlingTest, GetNodesWithNullGraph) {
    size_t num_nodes;
    ASSERT_NE(cudaGraphGetNodes(nullptr, nullptr, &num_nodes), cudaSuccess);
}

TEST_F(ErrorHandlingTest, GetDependenciesWithNullNode) {
    size_t num_deps;
    ASSERT_NE(cudaGraphNodeGetDependencies(nullptr, nullptr, &num_deps), cudaSuccess);
}

TEST_F(ErrorHandlingTest, GetTypeWithNullNode) {
    cudaGraphNodeType type;
    ASSERT_NE(cudaGraphNodeGetType(nullptr, &type), cudaSuccess);
}

TEST_F(ErrorHandlingTest, DestroyNodeFromWrongGraph) {
    cudaGraph_t graph1, graph2;
    cudaGraphCreate(&graph1, 0);
    cudaGraphCreate(&graph2, 0);

    cudaGraphNode_t node1;
    cudaGraphAddEmptyNode(&node1, graph1, nullptr, 0);

    // Try to destroy node1, which belongs to graph1, from graph2. This is not a valid API use.
    // While the API is `cudaGraphDestroyNode(node)`, it implies a context. Let's see if we can trigger an error.
    // Note: The `cudaGraphDestroyNode` function itself does not take a graph handle.
    // A more realistic error is trying to use the node in graph2 after it was created in graph1.
    ASSERT_NE(cudaGraphAddDependencies(graph2, &node1, &node1, 1), cudaSuccess);
    
    cudaGraphDestroyNode(node1); // This should be valid.
    ASSERT_EQ(cudaGraphDestroyNode(node1), cudaErrorInvalidValue); // Destroying twice should fail.

    cudaGraphDestroy(graph1);
    cudaGraphDestroy(graph2);
}

TEST_F(ErrorHandlingTest, RemoveNonExistentDependency) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    
    cudaGraphNode_t node1, node2;
    cudaGraphAddEmptyNode(&node1, graph, nullptr, 0);
    cudaGraphAddEmptyNode(&node2, graph, nullptr, 0);
    
    // There is no dependency between node1 and node2, so removing one should fail.
    ASSERT_NE(cudaGraphRemoveDependencies(graph, &node1, &node2, 1), cudaSuccess);

    cudaGraphDestroy(graph);
}

// --- Multi-Stream and Event Error Handling ---

TEST_F(ErrorHandlingTest, WaitOnInvalidEventInGraph) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);

    // Using a null event handle
    ASSERT_NE(cudaGraphAddEventWaitNode(nullptr, graph, nullptr, 0, nullptr), cudaSuccess);

    // Using a destroyed event handle
    cudaEvent_t destroyed_event;
    cudaEventCreate(&destroyed_event);
    cudaEventDestroy(destroyed_event);
    ASSERT_NE(cudaGraphAddEventWaitNode(nullptr, graph, nullptr, 0, destroyed_event), cudaSuccess);
    
    cudaGraphDestroy(graph);
}

TEST_F(ErrorHandlingTest, AddCrossGraphStreamDependency) {
    cudaGraph_t graphA, graphB;
    cudaStream_t streamA, streamB;
    cudaGraphCreate(&graphA, 0);
    cudaGraphCreate(&graphB, 0);
    cudaStreamCreate(&streamA);
    cudaStreamCreate(&streamB);
    
    // Create nodeA in graphA, which will run on streamA
    cudaGraphNode_t nodeA;
    cudaKernelNodeParams paramsA = {};
    paramsA.func = (void*)([] __global__ () {});
    paramsA.gridDim = {1,1,1}; paramsA.blockDim = {1,1,1};
    cudaGraphAddKernelNode(&nodeA, graphA, nullptr, 0, &paramsA);

    // Create nodeB in graphB, which will run on streamB
    cudaGraphNode_t nodeB;
    cudaKernelNodeParams paramsB = {};
    paramsB.func = (void*)([] __global__ () {});
    paramsB.gridDim = {1,1,1}; paramsB.blockDim = {1,1,1};
    cudaGraphAddKernelNode(&nodeB, graphB, nullptr, 0, &paramsB);
    
    // Attempt to make nodeB (in graphB) depend on nodeA (in graphA)
    // This is an invalid cross-graph dependency and should fail.
    ASSERT_NE(cudaGraphAddDependencies(graphB, &nodeB, &nodeA, 1), cudaSuccess);
    
    cudaGraphDestroy(graphA);
    cudaGraphDestroy(graphB);
    cudaStreamDestroy(streamA);
    cudaStreamDestroy(streamB);
}

TEST_F(ErrorHandlingTest, LaunchGraphAndDestroyStreamThenSync) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaKernelNodeParams params = {};
    params.func = (void*)([] __global__ () { for(int i=0; i<10000; ++i); }); // Non-trivial kernel
    params.gridDim = {1,1,1}; params.blockDim = {1,1,1};
    cudaGraphAddKernelNode(nullptr, graph, nullptr, 0, &params);
    
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    cudaStream_t stream;
    cudaStreamCreate(&stream);

    cudaEvent_t event;
    cudaEventCreate(&event);

    // Launch the graph, but don't wait for it
    cudaGraphLaunch(exec, stream);
    
    // Record an event right after launch
    cudaEventRecord(event, stream);
    
    // Immediately destroy the stream. This is a programming error.
    cudaStreamDestroy(stream);
    
    // Now, trying to synchronize the event recorded on the destroyed stream should fail.
    // The specific error can vary, but it must not be cudaSuccess.
    // cudaErrorStreamDestroyed is a likely candidate on newer drivers.
    ASSERT_NE(cudaEventSynchronize(event), cudaSuccess);

    cudaEventDestroy(event);
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
}

// --- New Test Cases from Coverage Analysis ---

TEST_F(ErrorHandlingTest, AddChildGraphWithCycle) {
    cudaGraph_t parentGraph, childGraph;
    cudaGraphCreate(&parentGraph, 0);
    cudaGraphCreate(&childGraph, 0);

    cudaKernelNodeParams params = {};
    params.func = (void*)([] __global__ () {});
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    // Parent: A -> child -> D
    // Child: B -> C
    // Cycle: D -> A
    cudaGraphNode_t nodeA, nodeD;
    cudaGraphAddKernelNode(&nodeA, parentGraph, nullptr, 0, &params);
    cudaGraphAddKernelNode(&nodeD, parentGraph, nullptr, 0, &params);

    cudaGraphNode_t childNode;
    cudaGraphAddChildGraphNode(&childNode, parentGraph, &nodeA, 1, childGraph);
    cudaGraphAddDependencies(parentGraph, &nodeD, &childNode, 1);
    
    // This dependency creates the cycle
    cudaGraphAddDependencies(parentGraph, &nodeA, &nodeD, 1);

    cudaGraphExec_t exec;
    ASSERT_NE(cudaGraphInstantiate(&exec, parentGraph, nullptr, nullptr, 0), cudaSuccess);

    cudaGraphDestroy(parentGraph);
    // childGraph is owned by parentGraph now, no need to destroy.
}

TEST_F(ErrorHandlingTest, AddChildGraphWithNullHandle) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    ASSERT_NE(cudaGraphAddChildGraphNode(nullptr, graph, nullptr, 0, nullptr), cudaSuccess);
    cudaGraphDestroy(graph);
}

TEST_F(ErrorHandlingTest, AddMemcpyNodeWithInvalidArgs) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaMemcpy3DParms p = {};
    // Using a null pointer for source or destination should be invalid.
    ASSERT_NE(cudaGraphAddMemcpyNode(nullptr, graph, nullptr, 0, &p), cudaSuccess);
    cudaGraphDestroy(graph);
}

TEST_F(ErrorHandlingTest, AddMemsetNodeWithInvalidArgs) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaMemsetParams p = {};
    // Using a null pointer for destination should be invalid.
    ASSERT_NE(cudaGraphAddMemsetNode(nullptr, graph, nullptr, 0, &p), cudaSuccess);
    cudaGraphDestroy(graph);
}

TEST_F(ErrorHandlingTest, CloneNullGraph) {
    cudaGraph_t clonedGraph;
    ASSERT_NE(cudaGraphClone(&clonedGraph, nullptr), cudaSuccess);
}

TEST_F(ErrorHandlingTest, UpdateMemcpyNodeParamsWithInvalidNode) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    
    int *d_a, *d_b;
    cudaMalloc(&d_a, sizeof(int));
    cudaMalloc(&d_b, sizeof(int));

    cudaGraphNode_t memcpyNode;
    cudaMemcpy3DParms params = {};
    params.srcPtr = make_cudaPitchedPtr(d_a, sizeof(int), 1, 1);
    params.dstPtr = make_cudaPitchedPtr(d_b, sizeof(int), 1, 1);
    params.extent = make_cudaExtent(sizeof(int), 1, 1);
    params.kind = cudaMemcpyDeviceToDevice;
    cudaGraphAddMemcpyNode(&memcpyNode, graph, nullptr, 0, &params);

    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    // Create a fake node handle
    cudaGraphNode_t fake_node = (cudaGraphNode_t)0xDEADBEEF;
    
    // Update should fail with an invalid node handle
    ASSERT_NE(cudaGraphExecMemcpyNodeSetParams(exec, fake_node, &params), cudaSuccess);

    cudaFree(d_a);
    cudaFree(d_b);
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
}

TEST_F(ErrorHandlingTest, StreamCaptureEndWithoutBegin) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);
    // Should fail because capture was never started.
    ASSERT_NE(cudaStreamEndCapture(stream, nullptr), cudaSuccess);
    cudaStreamDestroy(stream);
}

TEST_F(ErrorHandlingTest, StreamCaptureNestedBegin) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);
    cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal);
    // Should fail because capture is already active.
    ASSERT_NE(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    cudaStreamEndCapture(stream, nullptr); // Clean up
    cudaStreamDestroy(stream);
}

TEST_F(ErrorHandlingTest, StreamCaptureIllegalSync) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);
    cudaGraph_t graph;

    cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal);
    // cudaStreamSynchronize is not allowed during capture.
    cudaStreamSynchronize(stream);
    // The error is reported on cudaStreamEndCapture.
    ASSERT_NE(cudaStreamEndCapture(stream, &graph), cudaSuccess);

    cudaStreamDestroy(stream);
}

TEST_F(ErrorHandlingTest, UseDestroyedGraph) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);
    
    cudaGraphDestroy(graph); // Destroy the original graph.
    
    // Launching the exec should still be valid, as it's a self-contained copy.
    // But let's try to do something that requires the original graph definition.
    // For example, updating it.
    cudaGraph_t newGraph;
    cudaGraphCreate(&newGraph, 0);
    cudaGraphExecUpdateResult updateResult;
    cudaError_t updateErr;
    // This update might fail because the original graph is gone, but the API
    // might also just work on the exec. A better test is to destroy the exec.
    
    cudaGraphExecDestroy(exec);
    // Now, launching the destroyed exec should fail.
    ASSERT_NE(cudaGraphLaunch(exec, 0), cudaSuccess);

    cudaGraphDestroy(newGraph);
}

TEST_F(ErrorHandlingTest, UseDestroyedNode) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    
    cudaGraphNode_t nodeA, nodeB;
    cudaGraphAddEmptyNode(&nodeA, graph, nullptr, 0);
    cudaGraphAddEmptyNode(&nodeB, graph, nullptr, 0);

    cudaGraphDestroyNode(nodeA);

    // Any operation on the destroyed node should fail.
    ASSERT_NE(cudaGraphAddDependencies(graph, &nodeB, &nodeA, 1), cudaSuccess);

    cudaGraphDestroy(graph);
}

// --- Complex Scenarios ---

TEST_F(ErrorHandlingTest, UpdateExecChangeNodeType) {
    cudaGraph_t graph1, graph2;
    cudaGraphCreate(&graph1, 0);
    cudaGraphCreate(&graph2, 0);

    // Graph 1 has a kernel node
    cudaKernelNodeParams params = {};
    params.func = (void*)([] __global__ () {});
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    cudaGraphAddKernelNode(nullptr, graph1, nullptr, 0, &params);

    // Graph 2 has a memset node (same topology, different type)
    cudaMemsetParams memset_params = {};
    cudaGraphAddMemsetNode(nullptr, graph2, nullptr, 0, &memset_params);

    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph1, nullptr, nullptr, 0);

    // Update should fail with topology changed error
    cudaGraphExecUpdateResult update_result;
    cudaError_t update_err;
    cudaGraphExecUpdate(exec, graph2, nullptr, &update_result, &update_err);
    ASSERT_EQ(update_result, cudaGraphExecUpdateErrorNodeTypeChanged);

    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph1);
    cudaGraphDestroy(graph2);
}

TEST_F(ErrorHandlingTest, UpdateExecWithNewDependencyCycle) {
    cudaGraph_t graph1, graph2;
    cudaGraphCreate(&graph1, 0);
    
    // Graph 1: A -> B
    cudaGraphNode_t nodeA1, nodeB1;
    cudaKernelNodeParams params = {};
    params.func = (void*)([] __global__ () {});
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    cudaGraphAddKernelNode(&nodeA1, graph1, nullptr, 0, &params);
    cudaGraphAddKernelNode(&nodeB1, graph1, &nodeA1, 1, &params);
    
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph1, nullptr, nullptr, 0);

    // Graph 2: A -> B and B -> A (cycle)
    cudaGraphClone(&graph2, graph1);
    cudaGraphNode_t nodeA2 = cudaGraphNodeFindInClone(nodeA1, graph2);
    cudaGraphNode_t nodeB2 = cudaGraphNodeFindInClone(nodeB1, graph2);
    cudaGraphAddDependencies(graph2, &nodeA2, &nodeB2, 1);

    // Update should fail with a cycle error
    cudaGraphExecUpdateResult update_result;
    cudaError_t update_err;
    cudaGraphExecUpdate(exec, graph2, nullptr, &update_result, &update_err);
    ASSERT_EQ(update_result, cudaGraphExecUpdateErrorTopologyChanged);

    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph1);
    cudaGraphDestroy(graph2);
}

TEST_F(ErrorHandlingTest, UseNodeFromDestroyedGraphInUpdate) {
    cudaGraph_t graph1, graph2;
    cudaGraphCreate(&graph1, 0);
    cudaGraphCreate(&graph2, 0);

    cudaGraphNode_t node_in_graph1;
    cudaKernelNodeParams params = {};
    params.func = (void*)([] __global__ () {});
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    cudaGraphAddKernelNode(&node_in_graph1, graph1, nullptr, 0, &params);

    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph2, nullptr, nullptr, 0); // Instantiate an empty graph

    cudaGraphDestroy(graph1); // Destroy the graph containing the node

    // Update should fail because the node handle is from a destroyed graph
    ASSERT_NE(cudaGraphExecKernelNodeSetParams(exec, node_in_graph1, &params), cudaSuccess);

    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph2);
}

TEST_F(ErrorHandlingTest, CloneAndModifyOriginalGraph) {
    cudaGraph_t graph, clonedGraph;
    cudaGraphCreate(&graph, 0);
    cudaGraphAddEmptyNode(nullptr, graph, nullptr, 0);

    cudaGraphClone(&clonedGraph, graph);
    
    // Modify the original graph
    cudaGraphAddEmptyNode(nullptr, graph, nullptr, 0);

    size_t original_nodes, cloned_nodes;
    cudaGraphGetNodes(graph, nullptr, &original_nodes);
    cudaGraphGetNodes(clonedGraph, nullptr, &cloned_nodes);

    // The clone should be independent and not reflect the change
    ASSERT_EQ(original_nodes, 2);
    ASSERT_EQ(cloned_nodes, 1);

    cudaGraphDestroy(graph);
    cudaGraphDestroy(clonedGraph);
}

TEST_F(ErrorHandlingTest, GetRootNodesWithNullCount) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    ASSERT_NE(cudaGraphGetRootNodes(graph, nullptr, nullptr), cudaSuccess);
    cudaGraphDestroy(graph);
}

TEST_F(ErrorHandlingTest, GetEdgesWithNullCount) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaGraphNode_t n1, n2;
    cudaGraphAddEmptyNode(&n1, graph, nullptr, 0);
    cudaGraphAddEmptyNode(&n2, graph, &n1, 1);
    ASSERT_NE(cudaGraphGetEdges(graph, nullptr, nullptr, nullptr), cudaSuccess);
    cudaGraphDestroy(graph);
}
