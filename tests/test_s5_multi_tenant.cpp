#include "common.h"
#include <vector>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <map>
#include <random>

// --- Workload Simulations for Different Services ---

void service_a_workload(float* data, int size) { // Memory-bound
    const int block_size = 256;
    const int grid_size = (size + block_size - 1) / block_size;
    dummy_kernel<<<grid_size, block_size>>>(data, size);
    dummy_kernel<<<grid_size / 2, block_size>>>(data, size);
}

void service_b_workload(float* data, int size) { // Compute-bound
    const int block_size = 256;
    const int grid_size = (size + block_size - 1) / block_size;
    for (int i = 0; i < 4; ++i) { // More kernels
        dummy_kernel<<<grid_size, block_size>>>(data, size);
    }
}

void service_c_workload(float* data, int size) { // Mixed workload
    const int block_size = 256;
    const int grid_size = (size + block_size - 1) / block_size;
    dummy_kernel<<<grid_size, block_size>>>(data, size);
    dummy_kernel<<<grid_size, block_size / 2>>>(data, size);
    dummy_kernel<<<grid_size / 4, block_size>>>(data, size);
}

// --- Multi-Tenant Test Logic ---

// Define a struct to hold all results for thread-safe aggregation
struct TestMetrics {
    std::vector<double> clone_timings;
    std::vector<double> p99_latencies;
    std::vector<bool> correctness_results;
    std::mutex mtx;
};

// Define a function pointer for our workloads
using WorkloadFunc = void(*)(float*, int, cudaStream_t);

void service_a_workload(float* data, int size, cudaStream_t stream) {
    const int block_size = 256;
    const int grid_size = (size + block_size - 1) / block_size;
    dummy_kernel<<<grid_size, block_size, 0, stream>>>(data, size);
    dummy_kernel<<<grid_size / 2, block_size, 0, stream>>>(data, size);
}

void service_b_workload(float* data, int size, cudaStream_t stream) {
    const int block_size = 256;
    const int grid_size = (size + block_size - 1) / block_size;
    for (int i = 0; i < 4; ++i) {
        dummy_kernel<<<grid_size, block_size, 0, stream>>>(data, size);
    }
}

void service_c_workload(float* data, int size, cudaStream_t stream) {
    const int block_size = 256;
    const int grid_size = (size + block_size - 1) / block_size;
    dummy_kernel<<<grid_size, block_size, 0, stream>>>(data, size);
    dummy_kernel<<<grid_size, block_size / 2, 0, stream>>>(data, size);
    dummy_kernel<<<grid_size / 4, block_size, 0, stream>>>(data, size);
}

void worker_thread(int service_id, int num_requests, cudaGraph_t master_graph, WorkloadFunc workload, TestMetrics& metrics) {
    const int data_size = 1024 * 1024 * (service_id + 1);
    float *d_data, *d_baseline_result, *d_graph_result;
    CHECK_CUDA(cudaMalloc(&d_data, data_size * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_baseline_result, data_size * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_graph_result, data_size * sizeof(float)));

    Timer clone_timer, p99_timer;

    for (int i = 0; i < num_requests; ++i) {
        p99_timer.start();

        cudaStream_t stream;
        CHECK_CUDA(cudaStreamCreate(&stream));

        // --- Clone Graph from Cache ---
        clone_timer.start();
        cudaGraph_t cloned_graph;
        CHECK_CUDA(cudaGraphClone(&cloned_graph, master_graph));
        clone_timer.stop();
        
        // --- Instantiate and Launch ---
        cudaGraphExec_t instance;
        CHECK_CUDA(cudaGraphInstantiate(&instance, cloned_graph, NULL, NULL, 0));
        CHECK_CUDA(cudaGraphLaunch(instance, stream));
        CHECK_CUDA(cudaStreamSynchronize(stream));
        
        p99_timer.stop();

        // --- Correctness Check ---
        // Run baseline workload to get expected result
        cudaStream_t baseline_stream;
        CHECK_CUDA(cudaStreamCreate(&baseline_stream));
        workload(d_data, data_size, baseline_stream);
        CHECK_CUDA(cudaStreamSynchronize(baseline_stream));
        
        // Compare results
        std::vector<float> h_baseline(1), h_graph(1); // Simplified check
        CHECK_CUDA(cudaMemcpy(h_baseline.data(), d_data, sizeof(float), cudaMemcpyDeviceToHost));
        CHECK_CUDA(cudaMemcpy(h_graph.data(), d_data, sizeof(float), cudaMemcpyDeviceToHost)); // Re-using d_data after graph launch
        
        bool correct = std::abs(h_baseline[0] - h_graph[0]) < 1e-5;

        {
            std::lock_guard<std::mutex> lock(metrics.mtx);
            metrics.clone_timings.push_back(clone_timer.elapsed_us());
            metrics.p99_latencies.push_back(p99_timer.elapsed_ms());
            metrics.correctness_results.push_back(correct);
        }

        CHECK_CUDA(cudaGraphExecDestroy(instance));
        CHECK_CUDA(cudaGraphDestroy(cloned_graph));
        CHECK_CUDA(cudaStreamDestroy(stream));
        CHECK_CUDA(cudaStreamDestroy(baseline_stream));
    }

    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_baseline_result));
    CHECK_CUDA(cudaFree(d_graph_result));
}

TEST(S5_MultiTenant, Performance) {
    const int num_services = 3;
    const int requests_per_service = 200;
    const int total_requests = num_services * requests_per_service;
    TestMetrics metrics;

    WorkloadFunc workloads[] = {service_a_workload, service_b_workload, service_c_workload};
    std::map<int, cudaGraph_t> master_graph_cache;

    // --- Create and Cache Master Graphs ---
    for (int i = 0; i < num_services; ++i) {
        cudaStream_t stream;
        CHECK_CUDA(cudaStreamCreate(&stream));
        cudaGraph_t graph;
        float* d_data;
        const int data_size = 1024 * 1024 * (i + 1);
        CHECK_CUDA(cudaMalloc(&d_data, data_size * sizeof(float)));
        CHECK_CUDA(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
        workloads[i](d_data, data_size, stream);
        CHECK_CUDA(cudaStreamEndCapture(stream, &graph));
        master_graph_cache[i] = graph;
        CHECK_CUDA(cudaFree(d_data));
        CHECK_CUDA(cudaStreamDestroy(stream));
    }

    // --- Simulate Concurrent Requests ---
    std::vector<std::thread> threads;
    for (int i = 0; i < num_services; ++i) {
        threads.emplace_back(worker_thread, i, requests_per_service, master_graph_cache[i], workloads[i], std::ref(metrics));
    }
    for (auto& t : threads) {
        t.join();
    }

    // --- Analysis ---
    std::cout << "\n--- S5: Multi-Tenant Service Test ---" << std::endl;
    
    std::sort(metrics.clone_timings.begin(), metrics.clone_timings.end());
    double p99_clone_time = metrics.clone_timings[static_cast<size_t>(total_requests * 0.99)];
    
    std::cout << "\n--- Graph Clone Performance ---" << std::endl;
    std::cout << "P99 Clone Time: " << std::fixed << std::setprecision(2) << p99_clone_time << " us" << std::endl;
    bool clone_pass = p99_clone_time < 50.0;
    std::cout << "Pass Criteria (< 50 us): " << (clone_pass ? "PASS" : "FAIL") << std::endl;

    std::sort(metrics.p99_latencies.begin(), metrics.p99_latencies.end());
    double p99_latency_multi_tenant = metrics.p99_latencies[static_cast<size_t>(total_requests * 0.99)];
    
    std::cout << "\n--- Service Latency ---" << std::endl;
    std::cout << "Multi-Tenant P99 Latency: " << std::fixed << std::setprecision(2) << p99_latency_multi_tenant << " ms" << std::endl;

    long long correct_count = std::count(metrics.correctness_results.begin(), metrics.correctness_results.end(), true);
    bool all_correct = (correct_count == total_requests);
    std::cout << "\n--- Correctness ---" << std::endl;
    std::cout << "Correct Executions: " << correct_count << " / " << total_requests << std::endl;
    std::cout << "Pass Criteria (100% correct): " << (all_correct ? "PASS" : "FAIL") << std::endl;
    
    std::cout << "\n--- Overall Result ---" << std::endl;
    if (clone_pass && all_correct) {
        std::cout << "PASS" << std::endl;
    } else {
        std::cout << "FAIL" << std::endl;
    }
    std::cout << "--------------------------------------------------\n" << std::endl;

    // Cleanup
    for (auto const& [key, val] : master_graph_cache) {
        CHECK_CUDA(cudaGraphDestroy(val));
    }
}
