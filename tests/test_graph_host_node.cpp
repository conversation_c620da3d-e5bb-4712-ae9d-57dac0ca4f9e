#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <thread>
#include <vector>
#include <atomic>

// A simple callback function for host nodes
void CUDART_CB MyHostNodeCallback(void* data) {
    if (data) {
        // Atomically increment the integer pointed to by data
        std::atomic<int>* value = static_cast<std::atomic<int>*>(data);
        (*value)++;
    }
}

class GraphHostNodeTest : public ::testing::Test {};

// --- Functional Tests ---

TEST_F(GraphHostNodeTest, BasicHostNodeExecution) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);

    std::atomic<int> host_val(0);
    cudaHostNodeParams host_params = {};
    host_params.fn = MyHostNodeCallback;
    host_params.userData = &host_val;

    cudaGraphNode_t hostNode;
    ASSERT_EQ(cudaGraphAddHostNode(&hostNode, graph, nullptr, 0, &host_params), cudaSuccess);

    cudaGraphExec_t exec;
    ASSERT_EQ(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0), cudaSuccess);

    cudaStream_t stream;
    cudaStreamCreate(&stream);
    
    ASSERT_EQ(cudaGraphLaunch(exec, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    // The callback should have incremented the value
    ASSERT_EQ(host_val.load(), 1);

    cudaStreamDestroy(stream);
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
}

TEST_F(GraphHostNodeTest, HostNodeWithDependencies) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);

    std::atomic<int> host_val(0);
    
    // Kernel node that does nothing
    cudaKernelNodeParams kernel_params = {};
    kernel_params.func = (void*)([] __global__ () {});
    kernel_params.gridDim = {1,1,1};
    kernel_params.blockDim = {1,1,1};
    
    cudaGraphNode_t kernelNode;
    cudaGraphAddKernelNode(&kernelNode, graph, nullptr, 0, &kernel_params);

    // Host node that depends on the kernel node
    cudaHostNodeParams host_params = {};
    host_params.fn = MyHostNodeCallback;
    host_params.userData = &host_val;
    
    cudaGraphNode_t hostNode;
    cudaGraphAddHostNode(&hostNode, graph, &kernelNode, 1, &host_params);

    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    cudaStream_t stream;
    cudaStreamCreate(&stream);
    cudaGraphLaunch(exec, stream);
    cudaStreamSynchronize(stream);

    ASSERT_EQ(host_val.load(), 1);

    cudaStreamDestroy(stream);
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
}

// --- Boundary Tests ---

TEST_F(GraphHostNodeTest, AddHostNodeWithNullCallback) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);

    cudaHostNodeParams host_params = {};
    host_params.fn = nullptr; // Invalid callback
    host_params.userData = nullptr;

    cudaGraphNode_t hostNode;
    // Adding a host node with a null callback function should fail.
    ASSERT_NE(cudaGraphAddHostNode(&hostNode, graph, nullptr, 0, &host_params), cudaSuccess);

    cudaGraphDestroy(graph);
}

TEST_F(GraphHostNodeTest, HostNodeWithNoCallbackData) {
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);

    cudaHostNodeParams host_params = {};
    host_params.fn = MyHostNodeCallback;
    host_params.userData = nullptr; // No data passed

    cudaGraphNode_t hostNode;
    ASSERT_EQ(cudaGraphAddHostNode(&hostNode, graph, nullptr, 0, &host_params), cudaSuccess);

    cudaGraphExec_t exec;
    ASSERT_EQ(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0), cudaSuccess);

    cudaStream_t stream;
    cudaStreamCreate(&stream);
    // The graph should run successfully even with null userData.
    ASSERT_EQ(cudaGraphLaunch(exec, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    cudaStreamDestroy(stream);
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
}

// --- Stress Tests ---

TEST_F(GraphHostNodeTest, DISABLED_StressTestManyNodes) {
    const int num_nodes = 1000;
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);

    std::atomic<int> host_val(0);
    cudaHostNodeParams host_params = {};
    host_params.fn = MyHostNodeCallback;
    host_params.userData = &host_val;

    cudaGraphNode_t last_node = nullptr;
    for (int i = 0; i < num_nodes; ++i) {
        cudaGraphNode_t current_node;
        cudaGraphAddHostNode(&current_node, graph, last_node ? &last_node : nullptr, last_node ? 1 : 0, &host_params);
        last_node = current_node;
    }

    cudaGraphExec_t exec;
    ASSERT_EQ(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0), cudaSuccess);

    cudaStream_t stream;
    cudaStreamCreate(&stream);
    cudaGraphLaunch(exec, stream);
    cudaStreamSynchronize(stream);

    ASSERT_EQ(host_val.load(), num_nodes);

    cudaStreamDestroy(stream);
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
}
