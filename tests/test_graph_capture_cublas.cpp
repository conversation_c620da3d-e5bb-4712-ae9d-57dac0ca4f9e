#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <vector>
#include <iostream>

#define CUDA_CHECK(err) { \
    cudaError_t err_ = (err); \
    if (err_ != cudaSuccess) { \
        FAIL() << "CUDA error at " << __FILE__ << ":" << __LINE__ << ": " << cudaGetErrorString(err_); \
    } \
}

#define CUBLAS_CHECK(err) { \
    cublasStatus_t err_ = (err); \
    if (err_ != CUBLAS_STATUS_SUCCESS) { \
        FAIL() << "cuBLAS error at " << __FILE__ << ":" << __LINE__ << ": " << cublasGetStatusString(err_); \
    } \
}

// Simple kernel for testing - can be moved to a common header if used by more tests
__global__ void simple_kernel(float* out, const float* in, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        out[idx] = in[idx] * 2.0f;
    }
}

// Test fixture for cuBLAS operations in graph capture
class CublasGraphCaptureTest : public ::testing::Test {
protected:
    void SetUp() override {
        M = N = K = 256;
        bytes_A = M * K * sizeof(float);
        bytes_B = K * N * sizeof(float);
        bytes_C = M * N * sizeof(float);

        h_A.resize(M * K);
        h_B.resize(K * N);
        h_C.resize(M * N, 0);
        h_C_ref.resize(M * N, 0);

        for (int i = 0; i < M * K; ++i) h_A[i] = static_cast<float>(rand()) / RAND_MAX;
        for (int i = 0; i < K * N; ++i) h_B[i] = static_cast<float>(rand()) / RAND_MAX;

        CUDA_CHECK(cudaMalloc(&d_A, bytes_A));
        CUDA_CHECK(cudaMalloc(&d_B, bytes_B));
        CUDA_CHECK(cudaMalloc(&d_C, bytes_C));
        CUDA_CHECK(cudaMalloc(&d_D, bytes_C)); // Another buffer for chained operations

        CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), bytes_A, cudaMemcpyHostToDevice));
        CUDA_CHECK(cudaMemcpy(d_B, h_B.data(), bytes_B, cudaMemcpyHostToDevice));

        CUDA_CHECK(cudaStreamCreate(&stream));
        CUBLAS_CHECK(cublasCreate(&cublas_handle));
        CUBLAS_CHECK(cublasSetStream(cublas_handle, stream));
    }

    void TearDown() override {
        CUBLAS_CHECK(cublasDestroy(cublas_handle));
        CUDA_CHECK(cudaStreamDestroy(stream));
        CUDA_CHECK(cudaFree(d_A));
        CUDA_CHECK(cudaFree(d_B));
        CUDA_CHECK(cudaFree(d_C));
        CUDA_CHECK(cudaFree(d_D));
    }

    // Reference CPU implementation for verification
    void matrix_mult_cpu(const float* A, const float* B, float* C, int m, int n, int k) {
        for (int i = 0; i < m; ++i) {
            for (int j = 0; j < n; ++j) {
                float sum = 0.0f;
                for (int l = 0; l < k; ++l) {
                    sum += A[i * k + l] * B[l * n + j];
                }
                C[i * n + j] = sum;
            }
        }
    }

    int M, N, K;
    size_t bytes_A, bytes_B, bytes_C;
    std::vector<float> h_A, h_B, h_C, h_C_ref;
    float *d_A, *d_B, *d_C, *d_D;
    cudaStream_t stream;
    cublasHandle_t cublas_handle;
};

// Test capturing a single cuBLAS matrix multiplication
TEST_F(CublasGraphCaptureTest, CaptureSingleCublasCall) {
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;
    const float alpha = 1.0f, beta = 0.0f;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUBLAS_CHECK(cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_N, N, M, K, &alpha, d_B, N, d_A, K, &beta, d_C, N));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification ---
    CUDA_CHECK(cudaMemcpy(h_C.data(), d_C, bytes_C, cudaMemcpyDeviceToHost));
    matrix_mult_cpu(h_A.data(), h_B.data(), h_C_ref.data(), M, N, K);

    for (int i = 0; i < M * N; ++i) {
        ASSERT_NEAR(h_C[i], h_C_ref[i], 1e-3);
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing multiple sequential cuBLAS matrix multiplications
TEST_F(CublasGraphCaptureTest, CaptureMultipleCublasCalls) {
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;
    const float alpha = 1.0f, beta = 0.0f;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    // First operation: C = A * B
    CUBLAS_CHECK(cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_N, N, M, K, &alpha, d_B, N, d_A, K, &beta, d_C, N));
    // Second operation: D = C * A' (using d_A as a stand-in for A')
    CUBLAS_CHECK(cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_T, K, M, N, &alpha, d_A, K, d_C, N, &beta, d_D, K));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification ---
    // Verify the result of the second operation (in d_D)
    std::vector<float> h_D(M * K);
    CUDA_CHECK(cudaMemcpy(h_D.data(), d_D, M * K * sizeof(float), cudaMemcpyDeviceToHost));

    // Calculate reference result on CPU
    matrix_mult_cpu(h_A.data(), h_B.data(), h_C_ref.data(), M, N, K); // C = A * B
    std::vector<float> h_A_T(K * M);
    for(int i=0; i<M; ++i) for(int j=0; j<K; ++j) h_A_T[j*M + i] = h_A[i*K + j];
    std::vector<float> h_D_ref(M * K);
    matrix_mult_cpu(h_C_ref.data(), h_A_T.data(), h_D_ref.data(), M, K, N); // D = C * A'

    for (int i = 0; i < M * K; ++i) {
        ASSERT_NEAR(h_D[i], h_D_ref[i], 1e-1); // Looser tolerance for chained operations
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing a mix of custom kernels and cuBLAS calls
TEST_F(CublasGraphCaptureTest, CaptureMixedKernelAndCublas) {
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;
    const float alpha = 1.0f, beta = 0.0f;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    // Step 1: Pre-process matrix A with a custom kernel
    simple_kernel<<< (M * K + 255) / 256, 256, 0, stream >>>(d_A, d_A, M * K);
    // Step 2: Matrix multiplication C = A * B
    CUBLAS_CHECK(cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_N, N, M, K, &alpha, d_B, N, d_A, K, &beta, d_C, N));
    // Step 3: Post-process result matrix C with a custom kernel
    simple_kernel<<< (M * N + 255) / 256, 256, 0, stream >>>(d_C, d_C, M * N);
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    // Reset data since the graph modifies it in place
    CUDA_CHECK(cudaMemcpy(d_A, h_A.data(), bytes_A, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemset(d_C, 0, bytes_C));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification ---
    CUDA_CHECK(cudaMemcpy(h_C.data(), d_C, bytes_C, cudaMemcpyDeviceToHost));

    // Calculate reference result on CPU
    std::vector<float> h_A_processed = h_A;
    for(auto& val : h_A_processed) val *= 2.0f; // Kernel effect
    matrix_mult_cpu(h_A_processed.data(), h_B.data(), h_C_ref.data(), M, N, K);
    for(auto& val : h_C_ref) val *= 2.0f; // Kernel effect

    for (int i = 0; i < M * N; ++i) {
        ASSERT_NEAR(h_C[i], h_C_ref[i], 1e-2);
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing cuBLAS with transposed matrices
TEST_F(CublasGraphCaptureTest, CaptureCublasWithTranspose) {
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;
    const float alpha = 1.0f, beta = 0.0f;

    // C = A^T * B
    // Dimensions: (KxM) * (KxN) -> (MxN)
    // We need to adjust the leading dimensions in cublasSgemm call
    // A is MxK, so A^T is KxM. B is KxN. Result C is MxN.
    CUBLAS_CHECK(cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_T, N, M, K, &alpha, d_B, N, d_A, K, &beta, d_C, N));
    
    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUBLAS_CHECK(cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_T, N, M, K, &alpha, d_B, N, d_A, K, &beta, d_C, N));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaMemset(d_C, 0, bytes_C)); // Reset C
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification ---
    CUDA_CHECK(cudaMemcpy(h_C.data(), d_C, bytes_C, cudaMemcpyDeviceToHost));
    
    std::vector<float> h_A_T(M * K);
    for(int i=0; i<K; ++i) for(int j=0; j<M; ++j) h_A_T[j*K + i] = h_A[i*K + j];
    matrix_mult_cpu(h_A_T.data(), h_B.data(), h_C_ref.data(), M, N, K);

    for (int i = 0; i < M * N; ++i) {
        ASSERT_NEAR(h_C[i], h_C_ref[i], 1e-3);
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing cuBLAS with a non-zero beta value for accumulation
TEST_F(CublasGraphCaptureTest, CaptureCublasWithNonZeroBeta) {
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;
    const float alpha = 1.0f, beta = 0.5f;

    // Pre-fill C with some data
    std::vector<float> h_C_initial(M * N);
    for (int i = 0; i < M * N; ++i) h_C_initial[i] = static_cast<float>(rand()) / RAND_MAX;
    CUDA_CHECK(cudaMemcpy(d_C, h_C_initial.data(), bytes_C, cudaMemcpyHostToDevice));

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUBLAS_CHECK(cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_N, N, M, K, &alpha, d_B, N, d_A, K, &beta, d_C, N));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    // Reset C to its initial state for the graph run
    CUDA_CHECK(cudaMemcpy(d_C, h_C_initial.data(), bytes_C, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification ---
    CUDA_CHECK(cudaMemcpy(h_C.data(), d_C, bytes_C, cudaMemcpyDeviceToHost));
    
    matrix_mult_cpu(h_A.data(), h_B.data(), h_C_ref.data(), M, N, K);
    for (int i = 0; i < M * N; ++i) {
        h_C_ref[i] = alpha * h_C_ref[i] + beta * h_C_initial[i];
        ASSERT_NEAR(h_C[i], h_C_ref[i], 1e-3);
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing a batched matrix multiplication
TEST_F(CublasGraphCaptureTest, CaptureBatchedCublasCall) {
    const int batch_count = 4;
    const float alpha = 1.0f, beta = 0.0f;

    // Allocate memory for batched operation
    float **d_A_array, **d_B_array, **d_C_array;
    CUDA_CHECK(cudaMalloc(&d_A_array, batch_count * sizeof(float*)));
    CUDA_CHECK(cudaMalloc(&d_B_array, batch_count * sizeof(float*)));
    CUDA_CHECK(cudaMalloc(&d_C_array, batch_count * sizeof(float*)));

    std::vector<float*> h_A_array(batch_count), h_B_array(batch_count), h_C_array(batch_count);
    std::vector<float*> d_A_batches(batch_count), d_B_batches(batch_count), d_C_batches(batch_count);

    for (int i = 0; i < batch_count; ++i) {
        CUDA_CHECK(cudaMalloc(&d_A_batches[i], bytes_A));
        CUDA_CHECK(cudaMalloc(&d_B_batches[i], bytes_B));
        CUDA_CHECK(cudaMalloc(&d_C_batches[i], bytes_C));
        CUDA_CHECK(cudaMemcpy(d_A_batches[i], h_A.data(), bytes_A, cudaMemcpyHostToDevice));
        CUDA_CHECK(cudaMemcpy(d_B_batches[i], h_B.data(), bytes_B, cudaMemcpyHostToDevice));
        h_A_array[i] = d_A_batches[i];
        h_B_array[i] = d_B_batches[i];
        h_C_array[i] = d_C_batches[i];
    }

    CUDA_CHECK(cudaMemcpy(d_A_array, h_A_array.data(), batch_count * sizeof(float*), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_B_array, h_B_array.data(), batch_count * sizeof(float*), cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_C_array, h_C_array.data(), batch_count * sizeof(float*), cudaMemcpyHostToDevice));

    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUBLAS_CHECK(cublasSgemmBatched(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_N, N, M, K, &alpha,
                                    (const float**)d_B_array, N,
                                    (const float**)d_A_array, K, &beta,
                                    d_C_array, N, batch_count));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification ---
    matrix_mult_cpu(h_A.data(), h_B.data(), h_C_ref.data(), M, N, K);
    for (int i = 0; i < batch_count; ++i) {
        CUDA_CHECK(cudaMemcpy(h_C.data(), d_C_batches[i], bytes_C, cudaMemcpyDeviceToHost));
        for (int j = 0; j < M * N; ++j) {
            ASSERT_NEAR(h_C[j], h_C_ref[j], 1e-3);
        }
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
    for (int i = 0; i < batch_count; ++i) {
        CUDA_CHECK(cudaFree(d_A_batches[i]));
        CUDA_CHECK(cudaFree(d_B_batches[i]));
        CUDA_CHECK(cudaFree(d_C_batches[i]));
    }
    CUDA_CHECK(cudaFree(d_A_array));
    CUDA_CHECK(cudaFree(d_B_array));
    CUDA_CHECK(cudaFree(d_C_array));
}

// Test capturing cuBLAS calls on multiple streams with dependencies
TEST_F(CublasGraphCaptureTest, CaptureCublasOnMultiStream) {
    cudaStream_t stream2;
    cudaEvent_t event;
    cublasHandle_t handle2;
    float *d_E; // E = C * B

    CUDA_CHECK(cudaStreamCreate(&stream2));
    CUDA_CHECK(cudaEventCreate(&event));
    CUBLAS_CHECK(cublasCreate(&handle2));
    CUBLAS_CHECK(cublasSetStream(handle2, stream2));
    CUDA_CHECK(cudaMalloc(&d_E, bytes_C));

    const float alpha = 1.0f, beta = 0.0f;
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    // Op 1 on stream 1: C = A * B
    CUBLAS_CHECK(cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_N, N, M, K, &alpha, d_B, N, d_A, K, &beta, d_C, N));
    
    // Record event on stream 1
    CUDA_CHECK(cudaEventRecord(event, stream));
    // Stream 2 waits for stream 1
    CUDA_CHECK(cudaStreamWaitEvent(stream2, event, 0));

    // Op 2 on stream 2: E = C * B
    CUBLAS_CHECK(cublasSgemm(handle2, CUBLAS_OP_N, CUBLAS_OP_N, N, M, N, &alpha, d_B, N, d_C, N, &beta, d_E, N));
    
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaStreamSynchronize(stream2));

    // --- Verification ---
    std::vector<float> h_E(M * N);
    CUDA_CHECK(cudaMemcpy(h_E.data(), d_E, bytes_C, cudaMemcpyDeviceToHost));

    matrix_mult_cpu(h_A.data(), h_B.data(), h_C_ref.data(), M, N, K); // C = A * B
    std::vector<float> h_E_ref(M*N);
    matrix_mult_cpu(h_C_ref.data(), h_B.data(), h_E_ref.data(), M, N, N); // E = C * B

    for (int i = 0; i < M * N; ++i) {
        ASSERT_NEAR(h_E[i], h_E_ref[i], 1e-1);
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
    CUDA_CHECK(cudaFree(d_E));
    CUBLAS_CHECK(cublasDestroy(handle2));
    CUDA_CHECK(cudaEventDestroy(event));
    CUDA_CHECK(cudaStreamDestroy(stream2));
}

// Test that capture fails correctly with invalid arguments to cuBLAS
TEST_F(CublasGraphCaptureTest, CaptureCublasWithInvalidArgsFails) {
    cudaGraph_t graph = nullptr;
    const float alpha = 1.0f, beta = 0.0f;

    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));

    // Invalid call: M, N, K for d_A, d_B don't match leading dimensions
    cublasStatus_t status = cublasSgemm(cublas_handle, CUBLAS_OP_N, CUBLAS_OP_N,
                                        N + 1, M, K, // Invalid N
                                        &alpha, d_B, N, d_A, K, &beta, d_C, N);
    ASSERT_EQ(status, CUBLAS_STATUS_INVALID_VALUE);

    // End capture, which should fail because the stream is in an error state
    cudaError_t end_err = cudaStreamEndCapture(stream, &graph);
    ASSERT_NE(end_err, cudaSuccess);
    ASSERT_EQ(graph, nullptr);

    // Cleanup error state
    (void)cudaGetLastError();
    (void)cublasGetStatusString(CUBLAS_STATUS_SUCCESS);
}
