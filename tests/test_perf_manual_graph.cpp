#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <chrono>
#include <vector>
#include <iostream>

// A simple kernel for performance testing
__global__ void SimpleAdd(float* a, const float* b, const float* c, size_t n) {
    size_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        a[idx] = b[idx] + c[idx];
    }
}

// A second kernel to make the graph more complex
__global__ void SimpleMultiply(float* a, const float* b, float factor, size_t n) {
    size_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        a[idx] = b[idx] * factor;
    }
}

class ManualGraphPerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        cudaMalloc(&d_a, size_bytes);
        cudaMalloc(&d_b, size_bytes);
        cudaMalloc(&d_c, size_bytes);
    }

    void TearDown() override {
        cudaFree(d_a);
        cudaFree(d_b);
        cudaFree(d_c);
    }

    const size_t N = 1024;
    const size_t size_bytes = N * sizeof(float);
    float *d_a = nullptr, *d_b = nullptr, *d_c = nullptr;
    const int iterations = 100;
    const int test_loops = 50;
};

// --- Manual Graph Construction Performance Tests ---

TEST_F(ManualGraphPerformanceTest, ManySmallKernelsManual) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);

    // Allocate enough buffers for all kernel nodes in the graph
    std::vector<float*> d_a_vec(iterations), d_b_vec(iterations), d_c_vec(iterations);
    std::vector<std::vector<void*>> kernel_args_vec(iterations);

    for(int i = 0; i < iterations; ++i) {
        cudaMalloc(&d_a_vec[i], size_bytes);
        cudaMalloc(&d_b_vec[i], size_bytes);
        cudaMalloc(&d_c_vec[i], size_bytes);
        
        std::vector<float> h_b(N, 2.0f * (i + 1)), h_c(N, 3.0f * (i + 1));
        cudaMemcpy(d_b_vec[i], h_b.data(), size_bytes, cudaMemcpyHostToDevice);
        cudaMemcpy(d_c_vec[i], h_c.data(), size_bytes, cudaMemcpyHostToDevice);

        // Store pointers for kernel arguments
        kernel_args_vec[i] = {&d_a_vec[i], &d_b_vec[i], &d_c_vec[i], (void*)&N};
    }

    dim3 block(256, 1, 1);
    dim3 grid((N + block.x - 1) / block.x, 1, 1);
    
    // --- 1. Without CUDA Graph ---
    auto start_no_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        for (int j = 0; j < iterations; ++j) {
            cudaLaunchKernel((void*)SimpleAdd, grid, block, kernel_args_vec[j].data(), 0, stream);
        }
    }
    cudaStreamSynchronize(stream);
    auto end_no_graph = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> no_graph_duration = end_no_graph - start_no_graph;

    // --- 2. With CUDA Graph (Manual, Static) ---
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    
    for (int i = 0; i < iterations; ++i) {
        cudaKernelNodeParams kernelParams = {};
        kernelParams.func = (void*)SimpleAdd;
        kernelParams.gridDim = grid;
        kernelParams.blockDim = block;
        kernelParams.args = kernel_args_vec[i].data();
        cudaGraphAddKernelNode(nullptr, graph, nullptr, 0, &kernelParams);
    }
    
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    auto start_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        cudaGraphLaunch(exec, stream);
    }
    cudaStreamSynchronize(stream);
    auto end_graph = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> graph_duration = end_graph - start_graph;

    // Verify results from the graph execution
    std::vector<float> h_a_result(N);
    for(int i = 0; i < iterations; ++i) {
        cudaMemcpy(h_a_result.data(), d_a_vec[i], size_bytes, cudaMemcpyDeviceToHost);
        float expected_val = 2.0f * (i + 1) + 3.0f * (i + 1);
        for(size_t j = 0; j < N; ++j) {
            ASSERT_FLOAT_EQ(h_a_result[j], expected_val);
        }
    }

    std::cout << "[   INFO   ] ManySmallKernels (Static Graph vs. No Graph):" << std::endl;
    std::cout << "[   INFO   ]   Without Graph: " << no_graph_duration.count() / test_loops << " ms per loop" << std::endl;
    std::cout << "[   INFO   ]   With Graph:    " << graph_duration.count() / test_loops << " ms per loop" << std::endl;

    ASSERT_LT(graph_duration.count(), no_graph_duration.count());

    for(int i = 0; i < iterations; ++i) {
        cudaFree(d_a_vec[i]);
        cudaFree(d_b_vec[i]);
        cudaFree(d_c_vec[i]);
    }
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
    cudaStreamDestroy(stream);
}

TEST_F(ManualGraphPerformanceTest, DependentKernelAndMemcpyManual) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);

    float* h_a = new float[N];
    float* h_b = new float[N];
    for(size_t i = 0; i < N; ++i) h_b[i] = (float)i;
    
    dim3 grid((N + 255) / 256, 1, 1);
    dim3 block(256, 1, 1);
    float* d_a_ptr = d_a, *d_b_ptr = d_b, *d_c_ptr = d_c;
    size_t n_val = N;
    void* kernelArgs[] = {&d_a_ptr, &d_b_ptr, &d_c_ptr, &n_val};

    // --- 1. Without CUDA Graph ---
    auto start_no_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        cudaMemcpyAsync(d_b, h_b, size_bytes, cudaMemcpyHostToDevice, stream);
        cudaLaunchKernel((void*)SimpleAdd, grid, block, kernelArgs, 0, stream);
        cudaMemcpyAsync(h_a, d_a, size_bytes, cudaMemcpyDeviceToHost, stream);
    }
    cudaStreamSynchronize(stream);
    auto end_no_graph = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> no_graph_duration = end_no_graph - start_no_graph;

    // --- 2. With CUDA Graph (Manual) ---
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaGraphNode_t memcpyH2DNode, kernelNode, memcpyD2HNode;
    cudaGraphAddMemcpyNode1D(&memcpyH2DNode, graph, nullptr, 0, d_b, h_b, size_bytes, cudaMemcpyHostToDevice);
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)SimpleAdd;
    kernelParams.gridDim = grid;
    kernelParams.blockDim = block;
    kernelParams.args = kernelArgs;
    cudaGraphAddKernelNode(&kernelNode, graph, &memcpyH2DNode, 1, &kernelParams);
    cudaGraphAddMemcpyNode1D(&memcpyD2HNode, graph, &kernelNode, 1, h_a, d_a, size_bytes, cudaMemcpyDeviceToHost);
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    auto start_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        cudaGraphLaunch(exec, stream);
    }
    cudaStreamSynchronize(stream);
    auto end_graph = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> graph_duration = end_graph - start_graph;

    std::cout << "[   INFO   ] DependentKernelAndMemcpy (Manual vs. No Graph):" << std::endl;
    std::cout << "[   INFO   ]   Without Graph: " << no_graph_duration.count() / test_loops << " ms per loop" << std::endl;
    std::cout << "[   INFO   ]   With Graph:    " << graph_duration.count() / test_loops << " ms per loop" << std::endl;

    // Verify the result from the graph execution
    for(size_t i = 0; i < N; ++i) {
        // Assuming d_c was initialized to 0, the result should be h_b + 0
        ASSERT_FLOAT_EQ(h_a[i], (float)i);
    }

    ASSERT_LT(graph_duration.count(), no_graph_duration.count());

    delete[] h_a;
    delete[] h_b;
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
    cudaStreamDestroy(stream);
}

TEST_F(ManualGraphPerformanceTest, UpdatableGraphManual) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);

    // --- Setup for a more complex graph ---
    float *d_intermediate, *d_final_output;
    cudaMalloc(&d_intermediate, size_bytes);
    cudaMalloc(&d_final_output, size_bytes);

    std::vector<float> h_input1(N, 1.0f), h_input2(N, 2.0f);
    std::vector<float> h_output(N, 0.0f);

    float *d_input1, *d_input2;
    cudaMalloc(&d_input1, size_bytes);
    cudaMalloc(&d_input2, size_bytes);
    cudaMemcpy(d_input1, h_input1.data(), size_bytes, cudaMemcpyHostToDevice);
    cudaMemcpy(d_input2, h_input2.data(), size_bytes, cudaMemcpyHostToDevice);

    dim3 grid((N + 255) / 256, 1, 1);
    dim3 block(256, 1, 1);
    size_t n_val = N;

    // --- 1. Without Graph Update (Re-creating the graph each time) ---
    auto start_reinstantiate = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        float factor = (i % 2 == 0) ? 10.0f : 20.0f;
        
        cudaGraph_t temp_graph;
        cudaGraphCreate(&temp_graph, 0);
        
        void* add_args[] = {&d_intermediate, &d_input1, &d_input2, &n_val};
        cudaKernelNodeParams add_params = { (void*)SimpleAdd, grid, block, 0, add_args, nullptr };
        cudaGraphNode_t add_node;
        cudaGraphAddKernelNode(&add_node, temp_graph, nullptr, 0, &add_params);

        void* mul_args[] = {&d_final_output, &d_intermediate, &factor, &n_val};
        cudaKernelNodeParams mul_params = { (void*)SimpleMultiply, grid, block, 0, mul_args, nullptr };
        cudaGraphNode_t mul_node;
        cudaGraphAddKernelNode(&mul_node, temp_graph, &add_node, 1, &mul_params);

        cudaGraphExec_t temp_exec;
        cudaGraphInstantiate(&temp_exec, temp_graph, nullptr, nullptr, 0);
        cudaGraphLaunch(temp_exec, stream);
        
        cudaGraphExecDestroy(temp_exec);
        cudaGraphDestroy(temp_graph);
    }
    cudaStreamSynchronize(stream);
    auto end_reinstantiate = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> reinstantiate_duration = end_reinstantiate - start_reinstantiate;

    // --- 2. With Graph Update ---
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    
    void* add_args_tpl[] = {&d_intermediate, &d_input1, &d_input2, &n_val};
    cudaKernelNodeParams add_params_tpl = { (void*)SimpleAdd, grid, block, 0, add_args_tpl, nullptr };
    cudaGraphNode_t add_node_tpl;
    cudaGraphAddKernelNode(&add_node_tpl, graph, nullptr, 0, &add_params_tpl);

    float factor_for_update = 10.0f; // This will be updated in the loop
    void* mul_args_tpl[] = {&d_final_output, &d_intermediate, &factor_for_update, &n_val};
    cudaKernelNodeParams mul_params_tpl = { (void*)SimpleMultiply, grid, block, 0, mul_args_tpl, nullptr };
    cudaGraphNode_t mul_node_tpl;
    cudaGraphAddKernelNode(&mul_node_tpl, graph, &add_node_tpl, 1, &mul_params_tpl);

    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    auto start_update = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        factor_for_update = (i % 2 == 0) ? 10.0f : 20.0f;
        cudaGraphExecKernelNodeSetParams(exec, mul_node_tpl, &mul_params_tpl);
        cudaGraphLaunch(exec, stream);
    }
    cudaStreamSynchronize(stream);
    auto end_update = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> update_duration = end_update - start_update;

    std::cout << "[   INFO   ] ComplexUpdatableGraph (Re-instantiate vs. Update):" << std::endl;
    std::cout << "[   INFO   ]   Re-instantiating: " << reinstantiate_duration.count() / test_loops << " ms per loop" << std::endl;
    std::cout << "[   INFO   ]   Using Update:     " << update_duration.count() / test_loops << " ms per loop" << std::endl;

    ASSERT_LT(update_duration.count(), reinstantiate_duration.count());

    // Verification
    cudaMemcpy(h_output.data(), d_final_output, size_bytes, cudaMemcpyDeviceToHost);
    float final_factor = (test_loops - 1) % 2 == 0 ? 10.0f : 20.0f;
    for(size_t i = 0; i < N; ++i) {
        ASSERT_FLOAT_EQ(h_output[i], (h_input1[i] + h_input2[i]) * final_factor);
    }

    cudaFree(d_intermediate);
    cudaFree(d_final_output);
    cudaFree(d_input1);
    cudaFree(d_input2);
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
    cudaStreamDestroy(stream);
}
