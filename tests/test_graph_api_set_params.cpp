#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <iostream>
#include <thread>

#define CUDA_CHECK(err)                                         \
    do {                                                        \
        cudaError_t err_ = (err);                               \
        if (err_ != cudaSuccess) {                              \
            std::cerr << "CUDA error: " << cudaGetErrorString(err_) \
                      << " at " << __FILE__ << ":" << __LINE__ << std::endl; \
            FAIL();                                             \
        }                                                       \
    } while (0)

// --- Kernels for Testing ---
__global__ void set_value_kernel_graph_api(int* data, int value) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        *data = value;
    }
}

__global__ void another_kernel_graph_api() {}

__global__ void atomic_increment_kernel_api(int* counter) {
    atomicAdd(counter, 1);
}

// --- Symbol for From/To Symbol tests ---
__device__ __constant__ int g_test_symbol_graph_api;

// --- Test Fixture ---
class GraphApiSetParamsTest : public ::testing::Test {
protected:
    cudaGraph_t graph_{nullptr};
    int* d_data_{nullptr};
    int* h_data_{nullptr};

    void SetUp() override {
        CUDA_CHECK(cudaDeviceSynchronize()); 
        h_data_ = new int;
        CUDA_CHECK(cudaMalloc(&d_data_, sizeof(int)));
        CUDA_CHECK(cudaGraphCreate(&graph_, 0));
    }

    void TearDown() override {
        if (graph_) CUDA_CHECK(cudaGraphDestroy(graph_));
        if (d_data_) CUDA_CHECK(cudaFree(d_data_));
        if (h_data_) { delete h_data_; h_data_ = nullptr; }
    }
};

// =================================================================
// ==      cudaGraph...SetParams FUNCTIONAL TESTS                 ==
// =================================================================

TEST_F(GraphApiSetParamsTest, GraphKernelNodeSetParams) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {};
    params.func = (void*)set_value_kernel_graph_api;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph_, nullptr, 0, &params));
    
    int val = 789;
    void* args[] = {&d_data_, &val};
    params.kernelParams = args;
    // Set params on the graph object directly
    CUDA_CHECK(cudaGraphKernelNodeSetParams(node, &params));

    // Instantiate and run to verify
    cudaGraphExec_t exec;
    cudaStream_t stream;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaMemcpy(h_data_, d_data_, sizeof(int), cudaMemcpyDeviceToHost));
    
    ASSERT_EQ(*h_data_, val);

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
}

TEST_F(GraphApiSetParamsTest, GraphKernelNodeSetParamsTwice) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph_, nullptr, 0, &params));
    
    int val1 = 1, val2 = 2;
    void* args1[] = {&d_data_, &val1};
    void* args2[] = {&d_data_, &val2};

    // First update
    params.kernelParams = args1;
    CUDA_CHECK(cudaGraphKernelNodeSetParams(node, &params));
    // Second update, should overwrite the first
    params.kernelParams = args2;
    CUDA_CHECK(cudaGraphKernelNodeSetParams(node, &params));
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    int h_res;
    CUDA_CHECK(cudaMemcpy(&h_res, d_data_, sizeof(int), cudaMemcpyDeviceToHost));
    ASSERT_EQ(h_res, val2); // The second update should be the one that took effect

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
}

TEST_F(GraphApiSetParamsTest, GraphKernelNodeSetParamsAllFields) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph_, nullptr, 0, &params));
    
    // Update all updatable fields
    params.gridDim = {2,1,1};
    params.blockDim = {128,1,1};
    params.sharedMemBytes = 1024;
    CUDA_CHECK(cudaGraphKernelNodeSetParams(node, &params));

    // To verify, we need to get the params back from the node
    cudaKernelNodeParams fetched_params;
    CUDA_CHECK(cudaGraphKernelNodeGetParams(node, &fetched_params));

    ASSERT_EQ(fetched_params.gridDim.x, 2);
    ASSERT_EQ(fetched_params.blockDim.x, 128);
    ASSERT_EQ(fetched_params.sharedMemBytes, 1024);
}

TEST_F(GraphApiSetParamsTest, UpdateNodeInDependencyChain) {
    cudaGraphNode_t node_a, node_b;
    
    // Create node A
    int val_a = 10;
    void* args_a[] = {&d_data_, &val_a};
    cudaKernelNodeParams params_a = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, args_a};
    CUDA_CHECK(cudaGraphAddKernelNode(&node_a, graph_, nullptr, 0, &params_a));

    // Create node B, which depends on A
    cudaKernelNodeParams params_b = {(void*)another_kernel_graph_api, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node_b, graph_, &node_a, 1, &params_b));

    // Update node A
    int new_val_a = 20;
    void* new_args_a[] = {&d_data_, &new_val_a};
    params_a.kernelParams = new_args_a;
    CUDA_CHECK(cudaGraphKernelNodeSetParams(node_a, &params_a));

    // Instantiate and run
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Verify that the updated value from node A was used.
    int h_res;
    CUDA_CHECK(cudaMemcpy(&h_res, d_data_, sizeof(int), cudaMemcpyDeviceToHost));
    ASSERT_EQ(h_res, new_val_a);

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
}

TEST_F(GraphApiSetParamsTest, MultiDeviceSetParams) {
    int device_count = 0;
    CUDA_CHECK(cudaGetDeviceCount(&device_count));
    if (device_count < 2) {
        GTEST_SKIP() << "Skipping Multi-GPU test, requires at least 2 devices.";
    }

    int *d_data0, *d_data1;
    cudaGraphNode_t node0, node1;

    // Setup on Device 0
    CUDA_CHECK(cudaSetDevice(0));
    CUDA_CHECK(cudaMalloc(&d_data0, sizeof(int)));
    cudaKernelNodeParams params0 = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node0, graph_, nullptr, 0, &params0));

    // Setup on Device 1
    CUDA_CHECK(cudaSetDevice(1));
    CUDA_CHECK(cudaMalloc(&d_data1, sizeof(int)));
    cudaKernelNodeParams params1 = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node1, graph_, nullptr, 0, &params1));
    
    // Set params from host thread (should work regardless of current device)
    CUDA_CHECK(cudaSetDevice(0));
    int val0 = 111, val1 = 222;
    void* args0[] = {&d_data0, &val0};
    void* args1[] = {&d_data1, &val1};
    params0.kernelParams = args0;
    params1.kernelParams = args1;
    CUDA_CHECK(cudaGraphKernelNodeSetParams(node0, &params0));
    CUDA_CHECK(cudaGraphKernelNodeSetParams(node1, &params1));

    // Instantiate and launch
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Verify results on both devices
    int h_res0, h_res1;
    CUDA_CHECK(cudaMemcpy(&h_res0, d_data0, sizeof(int), cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaSetDevice(1));
    CUDA_CHECK(cudaMemcpy(&h_res1, d_data1, sizeof(int), cudaMemcpyDeviceToHost));

    ASSERT_EQ(h_res0, val0);
    ASSERT_EQ(h_res1, val1);

    // Cleanup
    CUDA_CHECK(cudaSetDevice(0));
    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaFree(d_data0));
    CUDA_CHECK(cudaSetDevice(1));
    CUDA_CHECK(cudaFree(d_data1));
    CUDA_CHECK(cudaSetDevice(0));
}

TEST_F(GraphApiSetParamsTest, GraphMemcpyNodeSetParams) {
    int *d_src1, *d_dst1, *d_src2, *d_dst2;
    int h_val1 = 11, h_val2 = 22;
    size_t size = sizeof(int);
    CUDA_CHECK(cudaMalloc(&d_src1, size));
    CUDA_CHECK(cudaMalloc(&d_dst1, size));
    CUDA_CHECK(cudaMalloc(&d_src2, size));
    CUDA_CHECK(cudaMalloc(&d_dst2, size));

    CUDA_CHECK(cudaMemcpy(d_src1, &h_val1, size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_src2, &h_val2, size, cudaMemcpyHostToDevice));

    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&node, graph_, nullptr, 0, d_dst1, d_src1, size, cudaMemcpyDeviceToDevice));
    
    // Update params on the graph node
    CUDA_CHECK(cudaGraphMemcpyNodeSetParams1D(node, d_dst2, d_src2, size, cudaMemcpyDeviceToDevice));
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    int h_res1 = 0, h_res2 = 0;
    CUDA_CHECK(cudaMemcpy(&h_res1, d_dst1, size, cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(&h_res2, d_dst2, size, cudaMemcpyDeviceToHost));
    
    ASSERT_EQ(h_res1, 0); // Original destination should be untouched
    ASSERT_EQ(h_res2, h_val2); // New destination should have the value

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaFree(d_src1)); CUDA_CHECK(cudaFree(d_dst1));
    CUDA_CHECK(cudaFree(d_src2)); CUDA_CHECK(cudaFree(d_dst2));
}

void CUDART_CB host_callback_1(void *data) { *(static_cast<int*>(data)) = 1; }
void CUDART_CB host_callback_2(void *data) { *(static_cast<int*>(data)) = 2; }

TEST_F(GraphApiSetParamsTest, GraphHostNodeSetParams) {
    cudaGraphNode_t node;
    int host_data = 0;
    cudaHostNodeParams params = {host_callback_1, &host_data};
    CUDA_CHECK(cudaGraphAddHostNode(&node, graph_, nullptr, 0, &params));
    
    // Update params on the graph node
    params.fn = host_callback_2; // Update function pointer
    CUDA_CHECK(cudaGraphHostNodeSetParams(node, &params));
    
    cudaGraphExec_t exec;
    cudaStream_t stream;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    ASSERT_EQ(host_data, 2); // Should be updated by the second callback

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
}

TEST_F(GraphApiSetParamsTest, GraphMemcpyToSymbolSetParams) {
    int *d_src1, *d_src2;
    int h_val1 = 111, h_val2 = 222;
    size_t size = sizeof(int);
    CUDA_CHECK(cudaMalloc(&d_src1, size));
    CUDA_CHECK(cudaMalloc(&d_src2, size));
    CUDA_CHECK(cudaMemcpy(d_src1, &h_val1, size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_src2, &h_val2, size, cudaMemcpyHostToDevice));

    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddMemcpyNodeToSymbol(&node, graph_, nullptr, 0, g_test_symbol_graph_api, d_src1, size, 0, cudaMemcpyDeviceToDevice));
    
    // Update params on the graph node to copy from d_src2 instead
    CUDA_CHECK(cudaGraphMemcpyNodeSetParamsToSymbol(node, g_test_symbol_graph_api, d_src2, size, 0, cudaMemcpyDeviceToDevice));
    
    cudaGraphExec_t exec;
    cudaStream_t stream;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    int h_res = 0;
    CUDA_CHECK(cudaMemcpyFromSymbol(&h_res, g_test_symbol_graph_api, size, 0, cudaMemcpyDeviceToHost));
    ASSERT_EQ(h_res, h_val2);

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaFree(d_src1));
    CUDA_CHECK(cudaFree(d_src2));
}

TEST_F(GraphApiSetParamsTest, GraphMemcpyFromSymbolSetParams) {
    int *d_dst1, *d_dst2;
    int h_symbol_val = 333;
    size_t size = sizeof(int);
    CUDA_CHECK(cudaMalloc(&d_dst1, size));
    CUDA_CHECK(cudaMalloc(&d_dst2, size));
    CUDA_CHECK(cudaMemcpyToSymbol(g_test_symbol_graph_api, &h_symbol_val, size, 0, cudaMemcpyHostToDevice));

    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddMemcpyNodeFromSymbol(&node, graph_, nullptr, 0, d_dst1, g_test_symbol_graph_api, size, 0, cudaMemcpyDeviceToDevice));

    // Update params on the graph node to copy to d_dst2 instead
    CUDA_CHECK(cudaGraphMemcpyNodeSetParamsFromSymbol(node, d_dst2, g_test_symbol_graph_api, size, 0, cudaMemcpyDeviceToDevice));

    cudaGraphExec_t exec;
    cudaStream_t stream;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    int h_res1 = 0, h_res2 = 0;
    CUDA_CHECK(cudaMemcpy(&h_res1, d_dst1, size, cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(&h_res2, d_dst2, size, cudaMemcpyDeviceToHost));

    ASSERT_EQ(h_res1, 0); // Original destination should be untouched
    ASSERT_EQ(h_res2, h_symbol_val);

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaFree(d_dst1));
    CUDA_CHECK(cudaFree(d_dst2));
}

TEST_F(GraphApiSetParamsTest, GraphMemsetNodeSetParams) {
    int *d_ptr1, *d_ptr2;
    const unsigned char val1 = 0xAA;
    const unsigned char val2 = 0xBB;
    const size_t size = sizeof(int);
    CUDA_CHECK(cudaMalloc(&d_ptr1, size));
    CUDA_CHECK(cudaMalloc(&d_ptr2, size));

    cudaGraphNode_t node;
    cudaMemsetParams params = {};
    params.dst = d_ptr1;
    params.value = val1;
    params.elementSize = 1;
    params.width = size;
    params.height = 1;
    CUDA_CHECK(cudaGraphAddMemsetNode(&node, graph_, nullptr, 0, &params));
    
    // Update params on the graph node to use a different destination and value
    params.dst = d_ptr2;
    params.value = val2;
    CUDA_CHECK(cudaGraphMemsetNodeSetParams(node, &params));
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    int h_res1 = 0, h_res2 = 0;
    int expected_res2 = 0;
    memset(&expected_res2, val2, size);
    CUDA_CHECK(cudaMemcpy(&h_res1, d_ptr1, size, cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(&h_res2, d_ptr2, size, cudaMemcpyDeviceToHost));
    
    ASSERT_EQ(h_res1, 0); // Original destination untouched
    ASSERT_EQ(h_res2, expected_res2);

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaFree(d_ptr1));
    CUDA_CHECK(cudaFree(d_ptr2));
}

TEST_F(GraphApiSetParamsTest, GraphChildGraphNodeSetParams) {
    // Create two possible child graphs
    cudaGraph_t child1, child2;
    CUDA_CHECK(cudaGraphCreate(&child1, 0));
    CUDA_CHECK(cudaGraphCreate(&child2, 0));
    
    int val1 = 1, val2 = 2;
    void* args1[] = {&d_data_, &val1};
    void* args2[] = {&d_data_, &val2};
    cudaKernelNodeParams p1 = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, args1};
    cudaKernelNodeParams p2 = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, args2};
    CUDA_CHECK(cudaGraphAddKernelNode(nullptr, child1, nullptr, 0, &p1));
    CUDA_CHECK(cudaGraphAddKernelNode(nullptr, child2, nullptr, 0, &p2));

    // Add child1 to the main graph initially
    cudaGraphNode_t child_node;
    CUDA_CHECK(cudaGraphAddChildGraphNode(&child_node, graph_, nullptr, 0, child1));

    // Before instantiating, update the node to use child2 instead
    CUDA_CHECK(cudaGraphChildGraphNodeSetParams(child_node, child2));

    cudaGraphExec_t exec;
    cudaStream_t stream;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    int h_res = 0;
    CUDA_CHECK(cudaMemcpy(&h_res, d_data_, sizeof(int), cudaMemcpyDeviceToHost));
    ASSERT_EQ(h_res, val2); // The second child graph should have executed

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(child1));
    CUDA_CHECK(cudaGraphDestroy(child2));
}

TEST_F(GraphApiSetParamsTest, GraphEventWaitNodeSetEvent) {
    cudaEvent_t event1, event2;
    CUDA_CHECK(cudaEventCreate(&event1));
    CUDA_CHECK(cudaEventCreate(&event2));

    cudaGraphNode_t wait_node;
    CUDA_CHECK(cudaGraphAddEventWaitNode(&wait_node, graph_, nullptr, 0, event1));
    
    // Update the event on the graph object
    CUDA_CHECK(cudaGraphEventWaitNodeSetEvent(wait_node, event2));
    
    cudaGraphNode_t kernel_node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}};
    CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph_, &wait_node, 1, &params));

    cudaGraphExec_t exec;
    cudaStream_t stream;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    CUDA_CHECK(cudaStreamCreate(&stream));
    
    // Launching the graph should block until event2 is recorded
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    // Recording event1 should NOT unblock the graph
    CUDA_CHECK(cudaEventRecord(event1, stream)); 
    // Now record event2, which should unblock it
    CUDA_CHECK(cudaEventRecord(event2, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaEventDestroy(event1));
    CUDA_CHECK(cudaEventDestroy(event2));
    SUCCEED();
}

TEST_F(GraphApiSetParamsTest, GraphEventRecordNodeSetEvent) {
    cudaEvent_t event1, event2;
    CUDA_CHECK(cudaEventCreate(&event1));
    CUDA_CHECK(cudaEventCreate(&event2));

    cudaGraphNode_t record_node;
    CUDA_CHECK(cudaGraphAddEventRecordNode(&record_node, graph_, nullptr, 0, event1));
    
    // Update the event on the graph object
    CUDA_CHECK(cudaGraphEventRecordNodeSetEvent(record_node, event2));

    cudaGraphExec_t exec;
    cudaStream_t stream;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph_, nullptr, nullptr, 0));
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    // After launch, event2 should be recorded, but event1 should not be.
    ASSERT_EQ(cudaEventQuery(event2), cudaSuccess);
    ASSERT_NE(cudaEventQuery(event1), cudaSuccess);

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaEventDestroy(event1));
    CUDA_CHECK(cudaEventDestroy(event2));
}

// =================================================================
// ==        cudaGraph...SetParams ERROR TESTS                  ==
// =================================================================

TEST_F(GraphApiSetParamsTest, GraphKernelNodeSetParamsWithNullNode) {
    cudaKernelNodeParams params = {};
    cudaGraphNode_t null_node = {}; // A zero-initialized node is invalid
    ASSERT_NE(cudaGraphKernelNodeSetParams(null_node, &params), cudaSuccess);
}

TEST_F(GraphApiSetParamsTest, GraphKernelNodeSetParamsWithNullParams) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph_, nullptr, 0, &params));

    // Calling with a null pointer for the params struct should fail.
    ASSERT_NE(cudaGraphKernelNodeSetParams(node, nullptr), cudaSuccess);
}

TEST_F(GraphApiSetParamsTest, GraphNodeSetParamsOnDestroyedGraph) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph_, nullptr, 0, &params));
    
    // Destroy the graph that the node belongs to
    CUDA_CHECK(cudaGraphDestroy(graph_));
    graph_ = nullptr; // Prevent TearDown from trying to double-destroy

    // Trying to set params on a node from a destroyed graph should fail.
    ASSERT_NE(cudaGraphKernelNodeSetParams(node, &params), cudaSuccess);
}

TEST_F(GraphApiSetParamsTest, SetParamsOnWrongNodeType) {
    cudaGraphNode_t memcpy_node;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpy_node, graph_, nullptr, 0, d_data_, d_data_, sizeof(int), cudaMemcpyDeviceToDevice));
    
    // Attempt to set Kernel node params on a Memcpy node
    cudaKernelNodeParams kernel_params = {};
    ASSERT_NE(cudaGraphKernelNodeSetParams(memcpy_node, &kernel_params), cudaSuccess);

    cudaGraphNode_t kernel_node;
    CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph_, nullptr, 0, &kernel_params));

    // Attempt to set Memset node params on a Kernel node
    cudaMemsetParams memset_params = {};
    ASSERT_NE(cudaGraphMemsetNodeSetParams(kernel_node, &memset_params), cudaSuccess);
}

TEST_F(GraphApiSetParamsTest, GraphEventNodeSetParamsWithNullEvent) {
    cudaGraphNode_t wait_node;
    // Create a wait node with a valid event first.
    cudaEvent_t valid_event;
    CUDA_CHECK(cudaEventCreate(&valid_event));
    CUDA_CHECK(cudaGraphAddEventWaitNode(&wait_node, graph_, nullptr, 0, valid_event));
    // Setting a null event should be an error
    ASSERT_NE(cudaGraphEventWaitNodeSetEvent(wait_node, nullptr), cudaSuccess);
    CUDA_CHECK(cudaEventDestroy(valid_event));
}

TEST_F(GraphApiSetParamsTest, GraphChildGraphNodeSetParamsWithNullGraph) {
    cudaGraphNode_t child_node;
    // Create a child node with a valid graph first.
    cudaGraph_t valid_child_graph;
    CUDA_CHECK(cudaGraphCreate(&valid_child_graph, 0));
    CUDA_CHECK(cudaGraphAddChildGraphNode(&child_node, graph_, nullptr, 0, valid_child_graph));
    // Setting a null graph should be an error
    ASSERT_NE(cudaGraphChildGraphNodeSetParams(child_node, nullptr), cudaSuccess);
    CUDA_CHECK(cudaGraphDestroy(valid_child_graph));
}

TEST_F(GraphApiSetParamsTest, GraphHostNodeSetParamsWithNullFn) {
    cudaGraphNode_t host_node;
    CUDA_CHECK(cudaGraphAddHostNode(&host_node, graph_, nullptr, 0, {host_callback_1, nullptr}));
    
    cudaHostNodeParams params = {nullptr, nullptr};
    // Setting a null function pointer should be an error
    ASSERT_NE(cudaGraphHostNodeSetParams(host_node, &params), cudaSuccess);
}


// =================================================================
// ==       cudaGraph...SetParams STRESS TESTS                  ==
// =================================================================

enum class GraphApiNodeType { KERNEL, MEMCPY, MEMSET };
using GraphApiStressParams = std::tuple<GraphApiNodeType, int, int>; // node_type, num_nodes, update_iterations

class GraphApiSetParamsStressTest : public ::testing::TestWithParam<GraphApiStressParams> {};

TEST_P(GraphApiSetParamsStressTest, RepetitiveSetParamsAndInstantiate) {
    auto p = GetParam();
    GraphApiNodeType node_type = std::get<0>(p);
    int num_nodes = std::get<1>(p);
    int update_iterations = std::get<2>(p);
    
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    std::vector<cudaGraphNode_t> nodes(num_nodes);

    // Create graph structure
    if (node_type == GraphApiNodeType::KERNEL) {
        for(int i=0; i<num_nodes; ++i) CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, nullptr, 0, {}));
    } else if (node_type == GraphApiNodeType::MEMCPY) {
        for(int i=0; i<num_nodes; ++i) CUDA_CHECK(cudaGraphAddMemcpyNode1D(&nodes[i], graph, nullptr, 0, nullptr, nullptr, 0, cudaMemcpyDefault));
    } else if (node_type == GraphApiNodeType::MEMSET) {
        for(int i=0; i<num_nodes; ++i) CUDA_CHECK(cudaGraphAddMemsetNode(&nodes[i], graph, nullptr, 0, {}));
    }
    
    for (int iter = 0; iter < update_iterations; ++iter) {
        // Set params for all nodes
        if (node_type == GraphApiNodeType::KERNEL) {
            cudaKernelNodeParams p = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, nullptr};
            for(const auto& node : nodes) CUDA_CHECK(cudaGraphKernelNodeSetParams(node, &p));
        } else if (node_type == GraphApiNodeType::MEMCPY) {
             for(const auto& node : nodes) CUDA_CHECK(cudaGraphMemcpyNodeSetParams1D(node, d_data_, d_data_, 1, cudaMemcpyDeviceToDevice));
        } else if (node_type == GraphApiNodeType::MEMSET) {
            cudaMemsetParams p = {d_data_, (unsigned char)iter, 1, 1, 1};
            for(const auto& node : nodes) CUDA_CHECK(cudaGraphMemsetNodeSetParams(node, &p));
        }

        // Instantiate graph to apply params
        cudaGraphExec_t exec;
        CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
        CUDA_CHECK(cudaGraphExecDestroy(exec)); // Destroy immediately, we only test the process
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(GraphApiSetParamsStress, GraphApiSetParamsStressTest,
    ::testing::Combine(
        ::testing::Values(GraphApiNodeType::KERNEL, GraphApiNodeType::MEMCPY, GraphApiNodeType::MEMSET),
        ::testing::Values(32, 128),
        ::testing::Values(20, 50)
    ),
    [](const auto& info) {
        std::string name;
        switch(std::get<0>(info.param)) {
            case GraphApiNodeType::KERNEL: name = "Kernel"; break;
            case GraphApiNodeType::MEMCPY: name = "Memcpy"; break;
            case GraphApiNodeType::MEMSET: name = "Memset"; break;
        }
        return name + "_Nodes" + std::to_string(std::get<1>(info.param)) + "_Iters" + std::to_string(std::get<2>(info.param));
    }
);

// =================================================================
// ==      cudaGraph CONCURRENCY TESTS                          ==
// =================================================================

class GraphApiConcurrentSetParamsTest : public ::testing::Test {};

TEST_F(GraphApiConcurrentSetParamsTest, ConcurrentSetParamsOnSameGraph) {
    const int num_threads = 8;
    const int nodes_per_thread = 32;
    const int num_nodes = num_threads * nodes_per_thread;

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    std::vector<int*> d_data(num_nodes);

    for (int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaMalloc(&d_data[i], sizeof(int)));
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, nullptr, 0, {}));
    }

    auto updater_lambda = [&](int thread_id, int start_node) {
        for (int i = 0; i < nodes_per_thread; ++i) {
            int node_idx = start_node + i;
            int val = thread_id * 1000 + i;
            void* args[] = {(void*)&d_data[node_idx], (void*)&val};
            cudaKernelNodeParams params = {(void*)set_value_kernel_graph_api, {1,1,1}, {1,1,1}, 0, args};
            // Concurrent calls to SetParams on the same graph but different nodes
            CUDA_CHECK(cudaGraphKernelNodeSetParams(nodes[node_idx], &params));
        }
    };

    std::vector<std::thread> threads;
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(updater_lambda, i, i * nodes_per_thread);
    }
    for(auto& t : threads) {
        t.join();
    }

    // Instantiate *after* all concurrent updates are done
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Verify all updates took place
    for (int i = 0; i < num_threads; ++i) {
        for (int j = 0; j < nodes_per_thread; ++j) {
            int node_idx = i * nodes_per_thread + j;
            int expected_val = i * 1000 + j;
            int h_res;
            CUDA_CHECK(cudaMemcpy(&h_res, d_data[node_idx], sizeof(int), cudaMemcpyDeviceToHost));
            ASSERT_EQ(h_res, expected_val);
        }
    }
    
    for(int i=0; i < num_nodes; ++i) CUDA_CHECK(cudaFree(d_data[i]));
    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

class GraphApiConcurrentLaunchTest : public ::testing::Test {};

TEST_F(GraphApiConcurrentLaunchTest, ConcurrentLaunchSameExecGraph) {
    const int num_threads = 16;
    int* d_counter;
    int h_counter = 0;
    
    CUDA_CHECK(cudaMalloc(&d_counter, sizeof(int)));
    CUDA_CHECK(cudaMemcpy(d_counter, &h_counter, sizeof(int), cudaMemcpyHostToDevice));

    // Create a graph with an atomic increment kernel
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaGraphNode_t node;
    void* args[] = {&d_counter};
    cudaKernelNodeParams params = {(void*)atomic_increment_kernel_api, {1,1,1}, {1,1,1}, 0, args};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    // This lambda will be executed by each thread
    auto launcher_lambda = [&](cudaGraphExec_t e) {
        cudaStream_t stream;
        CUDA_CHECK(cudaStreamCreate(&stream));
        // Each thread launches the same executable graph on its own stream
        CUDA_CHECK(cudaGraphLaunch(e, stream));
        CUDA_CHECK(cudaStreamSynchronize(stream));
        CUDA_CHECK(cudaStreamDestroy(stream));
    };

    std::vector<std::thread> threads;
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(launcher_lambda, exec);
    }
    for (auto& t : threads) {
        t.join();
    }

    // Verify that the counter was incremented by each thread
    CUDA_CHECK(cudaMemcpy(&h_counter, d_counter, sizeof(int), cudaMemcpyDeviceToHost));
    ASSERT_EQ(h_counter, num_threads);

    // Cleanup
    CUDA_CHECK(cudaFree(d_counter));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}
