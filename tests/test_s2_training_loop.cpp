#include "common.h"
#include <vector>
#include <cfloat> // For FLT_MAX

// --- Functional Kernel Implementations ---

__global__ void matrix_multiply_kernel(const float* a, const float* b, float* c, int M, int N, int K) {
    int row = blockIdx.y * blockDim.y + threadIdx.y;
    int col = blockIdx.x * blockDim.x + threadIdx.x;

    if (row < M && col < N) {
        float val = 0.0f;
        for (int i = 0; i < K; ++i) {
            val += a[row * K + i] * b[i * N + col];
        }
        c[row * N + col] = val;
    }
}

__global__ void layer_norm_add_kernel(const float* input, const float* residual, float* output, int hidden_size, float epsilon) {
    int tid = threadIdx.x;
    int row_idx = blockIdx.x;
    
    const float* row_in = input + row_idx * hidden_size;
    const float* row_res = residual + row_idx * hidden_size;
    float* row_out = output + row_idx * hidden_size;

    // Simplified mean/variance for workload simulation
    float mean = 0.0f;
    float variance = 0.0f;
    for(int i = tid; i < hidden_size; i += blockDim.x) {
        mean += row_in[i];
    }
    mean /= hidden_size; // Note: incorrect reduction, for workload simulation only

    for(int i = tid; i < hidden_size; i += blockDim.x) {
        variance += (row_in[i] - mean) * (row_in[i] - mean);
    }
    variance /= hidden_size; // Note: incorrect reduction

    for (int i = tid; i < hidden_size; i += blockDim.x) {
        row_out[i] = (row_in[i] - mean) / sqrtf(variance + epsilon) + row_res[i];
    }
}

__global__ void softmax_bmm_kernel(float* qk, float* v, float* out, int seq_len, int head_size) {
    int row = blockIdx.y * blockDim.y + threadIdx.y;
    int col = blockIdx.x * blockDim.x + threadIdx.x;
    int head_idx = blockIdx.z;

    float* qk_head = qk + head_idx * seq_len * seq_len;
    float* v_head = v + head_idx * seq_len * head_size;
    float* out_head = out + head_idx * seq_len * head_size;

    // 1. Softmax (simplified, per-thread)
    float max_val = -FLT_MAX;
    if (threadIdx.x == 0) { // Each thread in a row calculates its own max
        for (int i = 0; i < seq_len; ++i) {
            if (qk_head[row * seq_len + i] > max_val) {
                max_val = qk_head[row * seq_len + i];
            }
        }
    }
    
    float exp_sum = 0.0f;
    if (threadIdx.x == 0) {
        for (int i = 0; i < seq_len; ++i) {
            exp_sum += expf(qk_head[row * seq_len + i] - max_val);
        }
    }

    // 2. BMM
    if (row < seq_len && col < head_size) {
        float sum = 0.0f;
        for (int i = 0; i < seq_len; ++i) {
            float softmax_val = expf(qk_head[row * seq_len + i] - max_val) / exp_sum;
            sum += softmax_val * v_head[i * head_size + col];
        }
        out_head[row * head_size + col] = sum;
    }
}

__global__ void adam_optimizer_kernel(float* params, const float* grads, float* m, float* v, int num_params, float lr, float beta1, float beta2, float epsilon) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < num_params) {
        float grad = grads[i];
        m[i] = beta1 * m[i] + (1.0f - beta1) * grad;
        v[i] = beta2 * v[i] + (1.0f - beta2) * grad * grad;
        params[i] -= lr * m[i] / (sqrtf(v[i]) + epsilon);
    }
}

// --- BERT Simulation using Functional Kernels ---

void matrix_multiply(const float* a, const float* b, float* c, int M, int N, int K, cudaStream_t stream) {
    dim3 threads(16, 16);
    dim3 blocks((N + threads.x - 1) / threads.x, (M + threads.y - 1) / threads.y);
    matrix_multiply_kernel<<<blocks, threads, 0, stream>>>(a, b, c, M, N, K);
}

void attention_softmax_bmm(float* qk, float* v, float* out, int seq_len, int batch_size, int heads, int head_size, cudaStream_t stream) {
    dim3 threads(16, 16);
    dim3 blocks((head_size + threads.x - 1) / threads.x, (seq_len + threads.y - 1) / threads.y, batch_size * heads);
    softmax_bmm_kernel<<<blocks, threads, 0, stream>>>(qk, v, out, seq_len, head_size);
}

void layer_norm_add(const float* input, const float* residual, float* output, int seq_len, int hidden_size, int batch_size, cudaStream_t stream) {
    layer_norm_add_kernel<<<batch_size * seq_len, 256, 0, stream>>>(input, residual, output, hidden_size, 1e-5f);
}

void bert_layer(float* data, float* residual, float* buffer, int seq_len, int hidden_size, int batch_size, int heads, cudaStream_t stream) {
    int M = batch_size * seq_len;
    int N = hidden_size;
    int K = hidden_size;
    int head_size = hidden_size / heads;

    matrix_multiply(data, data, buffer, M, N, K, stream);
    attention_softmax_bmm(buffer, buffer, data, seq_len, batch_size, heads, head_size, stream);
    matrix_multiply(data, data, buffer, M, N, K, stream);
    layer_norm_add(buffer, residual, data, seq_len, hidden_size, batch_size, stream);

    matrix_multiply(data, data, buffer, M, N * 4, K, stream);
    matrix_multiply(buffer, buffer, data, M, N, K * 4, stream);
    layer_norm_add(data, residual, data, seq_len, hidden_size, batch_size, stream);
}

void bert_training_step(float* data, float* grads, float* buffer, int seq_len, int hidden_size, int batch_size, int num_layers, int heads, cudaStream_t stream) {
    for (int i = 0; i < num_layers; ++i) {
        bert_layer(data, data, buffer, seq_len, hidden_size, batch_size, heads, stream);
    }
    for (int i = 0; i < num_layers; ++i) {
        bert_layer(grads, grads, buffer, seq_len, hidden_size, batch_size, heads, stream);
    }
}

void optimizer_step(float* params, const float* grads, float* m, float* v, int num_params, cudaStream_t stream) {
    const int block_size = 256;
    const int grid_size = (num_params + block_size - 1) / block_size;
    adam_optimizer_kernel<<<grid_size, block_size, 0, stream>>>(params, grads, m, v, num_params, 0.001f, 0.9f, 0.999f, 1e-8f);
}

void run_training_test(int grad_accum_steps) {
    const int seq_len = 128;
    const int hidden_size = 768;
    const int num_layers = 4; // Simplified from 12
    const int num_heads = 12;
    const int global_batch_size = 1024;
    const int per_device_batch_size = global_batch_size / grad_accum_steps;
    const int num_params = 10 * 1000 * 1000; // Simplified from 110M

    const int data_size = seq_len * hidden_size * per_device_batch_size;
    const int iterations = 20; // Simplified from 50
    const int warmup_iterations = 5;

    float *d_data, *d_grads, *d_params, *d_m, *d_v, *d_buffer;
    CHECK_CUDA(cudaMalloc(&d_data, data_size * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_grads, data_size * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_params, num_params * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_m, num_params * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_v, num_params * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_buffer, data_size * 4 * sizeof(float)));

    CHECK_CUDA(cudaMemset(d_params, 0, num_params * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_m, 0, num_params * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_v, 0, num_params * sizeof(float)));

    Timer timer;
    std::vector<double> baseline_timings, graph_timings;
    cudaStream_t stream;
    CHECK_CUDA(cudaStreamCreate(&stream));

    // --- Baseline: Traditional Loop ---
    for (int i = 0; i < iterations + warmup_iterations; ++i) {
        timer.start();
        for (int j = 0; j < grad_accum_steps; ++j) {
            bert_training_step(d_data, d_grads, d_buffer, seq_len, hidden_size, per_device_batch_size, num_layers, num_heads, stream);
        }
        optimizer_step(d_params, d_grads, d_m, d_v, num_params, stream);
        CHECK_CUDA(cudaStreamSynchronize(stream));
        timer.stop();
        if (i >= warmup_iterations) {
            baseline_timings.push_back(timer.elapsed_ms());
        }
    }

    // --- Correctness Verification (Part 1: Get baseline result) ---
    std::vector<float> h_baseline_params(num_params);
    CHECK_CUDA(cudaMemcpy(h_baseline_params.data(), d_params, num_params * sizeof(float), cudaMemcpyDeviceToHost));

    // Reset device data for graph run
    CHECK_CUDA(cudaMemset(d_params, 0, num_params * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_m, 0, num_params * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_v, 0, num_params * sizeof(float)));

    // --- Graph Mode ---
    cudaGraph_t graph;
    cudaGraphExec_t instance;
    
    CHECK_CUDA(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    for (int j = 0; j < grad_accum_steps; ++j) {
        bert_training_step(d_data, d_grads, d_buffer, seq_len, hidden_size, per_device_batch_size, num_layers, num_heads, stream);
    }
    optimizer_step(d_params, d_grads, d_m, d_v, num_params, stream);
    CHECK_CUDA(cudaStreamEndCapture(stream, &graph));
    CHECK_CUDA(cudaGraphInstantiate(&instance, graph, NULL, NULL, 0));

    for (int i = 0; i < iterations + warmup_iterations; ++i) {
        timer.start();
        CHECK_CUDA(cudaGraphLaunch(instance, stream));
        CHECK_CUDA(cudaStreamSynchronize(stream));
        timer.stop();
        if (i >= warmup_iterations) {
            graph_timings.push_back(timer.elapsed_ms());
        }
    }

    // --- Correctness Verification (Part 2: Get graph result and compare) ---
    std::vector<float> h_graph_params(num_params);
    CHECK_CUDA(cudaMemcpy(h_graph_params.data(), d_params, num_params * sizeof(float), cudaMemcpyDeviceToHost));

    bool correct = true;
    for (size_t i = 0; i < num_params; ++i) {
        if (std::abs(h_baseline_params[i] - h_graph_params[i]) > 1e-4) {
            correct = false;
            break;
        }
    }

    // --- Analysis ---
    std::cout << "\n--- S2: Training Loop Test (Grad Accum: " << grad_accum_steps << ") ---" << std::endl;
    print_stats("Baseline (Loop)", baseline_timings, global_batch_size);
    print_stats("Graph", graph_timings, global_batch_size);

    double baseline_avg_time = std::accumulate(baseline_timings.begin(), baseline_timings.end(), 0.0) / baseline_timings.size();
    double graph_avg_time = std::accumulate(graph_timings.begin(), graph_timings.end(), 0.0) / graph_timings.size();
    double time_reduction = (baseline_avg_time - graph_avg_time) / baseline_avg_time * 100.0;

    std::cout << "\n--- Results & Pass Criteria ---" << std::endl;
    std::cout << "Correctness Check: " << (correct ? "PASS" : "FAIL") << std::endl;
    std::cout << "Step Time Reduction: " << std::fixed << std::setprecision(2) << time_reduction << "%" << std::endl;
    
    if (time_reduction >= 20.0 && correct) {
        std::cout << "Overall Result: PASS" << std::endl;
    } else {
        std::cout << "Overall Result: FAIL" << std::endl;
    }
    std::cout << "--------------------------------------------------\n" << std::endl;

    // Cleanup
    CHECK_CUDA(cudaGraphExecDestroy(instance));
    CHECK_CUDA(cudaGraphDestroy(graph));
    CHECK_CUDA(cudaStreamDestroy(stream));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_grads));
    CHECK_CUDA(cudaFree(d_params));
    CHECK_CUDA(cudaFree(d_m));
    CHECK_CUDA(cudaFree(d_v));
    CHECK_CUDA(cudaFree(d_buffer));
}

TEST(S2_TrainingLoop, Performance) {
    run_training_test(1);
    run_training_test(4);
    run_training_test(8);
}
