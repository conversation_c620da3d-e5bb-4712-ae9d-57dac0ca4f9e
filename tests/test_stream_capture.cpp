#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <random>
#include <tuple>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

// --- KERNELS needed for capture tests ---
__global__ void multiply_by_const_capture(float *data, float c, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        data[i] = data[i] * c;
    }
}

__global__ void add_vectors_inplace_capture(float *a, const float *b, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        a[i] = a[i] + b[i];
    }
}

// Kernels from multi_stream, renamed to avoid conflicts if linked together
__global__ void setValue_capture(float* data, float value, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        data[i] = value;
    }
}

__global__ void addValue_capture(float* data, float value, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        data[i] += value;
    }
}

__global__ void simple_noop_kernel() {}


// --- KERNELS needed for capture tests ---
__global__ void setValue(float* data, float value, int n);
__global__ void addValue(float* data, float value, int n);


class StreamCaptureTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaFree(nullptr)); // Initialize context
        n = 256;
        size = n * sizeof(float);

        CUDA_CHECK(cudaStreamCreateWithFlags(&stream, cudaStreamNonBlocking));
        CUDA_CHECK(cudaStreamCreateWithFlags(&streamB, cudaStreamNonBlocking));

        CUDA_CHECK(cudaMalloc(&d_a, size));
        CUDA_CHECK(cudaMalloc(&d_b, size));
        CUDA_CHECK(cudaMalloc(&d_c, size));
        
        std::vector<float> h_zeros(n, 0.0f);
        CUDA_CHECK(cudaMemcpy(d_a, h_zeros.data(), size, cudaMemcpyHostToDevice));
        CUDA_CHECK(cudaMemcpy(d_b, h_zeros.data(), size, cudaMemcpyHostToDevice));
        CUDA_CHECK(cudaMemcpy(d_c, h_zeros.data(), size, cudaMemcpyHostToDevice));
    }

    void TearDown() override {
        CUDA_CHECK(cudaFree(d_a));
        CUDA_CHECK(cudaFree(d_b));
        CUDA_CHECK(cudaFree(d_c));
        CUDA_CHECK(cudaStreamDestroy(stream));
        CUDA_CHECK(cudaStreamDestroy(streamB));
    }

    cudaStream_t stream, streamB; // stream is the main one, streamB is for multi-stream tests
    float *d_a, *d_b, *d_c;
    int n;
    size_t size;
};


TEST_F(StreamCaptureTest, BasicCaptureAndLaunch) {
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t exec = nullptr;
    
    std::vector<float> h_a(n, 3.0f);
    std::vector<float> h_b(n, 4.0f);
    std::vector<float> h_c(n, 0.0f);
    CUDA_CHECK(cudaMemcpy(d_a, h_a.data(), size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_b, h_b.data(), size, cudaMemcpyHostToDevice));

    // --- Capture ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));

    float const_val = 10.0f;
    multiply_by_const_capture<<<1, 256, 0, stream>>>(d_a, const_val, n); // a = 3 * 10 = 30
    add_vectors_inplace_capture<<<1, 256, 0, stream>>>(d_c, d_a, n);     // c = 0 + 30 = 30
    add_vectors_inplace_capture<<<1, 256, 0, stream>>>(d_c, d_b, n);     // c = 30 + 4 = 34
    
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiate and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verify ---
    CUDA_CHECK(cudaMemcpy(h_c.data(), d_c, size, cudaMemcpyDeviceToHost));
    for (int i = 0; i < n; ++i) {
        ASSERT_FLOAT_EQ(h_c[i], 34.0f);
    }

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(StreamCaptureTest, CaptureInterStreamDependencyWithEvents) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    // Define the graph by capturing operations from multiple streams
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));

    // Op 1 on the main stream `stream`: Set d_a to 10
    float valA = 10.0f;
    setValue_capture<<<1, 256, 0, stream>>>(d_a, valA, n);

    // Create an event to sync streamB with the main stream
    cudaEvent_t event;
    CUDA_CHECK(cudaEventCreate(&event));
    CUDA_CHECK(cudaEventRecord(event, stream));

    // Op 2 on Stream B: Wait for Op 1 and then add 5 to d_a
    CUDA_CHECK(cudaStreamWaitEvent(streamB, event, 0));
    float valB = 5.0f;
    addValue_capture<<<1, 256, 0, streamB>>>(d_a, valB, n);

    // End capture on the main stream. This should capture operations from both streams.
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));

    // Instantiate and launch the graph
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    // Launch on a clean stream to check execution
    cudaStream_t launch_stream;
    CUDA_CHECK(cudaStreamCreate(&launch_stream));
    CUDA_CHECK(cudaGraphLaunch(exec, launch_stream));
    CUDA_CHECK(cudaStreamSynchronize(launch_stream));

    // Verification
    std::vector<float> h_result(n);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_a, size, cudaMemcpyDeviceToHost));
    // Expected result: 10 + 5 = 15
    for (int i = 0; i < n; ++i) {
        ASSERT_FLOAT_EQ(h_result[i], 15.0f);
    }
    
    CUDA_CHECK(cudaEventDestroy(event));
    CUDA_CHECK(cudaStreamDestroy(launch_stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// --- Stream Capture Error Tests ---

class StreamCaptureErrorTest : public ::testing::Test {};

TEST_F(StreamCaptureErrorTest, EndCaptureWithoutBegin) {
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    cudaGraph_t graph;
    ASSERT_NE(cudaStreamEndCapture(stream, &graph), cudaSuccess);
    CUDA_CHECK(cudaStreamDestroy(stream));
}

TEST_F(StreamCaptureErrorTest, BeginCaptureOnAlreadyCapturingStream) {
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    // This second call should fail
    ASSERT_NE(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    
    // Clean up properly
    cudaGraph_t graph;
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph)); // This will capture the single "begin"
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
}

// --- Test Suite 7: Multi-Stream Capture Stress (4*4*4 = 64 Tests) ---
using StreamCaptureParams = std::tuple<int, int, int>; // num_streams, ops_per_stream, num_dependencies

class StreamCaptureStressTest : public ::testing::TestWithParam<StreamCaptureParams> {};

TEST_P(StreamCaptureStressTest, CaptureFromMultipleStreamsWithDependencies) {
    auto p = GetParam();
    int num_streams = std::get<0>(p);
    int ops_per_stream = std::get<1>(p);
    int num_dependencies = std::get<2>(p);
    
    cudaGraph_t graph;
    std::vector<cudaStream_t> streams(num_streams);
    for(int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamCreateWithFlags(&streams[i], cudaStreamNonBlocking));
    }

    // Begin capture on the default stream
    CUDA_CHECK(cudaStreamBeginCapture(streams[0], cudaStreamCaptureModeGlobal));

    for (int i = 0; i < ops_per_stream; ++i) {
        for (int j = 0; j < num_streams; ++j) {
            simple_noop_kernel<<<1, 1, 0, streams[j]>>>();
        }
    }

    // Create inter-stream dependencies
    std::vector<cudaEvent_t> events(num_dependencies);
    std::mt19937 rng(std::random_device{}());
    for (int i = 0; i < num_dependencies; ++i) {
        CUDA_CHECK(cudaEventCreate(&events[i]));
        // A stream records an event, a different stream waits for it.
        int stream_rec_idx = std::uniform_int_distribution<int>(0, num_streams - 1)(rng);
        int stream_wait_idx = std::uniform_int_distribution<int>(0, num_streams - 1)(rng);
        if (stream_rec_idx == stream_wait_idx && num_streams > 1) {
            stream_wait_idx = (stream_wait_idx + 1) % num_streams;
        }
        CUDA_CHECK(cudaEventRecord(events[i], streams[stream_rec_idx]));
        CUDA_CHECK(cudaStreamWaitEvent(streams[stream_wait_idx], events[i], 0));
    }

    // End capture and get the graph
    CUDA_CHECK(cudaStreamEndCapture(streams[0], &graph));

    ASSERT_NE(graph, nullptr);

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    // Launch the graph and verify it runs
    cudaStream_t launch_stream;
    CUDA_CHECK(cudaStreamCreate(&launch_stream));
    CUDA_CHECK(cudaGraphLaunch(exec, launch_stream));
    CUDA_CHECK(cudaStreamSynchronize(launch_stream));

    // Cleanup
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(launch_stream));
    for(int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
    }
     for (int i = 0; i < num_dependencies; ++i) {
        CUDA_CHECK(cudaEventDestroy(events[i]));
    }
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(StreamCaptureSuite, StreamCaptureStressTest,
    ::testing::Combine(
        ::testing::Values(2, 4, 8, 16),             // num_streams
        ::testing::Values(10, 50, 100, 200),        // ops_per_stream
        ::testing::Values(5, 10, 20, 40)            // num_dependencies
    ),
    [](const auto& info) {
        return "Streams" + std::to_string(std::get<0>(info.param)) +
               "__Ops" + std::to_string(std::get<1>(info.param)) +
               "__Deps" + std::to_string(std::get<2>(info.param));
    }
);
