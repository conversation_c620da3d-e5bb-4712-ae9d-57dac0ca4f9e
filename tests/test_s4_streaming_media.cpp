#include "common.h"
#include <vector>
#include <thread>
#include <chrono>

// --- Streaming Media Kernel Simulations ---

// Simulates a generic image filter (e.g., blur, sharpen)
__global__ void image_filter_kernel(const float* input, float* output, int width, int height) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < width && y < height) {
        int idx = y * width + x;
        // Simple operation to simulate a filter workload
        output[idx] = input[idx] * 0.9f + 0.1f;
    }
}

// Simulates an ESPCN-style super-resolution (pixel shuffle)
__global__ void espcn_super_resolution_kernel(const float* input, float* output, int width, int height, int scale) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < width && y < height) {
        int in_idx = y * width + x;
        // Simplified pixel shuffle logic for workload simulation
        for (int i = 0; i < scale; ++i) {
            for (int j = 0; j < scale; ++j) {
                int out_idx = (y * scale + i) * (width * scale) + (x * scale + j);
                output[out_idx] = input[in_idx];
            }
        }
    }
}

// --- Host-side simulation of hardware decode/encode ---
void simulate_decode() {
    // Simulate the time it takes for a hardware decoder to prepare a frame
    std::this_thread::sleep_for(std::chrono::milliseconds(2));
}

void simulate_encode() {
    // Simulate the time it takes for a hardware encoder to process a frame
    std::this_thread::sleep_for(std::chrono::milliseconds(3));
}

// --- Test Logic ---

void media_pipeline_workload(float* d_input, float* d_filter_buffers[], float* d_espcn_output, int width, int height, int scale, int num_filters, cudaStream_t stream) {
    dim3 threads(16, 16);
    dim3 blocks((width + threads.x - 1) / threads.x, (height + threads.y - 1) / threads.y);

    image_filter_kernel<<<blocks, threads, 0, stream>>>(d_input, d_filter_buffers[0], width, height);
    for (int j = 1; j < num_filters; ++j) {
        image_filter_kernel<<<blocks, threads, 0, stream>>>(d_filter_buffers[j-1], d_filter_buffers[j], width, height);
    }
    espcn_super_resolution_kernel<<<blocks, threads, 0, stream>>>(d_filter_buffers[num_filters-1], d_espcn_output, width, height, scale);
}

void run_streaming_test() {
    const int width = 3840;
    const int height = 2160;
    const int scale = 2;
    const int num_filters = 4;
    const double target_fps = 60.0;
    const double target_frame_time_ms = 1000.0 / target_fps;
    const int total_frames = 300;
    const int warmup_frames = 30;

    float *d_input, *d_filter_buffers[num_filters], *d_espcn_output;
    CHECK_CUDA(cudaMalloc(&d_input, width * height * sizeof(float)));
    for (int i = 0; i < num_filters; ++i) {
        CHECK_CUDA(cudaMalloc(&d_filter_buffers[i], width * height * sizeof(float)));
    }
    CHECK_CUDA(cudaMalloc(&d_espcn_output, width * height * scale * scale * sizeof(float)));

    Timer timer;
    std::vector<double> baseline_latencies, graph_latencies;
    cudaStream_t stream;
    CHECK_CUDA(cudaStreamCreate(&stream));

    // --- Baseline: Per-Frame Kernel Launch ---
    for (int i = 0; i < total_frames + warmup_frames; ++i) {
        simulate_decode();
        timer.start();
        media_pipeline_workload(d_input, d_filter_buffers, d_espcn_output, width, height, scale, num_filters, stream);
        CHECK_CUDA(cudaStreamSynchronize(stream));
        timer.stop();
        simulate_encode();
        if (i >= warmup_frames) baseline_latencies.push_back(timer.elapsed_ms());
    }

    // --- Correctness Verification (Part 1) ---
    std::vector<float> h_baseline_result(width * height * scale * scale);
    CHECK_CUDA(cudaMemcpy(h_baseline_result.data(), d_espcn_output, h_baseline_result.size() * sizeof(float), cudaMemcpyDeviceToHost));

    // --- Graph Mode ---
    cudaGraph_t graph;
    cudaGraphExec_t instance;
    CHECK_CUDA(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    media_pipeline_workload(d_input, d_filter_buffers, d_espcn_output, width, height, scale, num_filters, stream);
    CHECK_CUDA(cudaStreamEndCapture(stream, &graph));
    CHECK_CUDA(cudaGraphInstantiate(&instance, graph, NULL, NULL, 0));

    for (int i = 0; i < total_frames + warmup_frames; ++i) {
        simulate_decode();
        timer.start();
        CHECK_CUDA(cudaGraphLaunch(instance, stream));
        CHECK_CUDA(cudaStreamSynchronize(stream));
        timer.stop();
        simulate_encode();
        if (i >= warmup_frames) graph_latencies.push_back(timer.elapsed_ms());
    }

    // --- Correctness Verification (Part 2) ---
    std::vector<float> h_graph_result(width * height * scale * scale);
    CHECK_CUDA(cudaMemcpy(h_graph_result.data(), d_espcn_output, h_graph_result.size() * sizeof(float), cudaMemcpyDeviceToHost));
    bool correct = true;
    for (size_t i = 0; i < h_baseline_result.size(); ++i) {
        if (std::abs(h_baseline_result[i] - h_graph_result[i]) > 1e-5) {
            correct = false;
            break;
        }
    }

    // --- Analysis ---
    auto calculate_stats = [&](const std::vector<double>& latencies, const std::string& name) {
        double avg_latency = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();
        int dropped_frames = 0;
        for (double l : latencies) {
            if (l > target_frame_time_ms) dropped_frames++;
        }
        double drop_rate = static_cast<double>(dropped_frames) / latencies.size() * 100.0;
        std::cout << "\n--- " << name << " ---" << std::endl;
        std::cout << "Average Compute Latency: " << std::fixed << std::setprecision(2) << avg_latency << " ms" << std::endl;
        std::cout << "Frame Drop Rate: " << std::fixed << std::setprecision(2) << drop_rate << "%" << std::endl;
        return std::make_tuple(avg_latency, drop_rate);
    };

    std::cout << "\n--- S4: Streaming Media Test (Target: " << target_fps << " FPS) ---" << std::endl;
    calculate_stats(baseline_latencies, "Baseline (Launch)");
    auto graph_stats = calculate_stats(graph_latencies, "Graph");

    std::cout << "\n--- Results & Pass Criteria ---" << std::endl;
    double avg_latency_graph = std::get<0>(graph_stats);
    double drop_rate_graph = std::get<1>(graph_stats);
    bool latency_pass = avg_latency_graph < 16.0;
    bool drop_rate_pass = drop_rate_graph == 0.0;

    std::cout << "Correctness Check: " << (correct ? "PASS" : "FAIL") << std::endl;
    std::cout << "Graph Avg Latency < 16ms: " << (latency_pass ? "PASS" : "FAIL") << std::endl;
    std::cout << "Graph Frame Drop Rate == 0%: " << (drop_rate_pass ? "PASS" : "FAIL") << std::endl;

    if (latency_pass && drop_rate_pass && correct) {
        std::cout << "Overall Result: PASS" << std::endl;
    } else {
        std::cout << "Overall Result: FAIL" << std::endl;
    }
    std::cout << "--------------------------------------------------\n" << std::endl;

    // Cleanup
    CHECK_CUDA(cudaGraphExecDestroy(instance));
    CHECK_CUDA(cudaGraphDestroy(graph));
    CHECK_CUDA(cudaStreamDestroy(stream));
    CHECK_CUDA(cudaFree(d_input));
    for (int i = 0; i < num_filters; ++i) CHECK_CUDA(cudaFree(d_filter_buffers[i]));
    CHECK_CUDA(cudaFree(d_espcn_output));
}

TEST(S4_StreamingMedia, Performance) {
    run_streaming_test();
}
