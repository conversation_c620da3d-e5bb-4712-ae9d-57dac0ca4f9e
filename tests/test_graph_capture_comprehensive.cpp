#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <iostream>
#include <thread>
#include <atomic>

#define CUDA_CHECK(err) { \
    cudaError_t err_ = (err); \
    if (err_ != cudaSuccess) { \
        FAIL() << "CUDA error at " << __FILE__ << ":" << __LINE__ << ": " << cudaGetErrorString(err_); \
    } \
}

// Simple kernel for testing
__global__ void simple_kernel(float* out, const float* in, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        out[idx] = in[idx] * 2.0f;
    }
}

// Test fixture for different capture modes
class GraphCaptureModeTest : public ::testing::TestWithParam<cudaStreamCaptureMode> {
protected:
    void SetUp() override {
        const int size = 256;
        bytes = size * sizeof(float);

        h_in.resize(size);
        for (int i = 0; i < size; ++i) {
            h_in[i] = static_cast<float>(i);
        }
        h_out.resize(size, 0);

        CUDA_CHECK(cudaMalloc(&d_in, bytes));
        CUDA_CHECK(cudaMalloc(&d_out, bytes));
        CUDA_CHECK(cudaStreamCreate(&stream));
    }

    void TearDown() override {
        CUDA_CHECK(cudaFree(d_in));
        CUDA_CHECK(cudaFree(d_out));
        CUDA_CHECK(cudaStreamDestroy(stream));
    }

    size_t bytes;
    std::vector<float> h_in;
    std::vector<float> h_out;
    float *d_in, *d_out;
    cudaStream_t stream;
};

// Test fixture for multi-stream interaction during capture
class MultiStreamInteractionTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaStreamCreate(&stream1));
        CUDA_CHECK(cudaStreamCreate(&stream2));
        CUDA_CHECK(cudaEventCreate(&event));

        const int size = 256;
        bytes = size * sizeof(float);
        h_data.resize(size);
        std::fill(h_data.begin(), h_data.end(), 1.0f);

        CUDA_CHECK(cudaMalloc(&d_data, bytes));
        CUDA_CHECK(cudaMemcpy(d_data, h_data.data(), bytes, cudaMemcpyHostToDevice));
    }

    void TearDown() override {
        CUDA_CHECK(cudaStreamDestroy(stream1));
        CUDA_CHECK(cudaStreamDestroy(stream2));
        CUDA_CHECK(cudaEventDestroy(event));
        CUDA_CHECK(cudaFree(d_data));
    }

    cudaStream_t stream1, stream2;
    cudaEvent_t event;
    float* d_data;
    std::vector<float> h_data;
    size_t bytes;
};

// Test fixture for invalid capture scenarios
class InvalidCaptureTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaStreamCreate(&stream));
    }

    void TearDown() override {
        CUDA_CHECK(cudaStreamDestroy(stream));
    }

    cudaStream_t stream;
};

// Test fixture for boundary and exception scenarios
class AdvancedCaptureScenariosTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaStreamCreate(&stream));
    }

    void TearDown() override {
        CUDA_CHECK(cudaStreamDestroy(stream));
    }

    cudaStream_t stream;
};

// Test fixture for stress tests
class StressTest : public ::testing::Test {};

// Test fixture for different node types
class GraphNodeTypeTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaStreamCreate(&stream));
    }

    void TearDown() override {
        CUDA_CHECK(cudaStreamDestroy(stream));
    }

    cudaStream_t stream;
};

// Test fixture for checking stream capture status
class StreamCaptureStateTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaStreamCreate(&stream));
    }

    void TearDown() override {
        CUDA_CHECK(cudaStreamDestroy(stream));
    }

    cudaStream_t stream;
};


// --- Test Cases ---

// Parameterized test to run for each capture mode
TEST_P(GraphCaptureModeTest, CaptureAndLaunch) {
    cudaStreamCaptureMode mode = GetParam();
    const int size = h_in.size();

    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream, mode));
    CUDA_CHECK(cudaMemcpyAsync(d_in, h_in.data(), bytes, cudaMemcpyHostToDevice, stream));
    simple_kernel<<<1, 256, 0, stream>>>(d_out, d_in, size);
    CUDA_CHECK(cudaMemcpyAsync(h_out.data(), d_out, bytes, cudaMemcpyDeviceToHost, stream));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));

    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch Phase ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    ASSERT_NE(graph_exec, nullptr);

    // Reset output buffer for a clean run
    std::fill(h_out.begin(), h_out.end(), 0.0f);
    CUDA_CHECK(cudaMemcpy(d_out, h_out.data(), bytes, cudaMemcpyHostToDevice));

    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // --- Verification ---
    CUDA_CHECK(cudaMemcpy(h_out.data(), d_out, bytes, cudaMemcpyDeviceToHost));

    for (int i = 0; i < size; ++i) {
        ASSERT_FLOAT_EQ(h_out[i], h_in[i] * 2.0f) << "Verification failed at index " << i;
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test to verify that stream synchronization is correctly captured
TEST_F(MultiStreamInteractionTest, CaptureWithStreamSynchronization) {
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // stream2 does some work and records an event
    simple_kernel<<<1, 256, 0, stream2>>>(d_data, d_data, h_data.size());
    CUDA_CHECK(cudaEventRecord(event, stream2));

    // stream1 waits for the event from stream2
    CUDA_CHECK(cudaStreamWaitEvent(stream1, event, 0));
    
    // More work on stream1
    simple_kernel<<<1, 256, 0, stream1>>>(d_data, d_data, h_data.size());

    CUDA_CHECK(cudaStreamEndCapture(stream1, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    ASSERT_NE(graph_exec, nullptr);

    // Reset data
    std::fill(h_data.begin(), h_data.end(), 1.0f);
    CUDA_CHECK(cudaMemcpy(d_data, h_data.data(), bytes, cudaMemcpyHostToDevice));

    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream1));

    // --- Verification ---
    CUDA_CHECK(cudaMemcpy(h_data.data(), d_data, bytes, cudaMemcpyDeviceToHost));

    // After two kernel launches (one outside, one inside the graph), each value should be 4.0
    for (size_t i = 0; i < h_data.size(); ++i) {
        ASSERT_FLOAT_EQ(h_data[i], 4.0f) << "Verification failed at index " << i;
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing a fork-join pattern across two streams, based on user-provided example
TEST_F(MultiStreamInteractionTest, CaptureWithForkJoinPattern) {
    cudaEvent_t event_fork, event_join;
    CUDA_CHECK(cudaEventCreate(&event_fork));
    CUDA_CHECK(cudaEventCreate(&event_join));

    // Use a second data buffer for the forked stream to avoid race conditions
    float* d_data2;
    std::vector<float> h_data2(h_data.size());
    std::fill(h_data2.begin(), h_data2.end(), 1.0f);
    CUDA_CHECK(cudaMalloc(&d_data2, bytes));
    CUDA_CHECK(cudaMemcpy(d_data2, h_data2.data(), bytes, cudaMemcpyHostToDevice));

    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    // This follows the fork-join pattern: A -> (B || C) -> D
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // Kernel A on stream1
    simple_kernel<<<1, 256, 0, stream1>>>(d_data, d_data, h_data.size());

    // Fork: stream1 signals stream2
    CUDA_CHECK(cudaEventRecord(event_fork, stream1));
    CUDA_CHECK(cudaStreamWaitEvent(stream2, event_fork, 0));

    // Kernel B on stream1 (runs in parallel with C)
    simple_kernel<<<1, 256, 0, stream1>>>(d_data, d_data, h_data.size());
    // Kernel C on stream2 (runs in parallel with B)
    simple_kernel<<<1, 256, 0, stream2>>>(d_data2, d_data2, h_data2.size());

    // Join: stream1 waits for stream2
    CUDA_CHECK(cudaEventRecord(event_join, stream2));
    CUDA_CHECK(cudaStreamWaitEvent(stream1, event_join, 0));

    // Kernel D on stream1
    simple_kernel<<<1, 256, 0, stream1>>>(d_data, d_data, h_data.size());

    CUDA_CHECK(cudaStreamEndCapture(stream1, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    ASSERT_NE(graph_exec, nullptr);

    // Reset data for a clean run
    std::fill(h_data.begin(), h_data.end(), 1.0f);
    std::fill(h_data2.begin(), h_data2.end(), 1.0f);
    CUDA_CHECK(cudaMemcpy(d_data, h_data.data(), bytes, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_data2, h_data2.data(), bytes, cudaMemcpyHostToDevice));

    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream1));

    // --- Verification ---
    CUDA_CHECK(cudaMemcpy(h_data.data(), d_data, bytes, cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(h_data2.data(), d_data2, bytes, cudaMemcpyDeviceToHost));

    for (size_t i = 0; i < h_data.size(); ++i) {
        ASSERT_FLOAT_EQ(h_data[i], 8.0f) << "Verification failed for d_data at index " << i;
    }
    for (size_t i = 0; i < h_data2.size(); ++i) {
        ASSERT_FLOAT_EQ(h_data2[i], 2.0f) << "Verification failed for d_data2 at index " << i;
    }

    // --- Cleanup ---
    CUDA_CHECK(cudaFree(d_data2));
    CUDA_CHECK(cudaEventDestroy(event_fork));
    CUDA_CHECK(cudaEventDestroy(event_join));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing a one-to-many fork-join pattern
TEST_F(MultiStreamInteractionTest, CaptureWithMultipleForks) {
    const int num_forks = 3;
    std::vector<cudaStream_t> streams(num_forks);
    std::vector<cudaEvent_t> fork_events(num_forks);
    std::vector<cudaEvent_t> join_events(num_forks);
    std::vector<float*> d_data_forks(num_forks);
    std::vector<std::vector<float>> h_data_forks(num_forks, std::vector<float>(h_data.size()));

    for (int i = 0; i < num_forks; ++i) {
        CUDA_CHECK(cudaStreamCreate(&streams[i]));
        CUDA_CHECK(cudaEventCreate(&fork_events[i]));
        CUDA_CHECK(cudaEventCreate(&join_events[i]));
        CUDA_CHECK(cudaMalloc(&d_data_forks[i], bytes));
        std::fill(h_data_forks[i].begin(), h_data_forks[i].end(), 1.0f);
        CUDA_CHECK(cudaMemcpy(d_data_forks[i], h_data_forks[i].data(), bytes, cudaMemcpyHostToDevice));
    }

    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;

    // --- Capture Phase ---
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // Initial kernel on main stream
    simple_kernel<<<1, 256, 0, stream1>>>(d_data, d_data, h_data.size());

    // Fork to multiple streams
    for (int i = 0; i < num_forks; ++i) {
        CUDA_CHECK(cudaEventRecord(fork_events[i], stream1));
        CUDA_CHECK(cudaStreamWaitEvent(streams[i], fork_events[i], 0));
        simple_kernel<<<1, 256, 0, streams[i]>>>(d_data_forks[i], d_data_forks[i], h_data_forks[i].size());
    }

    // Join from multiple streams
    for (int i = 0; i < num_forks; ++i) {
        CUDA_CHECK(cudaEventRecord(join_events[i], streams[i]));
        CUDA_CHECK(cudaStreamWaitEvent(stream1, join_events[i], 0));
    }

    // Final kernel on main stream
    simple_kernel<<<1, 256, 0, stream1>>>(d_data, d_data, h_data.size());

    CUDA_CHECK(cudaStreamEndCapture(stream1, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation and Launch ---
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    ASSERT_NE(graph_exec, nullptr);

    // Reset data
    std::fill(h_data.begin(), h_data.end(), 1.0f);
    CUDA_CHECK(cudaMemcpy(d_data, h_data.data(), bytes, cudaMemcpyHostToDevice));
    for (int i = 0; i < num_forks; ++i) {
        std::fill(h_data_forks[i].begin(), h_data_forks[i].end(), 1.0f);
        CUDA_CHECK(cudaMemcpy(d_data_forks[i], h_data_forks[i].data(), bytes, cudaMemcpyHostToDevice));
    }

    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream1));

    // --- Verification ---
    CUDA_CHECK(cudaMemcpy(h_data.data(), d_data, bytes, cudaMemcpyDeviceToHost));
    for (size_t i = 0; i < h_data.size(); ++i) {
        ASSERT_FLOAT_EQ(h_data[i], 4.0f) << "Verification failed for main data at index " << i;
    }
    for (int i = 0; i < num_forks; ++i) {
        CUDA_CHECK(cudaMemcpy(h_data_forks[i].data(), d_data_forks[i], bytes, cudaMemcpyDeviceToHost));
        for (size_t j = 0; j < h_data_forks[i].size(); ++j) {
            ASSERT_FLOAT_EQ(h_data_forks[i][j], 2.0f) << "Verification failed for forked data " << i << " at index " << j;
        }
    }

    // --- Cleanup ---
    for (int i = 0; i < num_forks; ++i) {
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
        CUDA_CHECK(cudaEventDestroy(fork_events[i]));
        CUDA_CHECK(cudaEventDestroy(join_events[i]));
        CUDA_CHECK(cudaFree(d_data_forks[i]));
    }
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test a diamond dependency pattern: stream1 -> {stream2, stream3} -> stream4
TEST_F(MultiStreamInteractionTest, CaptureWithDiamondDependency) {
    cudaStream_t stream3, stream4;
    cudaEvent_t event1_2, event1_3, event2_4, event3_4;
    float *d_data2, *d_data3, *d_data4;

    CUDA_CHECK(cudaStreamCreate(&stream3));
    CUDA_CHECK(cudaStreamCreate(&stream4));
    CUDA_CHECK(cudaEventCreate(&event1_2));
    CUDA_CHECK(cudaEventCreate(&event1_3));
    CUDA_CHECK(cudaEventCreate(&event2_4));
    CUDA_CHECK(cudaEventCreate(&event3_4));
    CUDA_CHECK(cudaMalloc(&d_data2, bytes));
    CUDA_CHECK(cudaMalloc(&d_data3, bytes));
    CUDA_CHECK(cudaMalloc(&d_data4, bytes));

    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // Initial work on stream1
    simple_kernel<<<1, 256, 0, stream1>>>(d_data, d_data, h_data.size());

    // Fork from stream1 to stream2 and stream3
    CUDA_CHECK(cudaEventRecord(event1_2, stream1));
    CUDA_CHECK(cudaEventRecord(event1_3, stream1));
    CUDA_CHECK(cudaStreamWaitEvent(stream2, event1_2, 0));
    CUDA_CHECK(cudaStreamWaitEvent(stream3, event1_3, 0));

    // Parallel work on stream2 and stream3
    simple_kernel<<<1, 256, 0, stream2>>>(d_data2, d_data, h_data.size());
    simple_kernel<<<1, 256, 0, stream3>>>(d_data3, d_data, h_data.size());

    // Join from stream2 and stream3 to stream4
    CUDA_CHECK(cudaEventRecord(event2_4, stream2));
    CUDA_CHECK(cudaEventRecord(event3_4, stream3));
    CUDA_CHECK(cudaStreamWaitEvent(stream4, event2_4, 0));
    CUDA_CHECK(cudaStreamWaitEvent(stream4, event3_4, 0));

    // Final work on stream4
    simple_kernel<<<1, 256, 0, stream4>>>(d_data4, d_data2, h_data.size());
    simple_kernel<<<1, 256, 0, stream4>>>(d_data4, d_data3, h_data.size());

    // End capture on a stream involved in the dependency chain
    CUDA_CHECK(cudaStreamEndCapture(stream1, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Instantiation, Launch, and Verification ---
    cudaGraphExec_t graph_exec;
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream1));

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
    CUDA_CHECK(cudaStreamDestroy(stream3));
    CUDA_CHECK(cudaStreamDestroy(stream4));
    CUDA_CHECK(cudaEventDestroy(event1_2));
    CUDA_CHECK(cudaEventDestroy(event1_3));
    CUDA_CHECK(cudaEventDestroy(event2_4));
    CUDA_CHECK(cudaEventDestroy(event3_4));
    CUDA_CHECK(cudaFree(d_data2));
    CUDA_CHECK(cudaFree(d_data3));
    CUDA_CHECK(cudaFree(d_data4));
}

// Test behavior of Global vs ThreadLocal capture modes with external dependencies
TEST_F(MultiStreamInteractionTest, CaptureModeBehavior_GlobalVsThreadLocal) {
    cudaGraph_t graph = nullptr;

    // --- Global Mode: Should succeed by pulling stream2's work into the graph ---
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));
    
    // stream2 has work that stream1 will depend on
    simple_kernel<<<1, 1, 0, stream2>>>(d_data, d_data, 1);
    CUDA_CHECK(cudaEventRecord(event, stream2));
    CUDA_CHECK(cudaStreamWaitEvent(stream1, event, 0)); // stream1 waits on uncaptured stream2
    
    cudaError_t global_err = cudaStreamEndCapture(stream1, &graph);
    ASSERT_EQ(global_err, cudaSuccess) << "Global mode should capture the dependency from stream2";
    ASSERT_NE(graph, nullptr);
    CUDA_CHECK(cudaGraphDestroy(graph));
    graph = nullptr;

    // --- ThreadLocal Mode: Should fail because stream2 is external ---
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeThreadLocal));

    // Same dependency setup
    simple_kernel<<<1, 1, 0, stream2>>>(d_data, d_data, 1);
    CUDA_CHECK(cudaEventRecord(event, stream2));
    CUDA_CHECK(cudaStreamWaitEvent(stream1, event, 0));

    cudaError_t local_err = cudaStreamEndCapture(stream1, &graph);
    ASSERT_NE(local_err, cudaSuccess) << "ThreadLocal mode should fail to capture external dependency";
    ASSERT_EQ(graph, nullptr);

    // Cleanup from failed capture
    (void)cudaGetLastError();
    CUDA_CHECK(cudaStreamSynchronize(stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream2));
}

// Test behavior of Relaxed capture mode
TEST_F(MultiStreamInteractionTest, CaptureModeBehavior_Relaxed) {
    cudaGraph_t graph = nullptr;
    cudaEvent_t ext_event; // Event on an external stream
    CUDA_CHECK(cudaEventCreate(&ext_event));

    // --- Case 1: Captured stream waits on external stream (Allowed in Relaxed) ---
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeRelaxed));
    
    // External work on stream2
    simple_kernel<<<1, 1, 0, stream2>>>(d_data, d_data, 1);
    CUDA_CHECK(cudaEventRecord(ext_event, stream2));
    
    // Captured stream1 waits on external stream2. This is allowed.
    CUDA_CHECK(cudaStreamWaitEvent(stream1, ext_event, 0));
    
    cudaError_t relaxed_err = cudaStreamEndCapture(stream1, &graph);
    ASSERT_EQ(relaxed_err, cudaSuccess) << "Relaxed mode should allow waiting on an external event";
    ASSERT_NE(graph, nullptr);
    CUDA_CHECK(cudaGraphDestroy(graph));
    graph = nullptr;

    // --- Case 2: External stream waits on captured stream (Disallowed) ---
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeRelaxed));

    // Captured work on stream1
    simple_kernel<<<1, 1, 0, stream1>>>(d_data, d_data, 1);
    CUDA_CHECK(cudaEventRecord(event, stream1));

    // External stream2 waits on captured stream1. This is an error.
    CUDA_CHECK(cudaStreamWaitEvent(stream2, event, 0));

    cudaError_t relaxed_err_2 = cudaStreamEndCapture(stream1, &graph);
    ASSERT_NE(relaxed_err_2, cudaSuccess) << "Relaxed mode should not allow an external stream to wait on it";
    ASSERT_EQ(graph, nullptr);

    // Cleanup
    (void)cudaGetLastError();
    CUDA_CHECK(cudaStreamSynchronize(stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream2));
    CUDA_CHECK(cudaEventDestroy(ext_event));
}

// Test a sequential chain of stream dependencies: stream1 -> stream2 -> ...
TEST_F(MultiStreamInteractionTest, CaptureWithChainedStreamDependency) {
    const int num_streams = 4;
    std::vector<cudaStream_t> streams(num_streams);
    std::vector<cudaEvent_t> events(num_streams - 1);
    for (int i = 0; i < num_streams; ++i) CUDA_CHECK(cudaStreamCreate(&streams[i]));
    for (int i = 0; i < num_streams - 1; ++i) CUDA_CHECK(cudaEventCreate(&events[i]));

    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(streams[0], cudaStreamCaptureModeGlobal));

    // Create a chain of dependencies
    for (int i = 0; i < num_streams; ++i) {
        simple_kernel<<<1, 256, 0, streams[i]>>>(d_data, d_data, h_data.size());
        if (i < num_streams - 1) {
            CUDA_CHECK(cudaEventRecord(events[i], streams[i]));
            CUDA_CHECK(cudaStreamWaitEvent(streams[i+1], events[i], 0));
        }
    }

    CUDA_CHECK(cudaStreamEndCapture(streams[0], &graph));
    ASSERT_NE(graph, nullptr);

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    for (int i = 0; i < num_streams; ++i) CUDA_CHECK(cudaStreamDestroy(streams[i]));
    for (int i = 0; i < num_streams - 1; ++i) CUDA_CHECK(cudaEventDestroy(events[i]));
}

// Test a repeated fork-join pattern to the same stream
TEST_F(MultiStreamInteractionTest, CaptureWithRepeatedForkJoin) {
    cudaGraph_t graph = nullptr;
    cudaEvent_t e1, e2, e3, e4;
    CUDA_CHECK(cudaEventCreate(&e1));
    CUDA_CHECK(cudaEventCreate(&e2));
    CUDA_CHECK(cudaEventCreate(&e3));
    CUDA_CHECK(cudaEventCreate(&e4));

    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // First fork-join
    simple_kernel<<<1, 1, 0, stream1>>>(d_data, d_data, 1);
    CUDA_CHECK(cudaEventRecord(e1, stream1));
    CUDA_CHECK(cudaStreamWaitEvent(stream2, e1, 0));
    simple_kernel<<<1, 1, 0, stream2>>>(d_data, d_data, 1);
    CUDA_CHECK(cudaEventRecord(e2, stream2));
    CUDA_CHECK(cudaStreamWaitEvent(stream1, e2, 0));

    // Work on main stream
    simple_kernel<<<1, 1, 0, stream1>>>(d_data, d_data, 1);

    // Second fork-join
    CUDA_CHECK(cudaEventRecord(e3, stream1));
    CUDA_CHECK(cudaStreamWaitEvent(stream2, e3, 0));
    simple_kernel<<<1, 1, 0, stream2>>>(d_data, d_data, 1);
    CUDA_CHECK(cudaEventRecord(e4, stream2));
    CUDA_CHECK(cudaStreamWaitEvent(stream1, e4, 0));
    
    simple_kernel<<<1, 1, 0, stream1>>>(d_data, d_data, 1);

    CUDA_CHECK(cudaStreamEndCapture(stream1, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaEventDestroy(e1));
    CUDA_CHECK(cudaEventDestroy(e2));
    CUDA_CHECK(cudaEventDestroy(e3));
    CUDA_CHECK(cudaEventDestroy(e4));
}

// Test that creating a circular dependency between streams fails capture
TEST_F(MultiStreamInteractionTest, CaptureWithCircularDependencyFails) {
    cudaGraph_t graph = nullptr;
    cudaEvent_t event1, event2;
    CUDA_CHECK(cudaEventCreate(&event1));
    CUDA_CHECK(cudaEventCreate(&event2));

    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // Create a circular wait: stream1 waits for stream2, and stream2 waits for stream1
    CUDA_CHECK(cudaEventRecord(event2, stream2));
    CUDA_CHECK(cudaStreamWaitEvent(stream1, event2, 0));
    
    CUDA_CHECK(cudaEventRecord(event1, stream1));
    CUDA_CHECK(cudaStreamWaitEvent(stream2, event1, 0));

    // Ending the capture should fail because a cycle is detected
    cudaError_t err = cudaStreamEndCapture(stream1, &graph);
    ASSERT_NE(err, cudaSuccess) << "Capture should fail with a circular dependency";
    ASSERT_EQ(graph, nullptr);

    // Cleanup
    (void)cudaGetLastError();
    CUDA_CHECK(cudaStreamSynchronize(stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream2));
    CUDA_CHECK(cudaEventDestroy(event1));
    CUDA_CHECK(cudaEventDestroy(event2));
}

// Test that ending capture fails if a forked stream is not joined back
TEST_F(MultiStreamInteractionTest, UnjoinedStreamFailsCapture) {
    cudaGraph_t graph = nullptr;
    cudaEvent_t event_fork;
    CUDA_CHECK(cudaEventCreate(&event_fork));

    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // Fork to stream2
    CUDA_CHECK(cudaEventRecord(event_fork, stream1));
    CUDA_CHECK(cudaStreamWaitEvent(stream2, event_fork, 0));
    simple_kernel<<<1, 1, 0, stream2>>>(d_data, d_data, 1);

    // Do NOT join stream2 back to stream1
    cudaError_t err = cudaStreamEndCapture(stream1, &graph);
    ASSERT_EQ(err, cudaErrorInvalidValue) << "Capture should fail with unjoined streams";
    ASSERT_EQ(graph, nullptr);

    // To clean up, we need to synchronize both streams to finish operations
    CUDA_CHECK(cudaStreamSynchronize(stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream2));
    CUDA_CHECK(cudaEventDestroy(event_fork));
}

// Test that calling a blocking sync operation inside a capture fails
TEST_F(MultiStreamInteractionTest, SyncInsideCaptureFails) {
    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    simple_kernel<<<1, 1, 0, stream1>>>(d_data, d_data, 1);

    // This is an illegal operation during capture
    cudaError_t err = cudaStreamSynchronize(stream1);
    ASSERT_EQ(err, cudaErrorInvalidResourceHandle) << "Sync inside capture should fail";

    // End capture (which will likely also fail or report the earlier error)
    cudaError_t end_err = cudaStreamEndCapture(stream1, &graph);
    ASSERT_NE(end_err, cudaSuccess);
    ASSERT_EQ(graph, nullptr);

    // Reset error state for cleanup
    (void)cudaGetLastError(); 
    CUDA_CHECK(cudaStreamSynchronize(stream1));
}

// Test that an illegal API call (cudaMallocAsync) during capture is correctly handled
TEST_F(MultiStreamInteractionTest, IllegalApiCallDuringCapture_Malloc) {
    cudaGraph_t graph = nullptr;
    float* d_temp = nullptr;

    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));
    
    cudaError_t err = cudaMallocAsync(&d_temp, bytes, stream1);
    ASSERT_EQ(err, cudaErrorInvalidValue) << "cudaMallocAsync should fail during capture";

    cudaError_t end_err = cudaStreamEndCapture(stream1, &graph);
    ASSERT_NE(end_err, cudaSuccess);
    ASSERT_EQ(graph, nullptr);

    (void)cudaGetLastError(); 
    if (d_temp) cudaFreeAsync(d_temp, stream1);
    CUDA_CHECK(cudaStreamSynchronize(stream1));
}

// Test a subtle race condition where capture succeeds but logic is flawed due to a missing wait
TEST_F(MultiStreamInteractionTest, CaptureWithRaceConditionDueToMissingWait) {
    cudaGraph_t graph = nullptr;
    cudaGraphExec_t graph_exec = nullptr;
    cudaEvent_t event_s1_done;
    CUDA_CHECK(cudaEventCreate(&event_s1_done));

    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // stream1 produces data in d_data
    simple_kernel<<<1, 256, 0, stream1>>>(d_data, d_data, h_data.size());
    CUDA_CHECK(cudaEventRecord(event_s1_done, stream1));
    
    // stream2 consumes data from d_data but we forget to wait
    // CUDA_CHECK(cudaStreamWaitEvent(stream2, event_s1_done, 0)); 
    simple_kernel<<<1, 256, 0, stream2>>>(d_data, d_data, h_data.size());

    CUDA_CHECK(cudaStreamEndCapture(stream1, &graph));
    ASSERT_NE(graph, nullptr);

    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    
    std::fill(h_data.begin(), h_data.end(), 1.0f);
    CUDA_CHECK(cudaMemcpy(d_data, h_data.data(), bytes, cudaMemcpyHostToDevice));

    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream1));

    CUDA_CHECK(cudaMemcpy(h_data.data(), d_data, bytes, cudaMemcpyDeviceToHost));
    bool is_wrong = false;
    for (size_t i = 0; i < h_data.size(); ++i) {
        if (h_data[i] != 4.0f) { 
            is_wrong = true;
            break;
        }
    }
    ASSERT_TRUE(is_wrong) << "The graph produced the correct result, but was expected to fail due to the race condition.";

    CUDA_CHECK(cudaEventDestroy(event_s1_done));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test a more complex, multi-level fork-join pattern
TEST_F(MultiStreamInteractionTest, CaptureWithMultipleInteractingForks) {
    cudaStream_t stream3, stream4;
    cudaEvent_t e1_2, e1_3, e2_4, e3_1, e4_1;
    float *d_data2, *d_data3, *d_data4;

    CUDA_CHECK(cudaStreamCreate(&stream3));
    CUDA_CHECK(cudaStreamCreate(&stream4));
    CUDA_CHECK(cudaEventCreate(&e1_2));
    CUDA_CHECK(cudaEventCreate(&e1_3));
    CUDA_CHECK(cudaEventCreate(&e2_4));
    CUDA_CHECK(cudaEventCreate(&e3_1));
    CUDA_CHECK(cudaEventCreate(&e4_1));
    CUDA_CHECK(cudaMalloc(&d_data2, bytes));
    CUDA_CHECK(cudaMalloc(&d_data3, bytes));
    CUDA_CHECK(cudaMalloc(&d_data4, bytes));

    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(stream1, cudaStreamCaptureModeGlobal));

    // S1 -> {S2, S3}
    simple_kernel<<<1, 1, 0, stream1>>>(d_data, d_data, 1);
    CUDA_CHECK(cudaEventRecord(e1_2, stream1));
    CUDA_CHECK(cudaEventRecord(e1_3, stream1));
    CUDA_CHECK(cudaStreamWaitEvent(stream2, e1_2, 0));
    CUDA_CHECK(cudaStreamWaitEvent(stream3, e1_3, 0));

    // S2 -> S4
    simple_kernel<<<1, 1, 0, stream2>>>(d_data2, d_data, 1);
    CUDA_CHECK(cudaEventRecord(e2_4, stream2));
    CUDA_CHECK(cudaStreamWaitEvent(stream4, e2_4, 0));

    // S3 -> S1
    simple_kernel<<<1, 1, 0, stream3>>>(d_data3, d_data, 1);
    CUDA_CHECK(cudaEventRecord(e3_1, stream3));
    CUDA_CHECK(cudaStreamWaitEvent(stream1, e3_1, 0));

    // S4 -> S1
    simple_kernel<<<1, 1, 0, stream4>>>(d_data4, d_data2, 1);
    CUDA_CHECK(cudaEventRecord(e4_1, stream4));
    CUDA_CHECK(cudaStreamWaitEvent(stream1, e4_1, 0));

    // Final kernel on S1
    simple_kernel<<<1, 1, 0, stream1>>>(d_data, d_data, 1);

    CUDA_CHECK(cudaStreamEndCapture(stream1, &graph));
    ASSERT_NE(graph, nullptr);

    // --- Cleanup ---
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream3));
    CUDA_CHECK(cudaStreamDestroy(stream4));
    CUDA_CHECK(cudaEventDestroy(e1_2));
    CUDA_CHECK(cudaEventDestroy(e1_3));
    CUDA_CHECK(cudaEventDestroy(e2_4));
    CUDA_CHECK(cudaEventDestroy(e3_1));
    CUDA_CHECK(cudaEventDestroy(e4_1));
    CUDA_CHECK(cudaFree(d_data2));
    CUDA_CHECK(cudaFree(d_data3));
    CUDA_CHECK(cudaFree(d_data4));
}

// Test capturing an empty graph
TEST_F(AdvancedCaptureScenariosTest, EmptyGraph) {
    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    
    ASSERT_NE(graph, nullptr);

    cudaGraphExec_t graph_exec;
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test capturing a graph with a large number of nodes
TEST_F(AdvancedCaptureScenariosTest, ManyNodesGraph) {
    const int num_nodes = 1000;
    float* d_data;
    CUDA_CHECK(cudaMalloc(&d_data, sizeof(float)));

    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    for (int i = 0; i < num_nodes; ++i) {
        simple_kernel<<<1, 1, 0, stream>>>(d_data, d_data, 1);
    }
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    size_t node_count = 0;
    CUDA_CHECK(cudaGraphGetNodes(graph, nullptr, &node_count));
    ASSERT_EQ(node_count, num_nodes);

    CUDA_CHECK(cudaFree(d_data));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test that nested captures on the same stream fail
TEST_F(AdvancedCaptureScenariosTest, NestedCaptureFails) {
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    
    cudaError_t err = cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal);
    ASSERT_EQ(err, cudaErrorInvalidValue);

    cudaGraph_t graph;
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test that ending capture without beginning fails
TEST_F(AdvancedCaptureScenariosTest, EndCaptureWithoutBeginFails) {
    cudaGraph_t graph;
    cudaError_t err = cudaStreamEndCapture(stream, &graph);
    ASSERT_EQ(err, cudaErrorInvalidValue);
}

// Test concurrent graph capture from multiple host threads
TEST_F(StressTest, ConcurrentCapture) {
    const int num_threads = 8;
    std::vector<std::thread> threads;
    std::atomic<int> success_count(0);

    auto capture_task = [&](int thread_id) {
        cudaStream_t stream;
        CUDA_CHECK(cudaStreamCreate(&stream));

        cudaGraph_t graph = nullptr;
        CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeThreadLocal));
        CUDA_CHECK(cudaMemsetAsync(nullptr, 0, thread_id + 1, stream)); 
        cudaError_t end_err = cudaStreamEndCapture(stream, &graph);

        if (end_err == cudaSuccess && graph != nullptr) {
            success_count++;
            CUDA_CHECK(cudaGraphDestroy(graph));
        }

        CUDA_CHECK(cudaStreamDestroy(stream));
    };

    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(capture_task, i);
    }

    for (auto& t : threads) {
        t.join();
    }

    ASSERT_EQ(success_count.load(), num_threads);
}

// Stress test concurrent captures of fork-join graphs
TEST_F(StressTest, ConcurrentForkJoinCapture) {
    const int num_threads = 4;
    std::vector<std::thread> threads;
    std::atomic<int> success_count(0);

    auto task = [&]() {
        cudaStream_t s1, s2;
        cudaEvent_t e1, e2;
        float* d_data;
        CUDA_CHECK(cudaStreamCreate(&s1));
        CUDA_CHECK(cudaStreamCreate(&s2));
        CUDA_CHECK(cudaEventCreate(&e1));
        CUDA_CHECK(cudaEventCreate(&e2));
        CUDA_CHECK(cudaMalloc(&d_data, 16 * sizeof(float)));

        cudaGraph_t graph = nullptr;
        CUDA_CHECK(cudaStreamBeginCapture(s1, cudaStreamCaptureModeThreadLocal));
        simple_kernel<<<1, 1, 0, s1>>>(d_data, d_data, 1);
        CUDA_CHECK(cudaEventRecord(e1, s1));
        CUDA_CHECK(cudaStreamWaitEvent(s2, e1, 0));
        simple_kernel<<<1, 1, 0, s2>>>(d_data, d_data, 1);
        CUDA_CHECK(cudaEventRecord(e2, s2));
        CUDA_CHECK(cudaStreamWaitEvent(s1, e2, 0));
        simple_kernel<<<1, 1, 0, s1>>>(d_data, d_data, 1);
        cudaError_t err = cudaStreamEndCapture(s1, &graph);

        if (err == cudaSuccess && graph != nullptr) {
            success_count++;
            CUDA_CHECK(cudaGraphDestroy(graph));
        }

        CUDA_CHECK(cudaFree(d_data));
        CUDA_CHECK(cudaEventDestroy(e1));
        CUDA_CHECK(cudaEventDestroy(e2));
        CUDA_CHECK(cudaStreamDestroy(s1));
        CUDA_CHECK(cudaStreamDestroy(s2));
    };

    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(task);
    }
    for (auto& t : threads) {
        t.join();
    }

    ASSERT_EQ(success_count.load(), num_threads);
}

// Stress test with a deep chain of stream dependencies
TEST_F(StressTest, DeeplyNestedForkJoinCapture) {
    const int depth = 50;
    std::vector<cudaStream_t> streams(depth);
    std::vector<cudaEvent_t> events(depth - 1);
    float* d_data;
    CUDA_CHECK(cudaMalloc(&d_data, sizeof(float)));

    for (int i = 0; i < depth; ++i) CUDA_CHECK(cudaStreamCreate(&streams[i]));
    for (int i = 0; i < depth - 1; ++i) CUDA_CHECK(cudaEventCreate(&events[i]));

    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(streams[0], cudaStreamCaptureModeGlobal));

    // Create a deep dependency chain
    for (int i = 0; i < depth - 1; ++i) {
        simple_kernel<<<1, 1, 0, streams[i]>>>(d_data, d_data, 1);
        CUDA_CHECK(cudaEventRecord(events[i], streams[i]));
        CUDA_CHECK(cudaStreamWaitEvent(streams[i+1], events[i], 0));
    }
    simple_kernel<<<1, 1, 0, streams[depth-1]>>>(d_data, d_data, 1);

    CUDA_CHECK(cudaStreamEndCapture(streams[0], &graph));
    ASSERT_NE(graph, nullptr);

    size_t node_count = 0;
    CUDA_CHECK(cudaGraphGetNodes(graph, nullptr, &node_count));
    ASSERT_GE(node_count, depth);

    // Cleanup
    CUDA_CHECK(cudaGraphDestroy(graph));
    for (int i = 0; i < depth; ++i) CUDA_CHECK(cudaStreamDestroy(streams[i]));
    for (int i = 0; i < depth - 1; ++i) CUDA_CHECK(cudaEventDestroy(events[i]));
    CUDA_CHECK(cudaFree(d_data));
}

// Test rapid cycles of capture, instantiate, and launch
TEST_F(StressTest, RapidCycle) {
    const int num_cycles = 100;
    float* d_data;
    CUDA_CHECK(cudaMalloc(&d_data, sizeof(float)));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));

    for (int i = 0; i < num_cycles; ++i) {
        cudaGraph_t graph = nullptr;
        cudaGraphExec_t graph_exec = nullptr;

        CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
        simple_kernel<<<1, 1, 0, stream>>>(d_data, d_data, 1);
        CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
        ASSERT_NE(graph, nullptr);

        CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
        ASSERT_NE(graph_exec, nullptr);

        CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
        
        CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
        CUDA_CHECK(cudaGraphDestroy(graph));
    }
    
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaFree(d_data));
}

// Host callback function for the Host Node test
void CUDART_CB host_callback(void* data) {
    bool* flag = static_cast<bool*>(data);
    *flag = true;
}

// Test for Memset Node
TEST_F(GraphNodeTypeTest, MemsetNode) {
    const int size = 256;
    const size_t bytes = size * sizeof(int);
    int* d_mem;
    CUDA_CHECK(cudaMalloc(&d_mem, bytes));

    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUDA_CHECK(cudaMemsetAsync(d_mem, 0xAB, bytes, stream));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    cudaGraphExec_t graph_exec;
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));
    
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    std::vector<int> h_mem(size);
    CUDA_CHECK(cudaMemcpy(h_mem.data(), d_mem, bytes, cudaMemcpyDeviceToHost));

    for (int val : h_mem) {
        ASSERT_EQ(static_cast<unsigned char>(val), 0xAB);
    }

    CUDA_CHECK(cudaFree(d_mem));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test for Host Node
TEST_F(GraphNodeTypeTest, HostNode) {
    bool host_flag = false;

    cudaGraph_t graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUDA_CHECK(cudaLaunchHostFunc(stream, host_callback, &host_flag));
    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    cudaGraphExec_t graph_exec;
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec, graph, NULL, NULL, 0));

    ASSERT_FALSE(host_flag);
    CUDA_CHECK(cudaGraphLaunch(graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    ASSERT_TRUE(host_flag);

    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphExecDestroy(graph_exec));
}

// Test for Child Graph Node
TEST_F(GraphNodeTypeTest, ChildGraphNode) {
    const int size = 256;
    const size_t bytes = size * sizeof(float);
    float *d_child_in, *d_child_out;
    std::vector<float> h_child_in(size, 2.0f), h_child_out(size, 0.0f);

    CUDA_CHECK(cudaMalloc(&d_child_in, bytes));
    CUDA_CHECK(cudaMalloc(&d_child_out, bytes));
    CUDA_CHECK(cudaMemcpy(d_child_in, h_child_in.data(), bytes, cudaMemcpyHostToDevice));

    cudaGraph_t child_graph;
    cudaStream_t child_stream;
    CUDA_CHECK(cudaStreamCreate(&child_stream));
    CUDA_CHECK(cudaStreamBeginCapture(child_stream, cudaStreamCaptureModeGlobal));
    simple_kernel<<<1, 256, 0, child_stream>>>(d_child_out, d_child_in, size);
    CUDA_CHECK(cudaStreamEndCapture(child_stream, &child_graph));
    CUDA_CHECK(cudaStreamDestroy(child_stream));

    cudaGraph_t parent_graph = nullptr;
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    CUDA_CHECK(cudaGraphLaunch(child_graph, stream));
    CUDA_CHECK(cudaStreamEndCapture(stream, &parent_graph));
    ASSERT_NE(parent_graph, nullptr);

    cudaGraphExec_t parent_graph_exec;
    CUDA_CHECK(cudaGraphInstantiate(&parent_graph_exec, parent_graph, NULL, NULL, 0));

    CUDA_CHECK(cudaGraphLaunch(parent_graph_exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaMemcpy(h_child_out.data(), d_child_out, bytes, cudaMemcpyDeviceToHost));
    for (float val : h_child_out) {
        ASSERT_FLOAT_EQ(val, 4.0f);
    }

    CUDA_CHECK(cudaFree(d_child_in));
    CUDA_CHECK(cudaFree(d_child_out));
    CUDA_CHECK(cudaGraphDestroy(child_graph));
    CUDA_CHECK(cudaGraphDestroy(parent_graph));
    CUDA_CHECK(cudaGraphExecDestroy(parent_graph_exec));
}

// Test to ensure that errors during capture are correctly reported
TEST_F(InvalidCaptureTest, ErrorHandlingDuringCapture) {
    cudaGraph_t graph = nullptr;

    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));

    simple_kernel<<<1, 0, 0, stream>>>(nullptr, nullptr, 0); // Invalid configuration

    cudaError_t capture_status = cudaStreamEndCapture(stream, &graph);
    
    ASSERT_NE(capture_status, cudaSuccess);
    ASSERT_EQ(graph, nullptr);

    cudaError_t last_error = cudaGetLastError();
    ASSERT_EQ(last_error, cudaErrorInvalidConfiguration);
}

// Test that an illegal API call (cudaFreeAsync) during capture is correctly handled
TEST_F(InvalidCaptureTest, IllegalApiCallDuringCapture_Free) {
    cudaGraph_t graph = nullptr;
    float* d_temp = nullptr;
    CUDA_CHECK(cudaMalloc(&d_temp, 128));

    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    
    cudaError_t err = cudaFreeAsync(d_temp, stream);
    ASSERT_EQ(err, cudaErrorInvalidValue) << "cudaFreeAsync should fail during capture";

    cudaError_t end_err = cudaStreamEndCapture(stream, &graph);
    ASSERT_NE(end_err, cudaSuccess);
    ASSERT_EQ(graph, nullptr);

    (void)cudaGetLastError();
    CUDA_CHECK(cudaFree(d_temp));
}

// Test that querying an event during capture fails
TEST_F(InvalidCaptureTest, CaptureWithEventQueryFails) {
    cudaEvent_t event;
    CUDA_CHECK(cudaEventCreate(&event));
    cudaGraph_t graph = nullptr;

    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    
    simple_kernel<<<1, 1, 0, stream>>>(nullptr, nullptr, 0);
    CUDA_CHECK(cudaEventRecord(event, stream));

    // Querying an event is a host-synchronizing operation and is illegal during capture
    cudaError_t err = cudaEventQuery(event);
    ASSERT_EQ(err, cudaErrorInvalidResourceHandle) << "cudaEventQuery should fail during capture";

    // End capture, which should also fail because the stream is in an error state
    cudaError_t end_err = cudaStreamEndCapture(stream, &graph);
    ASSERT_NE(end_err, cudaSuccess);
    ASSERT_EQ(graph, nullptr);

    // Cleanup
    (void)cudaGetLastError();
    CUDA_CHECK(cudaEventDestroy(event));
}

// Test that ending capture on a different stream fails in Global mode
TEST_F(InvalidCaptureTest, EndCaptureOnDifferentStreamFailsInGlobalMode) {
    cudaStream_t other_stream;
    CUDA_CHECK(cudaStreamCreate(&other_stream));
    cudaGraph_t graph = nullptr;

    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    
    cudaError_t err = cudaStreamEndCapture(other_stream, &graph);
    ASSERT_EQ(err, cudaErrorInvalidValue);

    cudaError_t end_err = cudaStreamEndCapture(stream, &graph);
    if (end_err == cudaSuccess && graph != nullptr) {
        CUDA_CHECK(cudaGraphDestroy(graph));
    } else {
        (void)cudaGetLastError();
    }
    
    CUDA_CHECK(cudaStreamDestroy(other_stream));
}

// Test to verify the behavior of cudaStreamIsCapturing
TEST_F(StreamCaptureStateTest, VerifiesIsCapturingState) {
    cudaStreamCaptureStatus capture_status;
    cudaGraph_t graph = nullptr;

    ASSERT_EQ(cudaStreamIsCapturing(stream, &capture_status), cudaErrorStreamCaptureUnsupported);

    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    ASSERT_EQ(cudaStreamIsCapturing(stream, &capture_status), cudaSuccess);
    ASSERT_EQ(capture_status, cudaStreamCaptureStatusActive);

    simple_kernel<<<1, 1, 0, stream>>>(nullptr, nullptr, 0);

    CUDA_CHECK(cudaStreamEndCapture(stream, &graph));
    ASSERT_NE(graph, nullptr);

    ASSERT_EQ(cudaStreamIsCapturing(stream, &capture_status), cudaErrorStreamCaptureUnsupported);

    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Instantiate the test suite for all capture modes
INSTANTIATE_TEST_SUITE_P(
    CaptureModes,
    GraphCaptureModeTest,
    ::testing::Values(
        cudaStreamCaptureModeGlobal,
        cudaStreamCaptureModeThreadLocal,
        cudaStreamCaptureModeRelaxed
    )
);
