#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <chrono>
#include <iostream>
#include <vector>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            std::cerr << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__ << std::endl; \
            FAIL(); \
        } \
    } while(0)

// --- KERNEL for testing ---
__global__ void simple_kernel(float* p, float v) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        *p += v;
    }
}

class PerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaStreamCreate(&stream));
        CUDA_CHECK(cudaMalloc(&d_ptr, sizeof(float)));
        float val = 0.0f;
        CUDA_CHECK(cudaMemset(d_ptr, 0, sizeof(float)));
    }

    void TearDown() override {
        CUDA_CHECK(cudaFree(d_ptr));
        CUDA_CHECK(cudaStreamDestroy(stream));
    }
    
    cudaStream_t stream;
    float* d_ptr;
};


TEST_F(PerformanceTest, DISABLED_LaunchLatencyComparison) {
    const int num_launches = 100;
    
    // --- Graph method ---
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    float val = 1.0f;
    void* args[] = {&d_ptr, &val};
    cudaKernelNodeParams params = {};
    params.func = (void*)simple_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    params.kernelParams = args;
    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    CUDA_CHECK(cudaDeviceSynchronize());
    auto start_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_launches; ++i) {
        CUDA_CHECK(cudaGraphLaunch(exec, stream));
    }
    CUDA_CHECK(cudaDeviceSynchronize());
    auto end_graph = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> graph_ms = end_graph - start_graph;
    std::cout << "\n[ PERF ] Graph launch total time (" << num_launches << " launches): " << graph_ms.count() << " ms" << std::endl;

    // --- Native stream method ---
    CUDA_CHECK(cudaDeviceSynchronize());
    auto start_native = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_launches; ++i) {
        simple_kernel<<<1, 1, 0, stream>>>(d_ptr, 1.0f);
    }
    CUDA_CHECK(cudaDeviceSynchronize());
    auto end_native = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> native_ms = end_native - start_native;
    std::cout << "[ PERF ] Native launch total time (" << num_launches << " launches): " << native_ms.count() << " ms" << std::endl;

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));

    EXPECT_LT(graph_ms.count(), native_ms.count());
}

TEST_F(PerformanceTest, DISABLED_InstantiationOverhead) {
    const int num_nodes = 100;
    
    auto start_create = std::chrono::high_resolution_clock::now();
    
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    cudaKernelNodeParams params = {};
    params.func = (void*)simple_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    for (int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, (i>0 ? &nodes[i-1] : nullptr), (i>0 ? 1 : 0), &params));
    }
    auto end_create = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> create_ms = end_create - start_create;
    std::cout << "\n[ PERF ] Graph creation time (" << num_nodes << " nodes): " << create_ms.count() << " ms" << std::endl;

    auto start_instantiate = std::chrono::high_resolution_clock::now();
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaDeviceSynchronize()); // Instantiation can be async
    auto end_instantiate = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> instantiate_ms = end_instantiate - start_instantiate;
    std::cout << "[ PERF ] Graph instantiation time (" << num_nodes << " nodes): " << instantiate_ms.count() << " ms" << std::endl;

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    
    SUCCEED();
}
