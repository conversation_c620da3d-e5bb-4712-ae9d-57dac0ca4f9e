#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <numeric>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

// --- KERNELS for this test file ---
__global__ void multiply_by_const(float *data, float c, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        data[i] = data[i] * c;
    }
}

__global__ void add_vectors_inplace(float *a, const float *b, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        a[i] = a[i] + b[i];
    }
}

// --- More Kernels for Complex Test ---
__global__ void normalize_data(float* data, float factor, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        data[i] *= factor;
    }
}

__global__ void process_data(float* out, const float* in, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        out[i] = in[i] * in[i]; // e.g., square the elements
    }
}


class FunctionalTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Calling cudaFree(nullptr) is a standard, lightweight way to initialize the
        // CUDA context and check for any existing sticky errors on the current thread.
        CUDA_CHECK(cudaFree(nullptr));
        
        CUDA_CHECK(cudaStreamCreate(&stream));
        n = 256;
        size = n * sizeof(float);
        CUDA_CHECK(cudaMalloc(&d_a, size));
        CUDA_CHECK(cudaMalloc(&d_b, size));
        CUDA_CHECK(cudaMalloc(&d_c, size));

        std::vector<float> h_a(n, 1.0f);
        std::vector<float> h_b(n, 2.0f);
        CUDA_CHECK(cudaMemcpy(d_a, h_a.data(), size, cudaMemcpyHostToDevice));
        CUDA_CHECK(cudaMemcpy(d_b, h_b.data(), size, cudaMemcpyHostToDevice));
    }

    void TearDown() override {
        CUDA_CHECK(cudaFree(d_a));
        CUDA_CHECK(cudaFree(d_b));
        CUDA_CHECK(cudaFree(d_c));
        CUDA_CHECK(cudaStreamDestroy(stream));
    }

    cudaStream_t stream;
    float *d_a, *d_b, *d_c;
    int n;
    size_t size;
};

// Test a graph with a structure:
//      -> NodeB --
// NodeA          --> NodeC (d_a += d_b)
TEST_F(FunctionalTest, BranchAndMerge) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    // Node A: d_a *= 10.0
    float const1 = 10.0f;
    void* argsA[] = {&d_a, &const1, &n};
    cudaKernelNodeParams paramsA = {};
    paramsA.func = (void*)multiply_by_const;
    paramsA.gridDim = {1,1,1};
    paramsA.blockDim = {256,1,1};
    paramsA.kernelParams = argsA;
    cudaGraphNode_t nodeA;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeA, graph, nullptr, 0, &paramsA));

    // Node B: d_b *= 20.0
    float const2 = 20.0f;
    void* argsB[] = {&d_b, &const2, &n};
    cudaKernelNodeParams paramsB = {};
    paramsB.func = (void*)multiply_by_const;
    paramsB.gridDim = {1,1,1};
    paramsB.blockDim = {256,1,1};
    paramsB.kernelParams = argsB;
    cudaGraphNode_t nodeB;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeB, graph, nullptr, 0, &paramsB));

    // Node C: d_a += d_b
    void* argsC[] = {&d_a, &d_b, &n};
    cudaKernelNodeParams paramsC = {};
    paramsC.func = (void*)add_vectors_inplace;
    paramsC.gridDim = {1,1,1};
    paramsC.blockDim = {256,1,1};
    paramsC.kernelParams = argsC;
    cudaGraphNode_t nodeC;
    cudaGraphNode_t deps[] = {nodeA, nodeB};
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeC, graph, deps, 2, &paramsC));

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    std::vector<float> h_result(n);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_a, size, cudaMemcpyDeviceToHost));

    // Verify result: a_init=1, b_init=2. After A, a=10. After B, b=40. After C, a = 10+40=50.
    for(int i=0; i<n; ++i) {
        ASSERT_FLOAT_EQ(h_result[i], 50.0f);
    }

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(FunctionalTest, UpdateMultipleNodesAtomically) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    // Another buffer needed for the test
    float* d_d;
    CUDA_CHECK(cudaMalloc(&d_d, size));

    // Graph: Memcpy(d_a -> d_c) -> Kernel(d_c *= multiplier)
    // 1. Memcpy node
    cudaGraphNode_t memcpy_node;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpy_node, graph, nullptr, 0, d_c, d_a, size, cudaMemcpyDeviceToDevice));
    
    // 2. Kernel node
    float multiplier = 10.0f;
    void* args[] = {&d_c, &multiplier, &n};
    cudaKernelNodeParams params = {};
    params.func = (void*)multiply_by_const;
    params.gridDim = {1,1,1};
    params.blockDim = {256,1,1};
    params.kernelParams = args;
    cudaGraphNode_t kernel_node;
    CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph, &memcpy_node, 1, &params));

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    // First launch (d_c = d_a * 10)
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    std::vector<float> h_c1(n);
    CUDA_CHECK(cudaMemcpy(h_c1.data(), d_c, size, cudaMemcpyDeviceToHost));
    // Initial d_a is 1.0f, so d_c becomes 10.0f
    for(int i=0; i<n; ++i) ASSERT_FLOAT_EQ(h_c1[i], 10.0f);

    // Atomically update both nodes:
    // - Memcpy now copies d_b to d_d.
    // - Kernel now operates on d_d with multiplier 0.5.
    multiplier = 0.5f;
    args[0] = &d_d; // Point kernel to the new buffer d_d
    
    // The C++ standard does not guarantee the order of evaluation of function arguments.
    // To ensure the updates are applied correctly and to avoid relying on undefined behavior,
    // we should call the update functions sequentially. The runtime itself should handle
    // batching these updates if it supports such a feature, but the test code should be explicit.
    CUDA_CHECK(cudaGraphExecMemcpyNodeSetParams1D(exec, memcpy_node, d_d, d_b, size, cudaMemcpyDeviceToDevice));
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, kernel_node, &params));

    // Second launch
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Verify:
    // d_b (all 2.0f) is copied to d_d.
    // kernel operates on d_d: 2.0 * 0.5 = 1.0.
    std::vector<float> h_d(n);
    CUDA_CHECK(cudaMemcpy(h_d.data(), d_d, size, cudaMemcpyDeviceToHost));
    for(int i=0; i<n; ++i) ASSERT_FLOAT_EQ(h_d[i], 1.0f);

    CUDA_CHECK(cudaFree(d_d));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}
>>>>>>> REPLACE

TEST_F(FunctionalTest, MemcpyNodeParameterUpdate) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    // We need another buffer
    float* d_d;
    CUDA_CHECK(cudaMalloc(&d_d, size));

    // Initial graph: d_a -> d_c
    cudaGraphNode_t memcpy_node;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpy_node, graph, nullptr, 0, d_c, d_a, size, cudaMemcpyDeviceToDevice));
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    // First launch
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    std::vector<float> h_c_1(n);
    CUDA_CHECK(cudaMemcpy(h_c_1.data(), d_c, size, cudaMemcpyDeviceToHost));
    for(int i=0; i<n; ++i) ASSERT_FLOAT_EQ(h_c_1[i], 1.0f); // Initially d_a is all 1.0f

    // Update the memcpy node to copy to d_d instead of d_c
    CUDA_CHECK(cudaGraphExecMemcpyNodeSetParams1D(exec, memcpy_node, d_d, d_a, size, cudaMemcpyDeviceToDevice));

    // Second launch
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Verify d_d has the data, and d_c is unchanged
    std::vector<float> h_d(n);
    std::vector<float> h_c_2(n);
    CUDA_CHECK(cudaMemcpy(h_d.data(), d_d, size, cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(h_c_2.data(), d_c, size, cudaMemcpyDeviceToHost));

    for(int i=0; i<n; ++i) {
        ASSERT_FLOAT_EQ(h_d[i], 1.0f); // New destination has data
        ASSERT_FLOAT_EQ(h_c_2[i], 1.0f); // Old destination is unchanged from first launch
    }

    CUDA_CHECK(cudaFree(d_d));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(FunctionalTest, GraphExecUpdateWithTopologyChange) {
    cudaGraphExec_t exec = nullptr;
    
    // --- Graph 1: a *= 10 ---
    cudaGraph_t graph1;
    CUDA_CHECK(cudaGraphCreate(&graph1, 0));
    float multiplier1 = 10.0f;
    void* args1[] = {&d_a, &multiplier1, &n};
    cudaKernelNodeParams params1 = {};
    params1.func = (void*)multiply_by_const;
    params1.gridDim={1,1,1}; params1.blockDim={256,1,1}; params1.kernelParams=args1;
    CUDA_CHECK(cudaGraphAddKernelNode(nullptr, graph1, nullptr, 0, &params1));
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph1, nullptr, nullptr, 0));
    
    // Launch original graph
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    std::vector<float> h_a1(n);
    CUDA_CHECK(cudaMemcpy(h_a1.data(), d_a, size, cudaMemcpyDeviceToHost));
    ASSERT_FLOAT_EQ(h_a1[0], 10.0f);

    // --- Graph 2: a += b (different topology) ---
    cudaGraph_t graph2;
    CUDA_CHECK(cudaGraphCreate(&graph2, 0));
    void* args2[] = {&d_a, &d_b, &n};
    cudaKernelNodeParams params2 = {};
    params2.func = (void*)add_vectors_inplace;
    params2.gridDim={1,1,1}; params2.blockDim={256,1,1}; params2.kernelParams=args2;
    CUDA_CHECK(cudaGraphAddKernelNode(nullptr, graph2, nullptr, 0, &params2));

    // --- Update the executable with the new topology ---
    cudaGraphExecUpdateResult update_result;
    cudaError_t update_err;
    CUDA_CHECK(cudaGraphExecUpdate(exec, graph2, nullptr, &update_result, &update_err));
    
    ASSERT_EQ(update_err, cudaSuccess);
    ASSERT_EQ(update_result, cudaGraphExecUpdateSuccess);

    // Launch updated graph
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    std::vector<float> h_a2(n);
    CUDA_CHECK(cudaMemcpy(h_a2.data(), d_a, size, cudaMemcpyDeviceToHost));
    // Verify new logic: a (which is 10) + b (which is 2) = 12
    ASSERT_FLOAT_EQ(h_a2[0], 12.0f);

    CUDA_CHECK(cudaGraphDestroy(graph1));
    CUDA_CHECK(cudaGraphDestroy(graph2));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
}

TEST_F(FunctionalTest, ChainedKernelExecution) {
    // This test models a simple pipeline:
    // normalize_data(d_a) -> process_data(d_a -> d_c)
    std::vector<float> h_a(n, 2.0f); // initial data
    CUDA_CHECK(cudaMemcpy(d_a, h_a.data(), size, cudaMemcpyHostToDevice));

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    // Node 1: Normalize d_a
    float norm_factor = 0.5f;
    void* args1[] = {&d_a, &norm_factor, &n};
    cudaKernelNodeParams params1 = {};
    params1.func = (void*)normalize_data;
    params1.gridDim = {1,1,1};
    params1.blockDim = {256,1,1};
    params1.kernelParams = args1;
    cudaGraphNode_t node1;
    CUDA_CHECK(cudaGraphAddKernelNode(&node1, graph, nullptr, 0, &params1));

    // Node 2: Process d_a into d_c, depends on Node 1
    void* args2[] = {&d_c, &d_a, &n};
    cudaKernelNodeParams params2 = {};
    params2.func = (void*)process_data;
    params2.gridDim = {1,1,1};
    params2.blockDim = {256,1,1};
    params2.kernelParams = args2;
    cudaGraphNode_t node2;
    CUDA_CHECK(cudaGraphAddKernelNode(&node2, graph, &node1, 1, &params2));

    // Instantiate and Launch
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Verify
    std::vector<float> h_c(n);
    CUDA_CHECK(cudaMemcpy(h_c.data(), d_c, size, cudaMemcpyDeviceToHost));

    // Expected: val = 2.0 * 0.5 = 1.0. Then, 1.0 * 1.0 = 1.0
    for(int i=0; i<n; ++i) {
        ASSERT_FLOAT_EQ(h_c[i], 1.0f);
    }

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(FunctionalTest, KernelParameterUpdate) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    // Kernel arguments with initial multiplier
    float multiplier = 10.0f;
    void* args[] = {&d_a, &multiplier, &n};
    cudaKernelNodeParams params = {};
    params.func = (void*)multiply_by_const;
    params.gridDim = {1,1,1};
    params.blockDim = {256,1,1};
    params.kernelParams = args;
    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    // First launch (multiplier = 10.0)
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    std::vector<float> h_result1(n);
    CUDA_CHECK(cudaMemcpy(h_result1.data(), d_a, size, cudaMemcpyDeviceToHost));
    for(int i=0; i<n; ++i) ASSERT_FLOAT_EQ(h_result1[i], 10.0f);


    // Update parameters (multiplier = 0.5)
    multiplier = 0.5f; 
    // We need to create a new params struct for the update API.
    cudaKernelNodeParams new_params = params;
    new_params.kernelParams = args; // The args array already points to the updated 'multiplier'
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, node, &new_params));

    // Second launch
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    std::vector<float> h_result2(n);
    CUDA_CHECK(cudaMemcpy(h_result2.data(), d_a, size, cudaMemcpyDeviceToHost));
    // Result should be 10.0 * 0.5 = 5.0
    for(int i=0; i<n; ++i) ASSERT_FLOAT_EQ(h_result2[i], 5.0f);

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}
