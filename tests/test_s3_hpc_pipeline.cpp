#include "common.h"
#include <vector>

// --- CFD Pipeline Kernel Simulations ---

// Stage 1: Assemble matrix and vectors
__global__ void assemble_kernel(float* data, int size) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < size) {
        data[i] = (data[i] * 0.5f) + 1.0f; // Simplified assembly operation
    }
}

// Stage 2: Jacobi iterative solver
__global__ void jacobi_solver_kernel(const float* data, float* next_sol, int size) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i > 0 && i < size - 1) {
        // Simple stencil operation
        next_sol[i] = (data[i-1] + data[i] + data[i+1]) / 3.0f;
    }
}

// Stage 3: Post-process results
__global__ void postprocess_kernel(const float* data, float* results, int size) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < size) {
        results[i] = data[i] * 2.0f - 0.5f; // Simplified post-processing
    }
}

// --- Test Implementations ---

void cfd_pipeline_workload(float* d_data, float* d_next_sol, float* d_results, int data_size, int jacobi_iterations, cudaStream_t stream) {
    const int block_size = 256;
    const int grid_size = (data_size + block_size - 1) / block_size;

    assemble_kernel<<<grid_size, block_size, 0, stream>>>(d_data, data_size);
    
    for (int j = 0; j < jacobi_iterations; ++j) {
        float* in = (j % 2 == 0) ? d_data : d_next_sol;
        float* out = (j % 2 == 0) ? d_next_sol : d_data;
        jacobi_solver_kernel<<<grid_size, block_size, 0, stream>>>(in, out, data_size);
    }
    
    float* final_data = (jacobi_iterations % 2 == 0) ? d_data : d_next_sol;
    postprocess_kernel<<<grid_size, block_size, 0, stream>>>(final_data, d_results, data_size);
}

void run_hpc_pipeline_test() {
    const int data_size = 1024 * 1024 * 16; // 16M elements
    const int jacobi_iterations = 10;
    const int pipeline_iterations = 50;
    const int warmup_iterations = 5;

    float *d_data, *d_next_sol, *d_results;
    CHECK_CUDA(cudaMalloc(&d_data, data_size * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_next_sol, data_size * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_results, data_size * sizeof(float)));

    CHECK_CUDA(cudaMemset(d_data, 0, data_size * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_next_sol, 0, data_size * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_results, 0, data_size * sizeof(float)));

    Timer timer;
    std::vector<double> baseline_timings, graph_timings;
    cudaStream_t stream;
    CHECK_CUDA(cudaStreamCreate(&stream));

    // --- Baseline: Sequential Kernel Launch on a single stream ---
    for (int i = 0; i < pipeline_iterations + warmup_iterations; ++i) {
        timer.start();
        cfd_pipeline_workload(d_data, d_next_sol, d_results, data_size, jacobi_iterations, stream);
        CHECK_CUDA(cudaStreamSynchronize(stream));
        timer.stop();
        if (i >= warmup_iterations) {
            baseline_timings.push_back(timer.elapsed_ms());
        }
    }

    // --- Correctness Verification (Part 1) ---
    std::vector<float> h_baseline_result(data_size);
    CHECK_CUDA(cudaMemcpy(h_baseline_result.data(), d_results, data_size * sizeof(float), cudaMemcpyDeviceToHost));

    // Reset data for graph run
    CHECK_CUDA(cudaMemset(d_data, 0, data_size * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_next_sol, 0, data_size * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_results, 0, data_size * sizeof(float)));

    // --- Graph Mode ---
    cudaGraph_t graph;
    cudaGraphExec_t instance;

    CHECK_CUDA(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    cfd_pipeline_workload(d_data, d_next_sol, d_results, data_size, jacobi_iterations, stream);
    CHECK_CUDA(cudaStreamEndCapture(stream, &graph));
    CHECK_CUDA(cudaGraphInstantiate(&instance, graph, NULL, NULL, 0));

    for (int i = 0; i < pipeline_iterations + warmup_iterations; ++i) {
        timer.start();
        CHECK_CUDA(cudaGraphLaunch(instance, stream));
        CHECK_CUDA(cudaStreamSynchronize(stream));
        timer.stop();
        if (i >= warmup_iterations) {
            graph_timings.push_back(timer.elapsed_ms());
        }
    }

    // --- Correctness Verification (Part 2) ---
    std::vector<float> h_graph_result(data_size);
    CHECK_CUDA(cudaMemcpy(h_graph_result.data(), d_results, data_size * sizeof(float), cudaMemcpyDeviceToHost));
    
    bool correct = true;
    for (size_t i = 0; i < data_size; ++i) {
        if (std::abs(h_baseline_result[i] - h_graph_result[i]) > 1e-5) {
            correct = false;
            break;
        }
    }

    // --- Analysis ---
    std::cout << "\n--- S3: HPC Pipeline Test ---" << std::endl;
    print_stats("Baseline (Launch)", baseline_timings);
    print_stats("Graph", graph_timings);

    double baseline_avg_time = std::accumulate(baseline_timings.begin(), baseline_timings.end(), 0.0) / baseline_timings.size();
    double graph_avg_time = std::accumulate(graph_timings.begin(), graph_timings.end(), 0.0) / graph_timings.size();
    double speedup = baseline_avg_time / graph_avg_time;

    std::cout << "\n--- Results & Pass Criteria ---" << std::endl;
    std::cout << "Correctness Check: " << (correct ? "PASS" : "FAIL") << std::endl;
    std::cout << "Speedup: " << std::fixed << std::setprecision(2) << speedup << "x" << std::endl;
    
    if (speedup >= 1.3 && correct) {
        std::cout << "Overall Result: PASS" << std::endl;
    } else {
        std::cout << "Overall Result: FAIL" << std::endl;
    }
    std::cout << "--------------------------------------------------\n" << std::endl;

    // Cleanup
    CHECK_CUDA(cudaGraphExecDestroy(instance));
    CHECK_CUDA(cudaGraphDestroy(graph));
    CHECK_CUDA(cudaStreamDestroy(stream));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_next_sol));
    CHECK_CUDA(cudaFree(d_results));
}

TEST(S3_HpcPipeline, Performance) {
    run_hpc_pipeline_test();
}
