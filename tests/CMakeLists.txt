# Enable CUDA for this directory
set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY CUDA_STANDARD 17)

# --- Define the main test executable ---
add_executable(CudaGraphTests
    main.cpp
    test_unit_api.cpp
    test_integration_basic.cpp
    test_functional_complex.cpp
    test_error_handling.cpp
    test_performance.cpp
    test_stress.cpp
    test_multi_gpu.cpp
    test_stream_capture.cpp
    test_multi_stream.cpp
    test_user_object_comprehensive.cpp
)

# --- Define the new extended stress test executable ---
add_executable(CudaGraphExtendedStressTests
    test_stress_extended.cpp
)

# --- Define the comprehensive Graph API SetParams test executable ---
add_executable(CudaGraphApiSetParamsTests
    test_graph_api_set_params.cpp
)

# --- Define the Graph Exec SetParams test executable ---
add_executable(CudaGraphExecSetParamsTests
    test_graph_exec_set_params.cpp
)

# --- Define the Manual Graph Performance test executable ---
add_executable(CudaGraphManualPerfTests
    test_perf_manual_graph.cpp
)

# --- Link against GoogleTest and CUDA ---
# We'll use FetchContent to get GoogleTest for simplicity
include(FetchContent)
FetchContent_Declare(
  googletest
  URL https://github.com/google/googletest/archive/refs/tags/v1.14.0.zip
)
FetchContent_MakeAvailable(googletest)

target_link_libraries(CudaGraphTests PRIVATE
    gtest_main
    CUDA::cudart
)

target_link_libraries(CudaGraphExtendedStressTests PRIVATE
    gtest_main
    CUDA::cudart
)

target_link_libraries(CudaGraphApiSetParamsTests PRIVATE
    gtest_main
    CUDA::cudart
)

target_link_libraries(CudaGraphExecSetParamsTests PRIVATE
    gtest_main
    CUDA::cudart
)

target_link_libraries(CudaGraphManualPerfTests PRIVATE
    gtest_main
    CUDA::cudart
)

# --- Define the comprehensive Graph Capture test executable ---
add_executable(CudaGraphCaptureComprehensiveTests
    test_graph_capture_comprehensive.cpp
)

target_link_libraries(CudaGraphCaptureComprehensiveTests PRIVATE
    gtest_main
    CUDA::cudart
)

# --- Define the cuBLAS Graph Capture test executable ---
add_executable(CudaGraphCublasCaptureTests
    test_graph_capture_cublas.cpp
)

target_link_libraries(CudaGraphCublasCaptureTests PRIVATE
    gtest_main
    CUDA::cudart
    CUDA::cublas
)

# --- Define the cuDNN Graph Capture test executable ---
add_executable(CudaGraphCudnnCaptureTests
    test_graph_capture_cudnn.cpp
)

target_link_libraries(CudaGraphCudnnCaptureTests PRIVATE
    gtest_main
    CUDA::cudart
    CUDA::cudnn
)

# --- Define the Detailed Performance test executable ---
add_executable(CudaGraphDetailedPerfTests
    test_perf_detailed.cpp
)

target_link_libraries(CudaGraphDetailedPerfTests PRIVATE
    gtest_main
    CUDA::cudart
)

# --- Define the S1 Static Inference test executable ---
add_executable(CudaGraphS1InferenceTests
    test_s1_static_inference.cpp
)

target_link_libraries(CudaGraphS1InferenceTests PRIVATE
    CUDA::cudart
)

# --- Define the S2 Training Loop test executable ---
add_executable(CudaGraphS2TrainingLoopTests
    test_s2_training_loop.cpp
)

target_link_libraries(CudaGraphS2TrainingLoopTests PRIVATE
    CUDA::cudart
)

# --- Define the S3 HPC Pipeline test executable ---
add_executable(CudaGraphS3HpcPipelineTests
    test_s3_hpc_pipeline.cpp
)

target_link_libraries(CudaGraphS3HpcPipelineTests PRIVATE
    CUDA::cudart
)

# --- Define the S4 Streaming Media test executable ---
add_executable(CudaGraphS4StreamingMediaTests
    test_s4_streaming_media.cpp
)

target_link_libraries(CudaGraphS4StreamingMediaTests PRIVATE
    CUDA::cudart
)

# --- Define the S5 Multi-Tenant test executable ---
add_executable(CudaGraphS5MultiTenantTests
    test_s5_multi_tenant.cpp
)

target_link_libraries(CudaGraphS5MultiTenantTests PRIVATE
    CUDA::cudart
)

# --- Define the Custom Runtime test framework (excluded from default build) ---
add_executable(CudaGraphCustomRuntimeFramework
    test_custom_runtime_framework.cpp
)

target_link_libraries(CudaGraphCustomRuntimeFramework PRIVATE
    gtest_main
    CUDA::cudart
)

set_target_properties(CudaGraphCustomRuntimeFramework PROPERTIES
    EXCLUDE_FROM_ALL TRUE
)

# --- Add tests to CTest for discovery ---
include(GoogleTest)
gtest_discover_tests(CudaGraphTests)
gtest_discover_tests(CudaGraphExtendedStressTests)
gtest_discover_tests(CudaGraphApiSetParamsTests)
gtest_discover_tests(CudaGraphExecSetParamsTests)
gtest_discover_tests(CudaGraphManualPerfTests)
gtest_discover_tests(CudaGraphCaptureComprehensiveTests)
gtest_discover_tests(CudaGraphCublasCaptureTests)
gtest_discover_tests(CudaGraphCudnnCaptureTests)
gtest_discover_tests(CudaGraphDetailedPerfTests)
gtest_discover_tests(CudaGraphS1InferenceTests)
gtest_discover_tests(CudaGraphS2TrainingLoopTests)
gtest_discover_tests(CudaGraphS3HpcPipelineTests)
gtest_discover_tests(CudaGraphS4StreamingMediaTests)
gtest_discover_tests(CudaGraphS5MultiTenantTests)

# --- Define the Graph Exec Get Flags test executable ---
add_executable(CudaGraphExecGetFlagsTests
    test_graph_exec_get_flags.cpp
)

target_link_libraries(CudaGraphExecGetFlagsTests PRIVATE
    gtest_main
    CUDA::cudart
)

# --- Define the Graph Get Edges test executable ---
add_executable(CudaGraphGetEdgesTests
    test_graph_get_edges.cpp
)

target_link_libraries(CudaGraphGetEdgesTests PRIVATE
    gtest_main
    CUDA::cudart
)

# --- Define the Graph Exec Node Set Params test executable ---
add_executable(CudaGraphExecNodeSetParamsTests
    test_graph_exec_node_set_params.cpp
)

target_link_libraries(CudaGraphExecNodeSetParamsTests PRIVATE
    gtest_main
    CUDA::cudart
)

# --- Define the Graph Node Get Dependent Nodes test executable ---
add_executable(CudaGraphNodeGetDependentNodesTests
    test_graph_node_get_dependent_nodes.cpp
)

target_link_libraries(CudaGraphNodeGetDependentNodesTests PRIVATE
    gtest_main
    CUDA::cudart
)

# --- Add new tests to CTest for discovery ---
gtest_discover_tests(CudaGraphExecGetFlagsTests)
gtest_discover_tests(CudaGraphGetEdgesTests)
gtest_discover_tests(CudaGraphExecNodeSetParamsTests)
gtest_discover_tests(CudaGraphNodeGetDependentNodesTests)
