#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <set>
#include <algorithm>
#include <thread>
#include <future>
#include <atomic>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

__global__ void kernel_a(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = data[idx] * 2.0f;
    }
}

__global__ void kernel_b(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = data[idx] + 1.0f;
    }
}

__global__ void kernel_c(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = sqrtf(data[idx]);
    }
}

void CUDART_CB host_callback(void* data) {
    int* counter = static_cast<int*>(data);
    (*counter)++;
}

class GraphNodeGetDependentNodesTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaFree(nullptr));
        
        CUDA_CHECK(cudaMalloc(&d_data, data_size * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&d_temp1, data_size * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&d_temp2, data_size * sizeof(float)));
        
        h_data.resize(data_size);
        for (int i = 0; i < data_size; i++) {
            h_data[i] = static_cast<float>(i);
        }
        CUDA_CHECK(cudaMemcpy(d_data, h_data.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
        
        CUDA_CHECK(cudaStreamCreate(&stream));
        CUDA_CHECK(cudaEventCreate(&event));
        
        kernel_args[0] = &d_data;
        kernel_args[1] = &data_size;
        
        host_counter = 0;
    }
    
    void TearDown() override {
        if (d_data) cudaFree(d_data);
        if (d_temp1) cudaFree(d_temp1);
        if (d_temp2) cudaFree(d_temp2);
        if (stream) cudaStreamDestroy(stream);
        if (event) cudaEventDestroy(event);
    }
    
    void CreateSimpleChain(cudaGraph_t* graph, std::vector<cudaGraphNode_t>* nodes) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        nodes->resize(3);
        
        cudaKernelNodeParams kernelParams1 = {};
        kernelParams1.func = (void*)kernel_a;
        kernelParams1.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams1.blockDim = {256, 1, 1};
        kernelParams1.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[0], *graph, nullptr, 0, &kernelParams1));
        
        cudaKernelNodeParams kernelParams2 = {};
        kernelParams2.func = (void*)kernel_b;
        kernelParams2.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams2.blockDim = {256, 1, 1};
        kernelParams2.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[1], *graph, &(*nodes)[0], 1, &kernelParams2));
        
        cudaKernelNodeParams kernelParams3 = {};
        kernelParams3.func = (void*)kernel_c;
        kernelParams3.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams3.blockDim = {256, 1, 1};
        kernelParams3.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[2], *graph, &(*nodes)[1], 1, &kernelParams3));
    }
    
    void CreateStarTopology(cudaGraph_t* graph, std::vector<cudaGraphNode_t>* nodes, int num_leaves) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        nodes->resize(num_leaves + 1);
        
        cudaKernelNodeParams rootParams = {};
        rootParams.func = (void*)kernel_a;
        rootParams.gridDim = {(data_size + 255) / 256, 1, 1};
        rootParams.blockDim = {256, 1, 1};
        rootParams.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[0], *graph, nullptr, 0, &rootParams));
        
        cudaKernelNodeParams leafParams = {};
        leafParams.func = (void*)kernel_b;
        leafParams.gridDim = {(data_size + 255) / 256, 1, 1};
        leafParams.blockDim = {256, 1, 1};
        leafParams.kernelParams = (void**)kernel_args;
        
        for (int i = 1; i <= num_leaves; i++) {
            CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[i], *graph, &(*nodes)[0], 1, &leafParams));
        }
    }
    
    void CreateDiamondTopology(cudaGraph_t* graph, std::vector<cudaGraphNode_t>* nodes) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        nodes->resize(4);
        
        cudaKernelNodeParams kernelParams = {};
        kernelParams.func = (void*)kernel_a;
        kernelParams.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams.blockDim = {256, 1, 1};
        kernelParams.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[0], *graph, nullptr, 0, &kernelParams));
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[1], *graph, &(*nodes)[0], 1, &kernelParams));
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[2], *graph, &(*nodes)[0], 1, &kernelParams));
        
        cudaGraphNode_t deps[] = {(*nodes)[1], (*nodes)[2]};
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[3], *graph, deps, 2, &kernelParams));
    }
    
    void CreateComplexGraph(cudaGraph_t* graph, std::vector<cudaGraphNode_t>* nodes) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        nodes->resize(10);
        
        CUDA_CHECK(cudaGraphAddMemcpyNode1D(&(*nodes)[0], *graph, nullptr, 0, d_temp1, d_data, 
                                          data_size * sizeof(float), cudaMemcpyDeviceToDevice));
        
        cudaKernelNodeParams kernelParams1 = {};
        kernelParams1.func = (void*)kernel_a;
        kernelParams1.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams1.blockDim = {256, 1, 1};
        kernelParams1.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[1], *graph, &(*nodes)[0], 1, &kernelParams1));
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[2], *graph, &(*nodes)[0], 1, &kernelParams1));
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[3], *graph, &(*nodes)[0], 1, &kernelParams1));
        
        cudaKernelNodeParams kernelParams2 = {};
        kernelParams2.func = (void*)kernel_b;
        kernelParams2.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams2.blockDim = {256, 1, 1};
        kernelParams2.kernelParams = (void**)kernel_args;
        
        cudaGraphNode_t deps1[] = {(*nodes)[1], (*nodes)[2]};
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[4], *graph, deps1, 2, &kernelParams2));
        
        cudaGraphNode_t deps2[] = {(*nodes)[2], (*nodes)[3]};
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[5], *graph, deps2, 2, &kernelParams2));
        
        CUDA_CHECK(cudaGraphAddEventRecordNode(&(*nodes)[6], *graph, &(*nodes)[4], 1, event));
        
        cudaHostNodeParams hostParams = {};
        hostParams.fn = host_callback;
        hostParams.userData = &host_counter;
        CUDA_CHECK(cudaGraphAddHostNode(&(*nodes)[7], *graph, &(*nodes)[6], 1, &hostParams));
        
        cudaMemsetParams memsetParams = {};
        memsetParams.dst = d_temp2;
        memsetParams.value = 0;
        memsetParams.elementSize = sizeof(float);
        memsetParams.width = data_size;
        memsetParams.height = 1;
        CUDA_CHECK(cudaGraphAddMemsetNode(&(*nodes)[8], *graph, &(*nodes)[7], 1, &memsetParams));
        
        cudaGraphNode_t final_deps[] = {(*nodes)[5], (*nodes)[8]};
        CUDA_CHECK(cudaGraphAddEmptyNode(&(*nodes)[9], *graph, final_deps, 2));
    }
    
    bool ContainsNode(const std::vector<cudaGraphNode_t>& nodes, cudaGraphNode_t target) {
        return std::find(nodes.begin(), nodes.end(), target) != nodes.end();
    }
    
    std::set<cudaGraphNode_t> VectorToSet(const std::vector<cudaGraphNode_t>& vec, size_t count) {
        return std::set<cudaGraphNode_t>(vec.begin(), vec.begin() + count);
    }
    
    const int data_size = 1024;
    float* d_data = nullptr;
    float* d_temp1 = nullptr;
    float* d_temp2 = nullptr;
    std::vector<float> h_data;
    cudaStream_t stream = nullptr;
    cudaEvent_t event = nullptr;
    void* kernel_args[2];
    int host_counter = 0;
};

// Test 1: Basic functionality - Simple chain
TEST_F(GraphNodeGetDependentNodesTest, BasicChainTopology) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateSimpleChain(&graph, &nodes);
    
    // Test node 0 (should have node 1 as dependent)
    size_t num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 1);
    
    std::vector<cudaGraphNode_t> dependents(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], dependents.data(), &num_dependents));
    EXPECT_EQ(dependents[0], nodes[1]);
    
    // Test node 1 (should have node 2 as dependent)
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[1], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 1);
    
    dependents.resize(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[1], dependents.data(), &num_dependents));
    EXPECT_EQ(dependents[0], nodes[2]);
    
    // Test node 2 (should have no dependents)
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[2], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 0);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 2: Empty graph
TEST_F(GraphNodeGetDependentNodesTest, EmptyGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    // No nodes to test
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 3: Single node (no dependents)
TEST_F(GraphNodeGetDependentNodesTest, SingleNodeGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaGraphNode_t node;
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &kernelParams));
    
    size_t num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(node, nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 0);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 4: Star topology
TEST_F(GraphNodeGetDependentNodesTest, StarTopology) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    const int num_leaves = 5;
    
    CreateStarTopology(&graph, &nodes, num_leaves);
    
    // Root node should have all leaf nodes as dependents
    size_t num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, num_leaves);
    
    std::vector<cudaGraphNode_t> dependents(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], dependents.data(), &num_dependents));
    
    // Verify all leaf nodes are present
    for (int i = 1; i <= num_leaves; i++) {
        EXPECT_TRUE(ContainsNode(dependents, nodes[i]));
    }
    
    // Leaf nodes should have no dependents
    for (int i = 1; i <= num_leaves; i++) {
        num_dependents = 0;
        CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[i], nullptr, &num_dependents));
        EXPECT_EQ(num_dependents, 0);
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 5: Diamond topology
TEST_F(GraphNodeGetDependentNodesTest, DiamondTopology) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateDiamondTopology(&graph, &nodes);
    
    // Node 0 should have nodes 1 and 2 as dependents
    size_t num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 2);
    
    std::vector<cudaGraphNode_t> dependents(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], dependents.data(), &num_dependents));
    
    EXPECT_TRUE(ContainsNode(dependents, nodes[1]));
    EXPECT_TRUE(ContainsNode(dependents, nodes[2]));
    
    // Node 1 should have node 3 as dependent
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[1], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 1);
    
    dependents.resize(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[1], dependents.data(), &num_dependents));
    EXPECT_EQ(dependents[0], nodes[3]);
    
    // Node 2 should have node 3 as dependent
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[2], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 1);
    
    dependents.resize(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[2], dependents.data(), &num_dependents));
    EXPECT_EQ(dependents[0], nodes[3]);
    
    // Node 3 should have no dependents
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[3], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 0);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 6: Complex graph with multiple node types
TEST_F(GraphNodeGetDependentNodesTest, ComplexGraphDependents) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&graph, &nodes);
    
    // Node 0 (memcpy) should have nodes 1, 2, 3 as dependents
    size_t num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 3);
    
    std::vector<cudaGraphNode_t> dependents(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], dependents.data(), &num_dependents));
    
    EXPECT_TRUE(ContainsNode(dependents, nodes[1]));
    EXPECT_TRUE(ContainsNode(dependents, nodes[2]));
    EXPECT_TRUE(ContainsNode(dependents, nodes[3]));
    
    // Node 1 should have node 4 as dependent
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[1], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 1);
    
    dependents.resize(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[1], dependents.data(), &num_dependents));
    EXPECT_EQ(dependents[0], nodes[4]);
    
    // Node 2 should have nodes 4 and 5 as dependents
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[2], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 2);
    
    dependents.resize(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[2], dependents.data(), &num_dependents));
    
    EXPECT_TRUE(ContainsNode(dependents, nodes[4]));
    EXPECT_TRUE(ContainsNode(dependents, nodes[5]));
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 7: Null pointer validation
TEST_F(GraphNodeGetDependentNodesTest, NullPointerValidation) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateSimpleChain(&graph, &nodes);
    
    size_t num_dependents = 10;
    
    // Test with null node
    EXPECT_EQ(cudaGraphNodeGetDependentNodes(nullptr, nullptr, &num_dependents), cudaErrorInvalidValue);
    
    // Test with null count
    EXPECT_EQ(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, nullptr), cudaErrorInvalidValue);
    
    // Test with null dependents array but non-zero count (when count > 0)
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, &num_dependents)); // This should work (query count)
    
    if (num_dependents > 0) {
        EXPECT_EQ(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, &num_dependents), cudaErrorInvalidValue);
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 8: Invalid node handle
TEST_F(GraphNodeGetDependentNodesTest, InvalidNodeHandle) {
    size_t num_dependents = 0;
    
    cudaGraphNode_t invalid_node = nullptr;
    EXPECT_EQ(cudaGraphNodeGetDependentNodes(invalid_node, nullptr, &num_dependents), cudaErrorInvalidValue);
    
    cudaGraphNode_t fake_node = reinterpret_cast<cudaGraphNode_t>(0xDEADBEEF);
    EXPECT_NE(cudaGraphNodeGetDependentNodes(fake_node, nullptr, &num_dependents), cudaSuccess);
}

// Test 9: Buffer size validation
TEST_F(GraphNodeGetDependentNodesTest, BufferSizeValidation) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateStarTopology(&graph, &nodes, 5);
    
    size_t actual_num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, &actual_num_dependents));
    
    if (actual_num_dependents > 0) {
        // Buffer too small
        std::vector<cudaGraphNode_t> small_buffer(actual_num_dependents - 1);
        size_t smaller_count = actual_num_dependents - 1;
        
        cudaError_t result = cudaGraphNodeGetDependentNodes(nodes[0], small_buffer.data(), &smaller_count);
        EXPECT_EQ(result, cudaErrorInvalidValue);
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 10: Linear chain topology
TEST_F(GraphNodeGetDependentNodesTest, LinearChainTopology) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    const int chain_length = 10;
    std::vector<cudaGraphNode_t> nodes(chain_length);
    
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[0], graph, nullptr, 0, &kernelParams));
    
    for (int i = 1; i < chain_length; i++) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, &nodes[i-1], 1, &kernelParams));
    }
    
    // Test each node in the chain
    for (int i = 0; i < chain_length - 1; i++) {
        size_t num_dependents = 0;
        CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[i], nullptr, &num_dependents));
        EXPECT_EQ(num_dependents, 1);
        
        std::vector<cudaGraphNode_t> dependents(num_dependents);
        CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[i], dependents.data(), &num_dependents));
        EXPECT_EQ(dependents[0], nodes[i + 1]);
    }
    
    // Last node should have no dependents
    size_t num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[chain_length - 1], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 0);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 11: Multiple parallel branches
TEST_F(GraphNodeGetDependentNodesTest, ParallelBranches) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    std::vector<cudaGraphNode_t> nodes(7);
    
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    // Root node
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[0], graph, nullptr, 0, &kernelParams));
    
    // Two parallel branches
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[1], graph, &nodes[0], 1, &kernelParams));
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[2], graph, &nodes[0], 1, &kernelParams));
    
    // Each branch continues
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[3], graph, &nodes[1], 1, &kernelParams));
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[4], graph, &nodes[2], 1, &kernelParams));
    
    // Branches merge
    cudaGraphNode_t merge_deps[] = {nodes[3], nodes[4]};
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[5], graph, merge_deps, 2, &kernelParams));
    
    // Final node
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[6], graph, &nodes[5], 1, &kernelParams));
    
    // Test root node (should have 2 dependents)
    size_t num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 2);
    
    std::vector<cudaGraphNode_t> dependents(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[0], dependents.data(), &num_dependents));
    
    EXPECT_TRUE(ContainsNode(dependents, nodes[1]));
    EXPECT_TRUE(ContainsNode(dependents, nodes[2]));
    
    // Test merge node dependencies
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[3], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 1);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[4], nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 1);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 12: Thread safety test
TEST_F(GraphNodeGetDependentNodesTest, ThreadSafety) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&graph, &nodes);
    
    std::atomic<bool> test_passed{true};
    std::vector<std::future<void>> futures;
    const int num_threads = 8;
    const int iterations_per_thread = 50;
    
    for (int i = 0; i < num_threads; i++) {
        futures.push_back(std::async(std::launch::async, [&, i]() {
            for (int j = 0; j < iterations_per_thread; j++) {
                int node_idx = j % nodes.size();
                size_t num_dependents = 0;
                
                cudaError_t result = cudaGraphNodeGetDependentNodes(nodes[node_idx], nullptr, &num_dependents);
                if (result != cudaSuccess) {
                    test_passed.store(false);
                    break;
                }
                
                if (num_dependents > 0) {
                    std::vector<cudaGraphNode_t> dependents(num_dependents);
                    result = cudaGraphNodeGetDependentNodes(nodes[node_idx], dependents.data(), &num_dependents);
                    if (result != cudaSuccess) {
                        test_passed.store(false);
                        break;
                    }
                }
            }
        }));
    }
    
    for (auto& future : futures) {
        future.wait();
    }
    
    EXPECT_TRUE(test_passed.load());
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 13: Dependent consistency after graph modifications
TEST_F(GraphNodeGetDependentNodesTest, ConsistencyAfterModification) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaGraphNode_t node1, node2, node3;
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&node1, graph, nullptr, 0, &kernelParams));
    CUDA_CHECK(cudaGraphAddKernelNode(&node2, graph, &node1, 1, &kernelParams));
    
    // Check initial state
    size_t num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(node1, nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 1);
    
    // Add another dependent
    CUDA_CHECK(cudaGraphAddKernelNode(&node3, graph, &node1, 1, &kernelParams));
    
    // Check updated state
    num_dependents = 0;
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(node1, nullptr, &num_dependents));
    EXPECT_EQ(num_dependents, 2);
    
    std::vector<cudaGraphNode_t> dependents(num_dependents);
    CUDA_CHECK(cudaGraphNodeGetDependentNodes(node1, dependents.data(), &num_dependents));
    
    EXPECT_TRUE(ContainsNode(dependents, node2));
    EXPECT_TRUE(ContainsNode(dependents, node3));
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 14: Stress test with large graph
TEST_F(GraphNodeGetDependentNodesTest, StressTestLargeGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    const int num_nodes = 1000;
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    // Create root
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[0], graph, nullptr, 0, &kernelParams));
    
    // Create binary tree structure
    for (int i = 1; i < num_nodes; i++) {
        int parent_idx = (i - 1) / 2;
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, &nodes[parent_idx], 1, &kernelParams));
    }
    
    // Test some nodes randomly
    for (int test_idx = 0; test_idx < 50; test_idx++) {
        int node_idx = test_idx * 20;  // Test every 20th node
        if (node_idx >= num_nodes) break;
        
        size_t num_dependents = 0;
        CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[node_idx], nullptr, &num_dependents));
        
        if (num_dependents > 0) {
            std::vector<cudaGraphNode_t> dependents(num_dependents);
            CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[node_idx], dependents.data(), &num_dependents));
            
            // Verify all dependents are unique
            std::set<cudaGraphNode_t> unique_dependents(dependents.begin(), dependents.end());
            EXPECT_EQ(unique_dependents.size(), num_dependents);
        }
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 15: Memory consistency check
TEST_F(GraphNodeGetDependentNodesTest, MemoryConsistencyCheck) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&graph, &nodes);
    
    // Test consistency across multiple calls
    for (int node_idx = 0; node_idx < static_cast<int>(nodes.size()); node_idx++) {
        size_t num_dependents1 = 0, num_dependents2 = 0, num_dependents3 = 0;
        
        CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[node_idx], nullptr, &num_dependents1));
        CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[node_idx], nullptr, &num_dependents2));
        CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[node_idx], nullptr, &num_dependents3));
        
        EXPECT_EQ(num_dependents1, num_dependents2);
        EXPECT_EQ(num_dependents2, num_dependents3);
        
        if (num_dependents1 > 0) {
            std::vector<cudaGraphNode_t> dependents1(num_dependents1);
            std::vector<cudaGraphNode_t> dependents2(num_dependents1);
            std::vector<cudaGraphNode_t> dependents3(num_dependents1);
            
            CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[node_idx], dependents1.data(), &num_dependents1));
            CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[node_idx], dependents2.data(), &num_dependents2));
            CUDA_CHECK(cudaGraphNodeGetDependentNodes(nodes[node_idx], dependents3.data(), &num_dependents3));
            
            // Convert to sets for order-independent comparison
            auto set1 = VectorToSet(dependents1, num_dependents1);
            auto set2 = VectorToSet(dependents2, num_dependents2);
            auto set3 = VectorToSet(dependents3, num_dependents3);
            
            EXPECT_EQ(set1, set2);
            EXPECT_EQ(set2, set3);
        }
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 16: Integration test with cloned graph
TEST_F(GraphNodeGetDependentNodesTest, ClonedGraphDependents) {
    cudaGraph_t original_graph, cloned_graph;
    std::vector<cudaGraphNode_t> original_nodes;
    
    CreateComplexGraph(&original_graph, &original_nodes);
    
    CUDA_CHECK(cudaGraphClone(&cloned_graph, original_graph));
    
    // Get nodes from cloned graph
    size_t num_nodes = 0;
    CUDA_CHECK(cudaGraphGetNodes(cloned_graph, nullptr, &num_nodes));
    EXPECT_EQ(num_nodes, original_nodes.size());
    
    std::vector<cudaGraphNode_t> cloned_nodes(num_nodes);
    CUDA_CHECK(cudaGraphGetNodes(cloned_graph, cloned_nodes.data(), &num_nodes));
    
    // Test that dependent relationships are preserved
    // (Note: cloned nodes will be different objects, but relationships should be consistent)
    for (size_t i = 0; i < original_nodes.size(); i++) {
        size_t original_num_dependents = 0;
        size_t cloned_num_dependents = 0;
        
        CUDA_CHECK(cudaGraphNodeGetDependentNodes(original_nodes[i], nullptr, &original_num_dependents));
        
        // Find corresponding node in cloned graph and test
        // This is complex due to node mapping, so we just verify the API works
        for (size_t j = 0; j < cloned_nodes.size(); j++) {
            cudaError_t result = cudaGraphNodeGetDependentNodes(cloned_nodes[j], nullptr, &cloned_num_dependents);
            EXPECT_EQ(result, cudaSuccess);
        }
    }
    
    CUDA_CHECK(cudaGraphDestroy(original_graph));
    CUDA_CHECK(cudaGraphDestroy(cloned_graph));
}