#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <set>
#include <algorithm>
#include <thread>
#include <future>
#include <atomic>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

__global__ void kernel_a(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = data[idx] * 2.0f;
    }
}

__global__ void kernel_b(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = data[idx] + 1.0f;
    }
}

__global__ void kernel_c(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = sqrtf(data[idx]);
    }
}

void CUDART_CB host_callback(void* data) {
    int* counter = static_cast<int*>(data);
    (*counter)++;
}

class GraphGetEdgesTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaFree(nullptr));
        
        CUDA_CHECK(cudaMalloc(&d_data, data_size * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&d_temp1, data_size * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&d_temp2, data_size * sizeof(float)));
        
        h_data.resize(data_size);
        for (int i = 0; i < data_size; i++) {
            h_data[i] = static_cast<float>(i);
        }
        CUDA_CHECK(cudaMemcpy(d_data, h_data.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
        
        CUDA_CHECK(cudaStreamCreate(&stream));
        CUDA_CHECK(cudaEventCreate(&event));
        
        kernel_args[0] = &d_data;
        kernel_args[1] = &data_size;
    }
    
    void TearDown() override {
        if (d_data) cudaFree(d_data);
        if (d_temp1) cudaFree(d_temp1);
        if (d_temp2) cudaFree(d_temp2);
        if (stream) cudaStreamDestroy(stream);
        if (event) cudaEventDestroy(event);
    }
    
    void CreateSimpleGraph(cudaGraph_t* graph, std::vector<cudaGraphNode_t>* nodes) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        nodes->resize(2);
        
        cudaKernelNodeParams kernelParams1 = {};
        kernelParams1.func = (void*)kernel_a;
        kernelParams1.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams1.blockDim = {256, 1, 1};
        kernelParams1.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[0], *graph, nullptr, 0, &kernelParams1));
        
        cudaKernelNodeParams kernelParams2 = {};
        kernelParams2.func = (void*)kernel_b;
        kernelParams2.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams2.blockDim = {256, 1, 1};
        kernelParams2.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[1], *graph, &(*nodes)[0], 1, &kernelParams2));
    }
    
    void CreateComplexGraph(cudaGraph_t* graph, std::vector<cudaGraphNode_t>* nodes) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        nodes->resize(8);
        
        CUDA_CHECK(cudaGraphAddMemcpyNode1D(&(*nodes)[0], *graph, nullptr, 0, d_temp1, d_data, 
                                          data_size * sizeof(float), cudaMemcpyDeviceToDevice));
        
        cudaKernelNodeParams kernelParams1 = {};
        kernelParams1.func = (void*)kernel_a;
        kernelParams1.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams1.blockDim = {256, 1, 1};
        kernelParams1.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[1], *graph, &(*nodes)[0], 1, &kernelParams1));
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[2], *graph, &(*nodes)[0], 1, &kernelParams1));
        
        cudaKernelNodeParams kernelParams2 = {};
        kernelParams2.func = (void*)kernel_b;
        kernelParams2.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams2.blockDim = {256, 1, 1};
        kernelParams2.kernelParams = (void**)kernel_args;
        
        cudaGraphNode_t deps1[] = {(*nodes)[1], (*nodes)[2]};
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[3], *graph, deps1, 2, &kernelParams2));
        
        CUDA_CHECK(cudaGraphAddEventRecordNode(&(*nodes)[4], *graph, &(*nodes)[3], 1, event));
        
        counter = 0;
        cudaHostNodeParams hostParams = {};
        hostParams.fn = host_callback;
        hostParams.userData = &counter;
        CUDA_CHECK(cudaGraphAddHostNode(&(*nodes)[5], *graph, &(*nodes)[4], 1, &hostParams));
        
        cudaMemsetParams memsetParams = {};
        memsetParams.dst = d_temp2;
        memsetParams.value = 0;
        memsetParams.elementSize = sizeof(float);
        memsetParams.width = data_size;
        memsetParams.height = 1;
        CUDA_CHECK(cudaGraphAddMemsetNode(&(*nodes)[6], *graph, &(*nodes)[5], 1, &memsetParams));
        
        CUDA_CHECK(cudaGraphAddEmptyNode(&(*nodes)[7], *graph, &(*nodes)[6], 1));
    }
    
    bool EdgeExists(const std::vector<std::pair<cudaGraphNode_t, cudaGraphNode_t>>& edges,
                   cudaGraphNode_t from, cudaGraphNode_t to) {
        return std::find(edges.begin(), edges.end(), std::make_pair(from, to)) != edges.end();
    }
    
    std::vector<std::pair<cudaGraphNode_t, cudaGraphNode_t>> GetAllEdges(
        const std::vector<cudaGraphNode_t>& edges_from,
        const std::vector<cudaGraphNode_t>& edges_to,
        size_t num_edges) {
        
        std::vector<std::pair<cudaGraphNode_t, cudaGraphNode_t>> result;
        for (size_t i = 0; i < num_edges; i++) {
            result.push_back({edges_from[i], edges_to[i]});
        }
        return result;
    }
    
    const int data_size = 1024;
    float* d_data = nullptr;
    float* d_temp1 = nullptr;
    float* d_temp2 = nullptr;
    std::vector<float> h_data;
    cudaStream_t stream = nullptr;
    cudaEvent_t event = nullptr;
    void* kernel_args[2];
    int counter = 0;
};

// Test 1: Basic functionality with simple graph
TEST_F(GraphGetEdgesTest, BasicFunctionality) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateSimpleGraph(&graph, &nodes);
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    EXPECT_EQ(num_edges, 1);  // One edge from node 0 to node 1
    
    std::vector<cudaGraphNode_t> edges_from(num_edges);
    std::vector<cudaGraphNode_t> edges_to(num_edges);
    
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from.data(), edges_to.data(), &num_edges));
    
    EXPECT_EQ(edges_from[0], nodes[0]);
    EXPECT_EQ(edges_to[0], nodes[1]);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 2: Empty graph
TEST_F(GraphGetEdgesTest, EmptyGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    EXPECT_EQ(num_edges, 0);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 3: Single node graph (no edges)
TEST_F(GraphGetEdgesTest, SingleNodeGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaGraphNode_t node;
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &kernelParams));
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    EXPECT_EQ(num_edges, 0);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 4: Complex graph with multiple node types and dependencies
TEST_F(GraphGetEdgesTest, ComplexGraphEdges) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&graph, &nodes);
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    
    std::vector<cudaGraphNode_t> edges_from(num_edges);
    std::vector<cudaGraphNode_t> edges_to(num_edges);
    
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from.data(), edges_to.data(), &num_edges));
    
    auto all_edges = GetAllEdges(edges_from, edges_to, num_edges);
    
    EXPECT_TRUE(EdgeExists(all_edges, nodes[0], nodes[1])); // memcpy -> kernel1
    EXPECT_TRUE(EdgeExists(all_edges, nodes[0], nodes[2])); // memcpy -> kernel2  
    EXPECT_TRUE(EdgeExists(all_edges, nodes[1], nodes[3])); // kernel1 -> kernel3
    EXPECT_TRUE(EdgeExists(all_edges, nodes[2], nodes[3])); // kernel2 -> kernel3
    EXPECT_TRUE(EdgeExists(all_edges, nodes[3], nodes[4])); // kernel3 -> event_record
    EXPECT_TRUE(EdgeExists(all_edges, nodes[4], nodes[5])); // event_record -> host
    EXPECT_TRUE(EdgeExists(all_edges, nodes[5], nodes[6])); // host -> memset
    EXPECT_TRUE(EdgeExists(all_edges, nodes[6], nodes[7])); // memset -> empty
    
    EXPECT_EQ(num_edges, 8);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 5: Null pointer validation
TEST_F(GraphGetEdgesTest, NullPointerValidation) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateSimpleGraph(&graph, &nodes);
    
    size_t num_edges = 10;
    
    EXPECT_EQ(cudaGraphGetEdges(nullptr, nullptr, nullptr, &num_edges), cudaErrorInvalidValue);
    EXPECT_EQ(cudaGraphGetEdges(graph, nullptr, nullptr, nullptr), cudaErrorInvalidValue);
    
    std::vector<cudaGraphNode_t> edges_from(1);
    std::vector<cudaGraphNode_t> edges_to(1);
    
    EXPECT_EQ(cudaGraphGetEdges(graph, edges_from.data(), nullptr, &num_edges), cudaErrorInvalidValue);
    EXPECT_EQ(cudaGraphGetEdges(graph, nullptr, edges_to.data(), &num_edges), cudaErrorInvalidValue);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 6: Invalid graph handle
TEST_F(GraphGetEdgesTest, InvalidGraphHandle) {
    cudaGraph_t invalid_graph = nullptr;
    size_t num_edges = 0;
    
    EXPECT_EQ(cudaGraphGetEdges(invalid_graph, nullptr, nullptr, &num_edges), cudaErrorInvalidValue);
    
    cudaGraph_t fake_graph = reinterpret_cast<cudaGraph_t>(0xDEADBEEF);
    EXPECT_NE(cudaGraphGetEdges(fake_graph, nullptr, nullptr, &num_edges), cudaSuccess);
}

// Test 7: Buffer size validation
TEST_F(GraphGetEdgesTest, BufferSizeValidation) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&graph, &nodes);
    
    size_t actual_num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &actual_num_edges));
    
    std::vector<cudaGraphNode_t> edges_from(actual_num_edges - 1);
    std::vector<cudaGraphNode_t> edges_to(actual_num_edges - 1);
    size_t smaller_size = actual_num_edges - 1;
    
    cudaError_t result = cudaGraphGetEdges(graph, edges_from.data(), edges_to.data(), &smaller_size);
    EXPECT_EQ(result, cudaErrorInvalidValue);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 8: Star topology graph
TEST_F(GraphGetEdgesTest, StarTopology) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    const int num_nodes = 6;  // 1 center + 5 leaves
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[0], graph, nullptr, 0, &kernelParams));
    
    for (int i = 1; i < num_nodes; i++) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, &nodes[0], 1, &kernelParams));
    }
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    EXPECT_EQ(num_edges, 5);  // 5 edges from center to leaves
    
    std::vector<cudaGraphNode_t> edges_from(num_edges);
    std::vector<cudaGraphNode_t> edges_to(num_edges);
    
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from.data(), edges_to.data(), &num_edges));
    
    for (size_t i = 0; i < num_edges; i++) {
        EXPECT_EQ(edges_from[i], nodes[0]);
        bool found_target = false;
        for (int j = 1; j < num_nodes; j++) {
            if (edges_to[i] == nodes[j]) {
                found_target = true;
                break;
            }
        }
        EXPECT_TRUE(found_target);
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 9: Linear chain topology
TEST_F(GraphGetEdgesTest, LinearChainTopology) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    const int chain_length = 10;
    std::vector<cudaGraphNode_t> nodes(chain_length);
    
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[0], graph, nullptr, 0, &kernelParams));
    
    for (int i = 1; i < chain_length; i++) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, &nodes[i-1], 1, &kernelParams));
    }
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    EXPECT_EQ(num_edges, chain_length - 1);
    
    std::vector<cudaGraphNode_t> edges_from(num_edges);
    std::vector<cudaGraphNode_t> edges_to(num_edges);
    
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from.data(), edges_to.data(), &num_edges));
    
    for (int i = 0; i < chain_length - 1; i++) {
        bool edge_found = false;
        for (size_t j = 0; j < num_edges; j++) {
            if (edges_from[j] == nodes[i] && edges_to[j] == nodes[i + 1]) {
                edge_found = true;
                break;
            }
        }
        EXPECT_TRUE(edge_found) << "Edge from node " << i << " to node " << (i + 1) << " not found";
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 10: Diamond topology
TEST_F(GraphGetEdgesTest, DiamondTopology) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    std::vector<cudaGraphNode_t> nodes(4);
    
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[0], graph, nullptr, 0, &kernelParams));
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[1], graph, &nodes[0], 1, &kernelParams));
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[2], graph, &nodes[0], 1, &kernelParams));
    
    cudaGraphNode_t deps[] = {nodes[1], nodes[2]};
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[3], graph, deps, 2, &kernelParams));
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    EXPECT_EQ(num_edges, 4);  // 0->1, 0->2, 1->3, 2->3
    
    std::vector<cudaGraphNode_t> edges_from(num_edges);
    std::vector<cudaGraphNode_t> edges_to(num_edges);
    
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from.data(), edges_to.data(), &num_edges));
    
    auto all_edges = GetAllEdges(edges_from, edges_to, num_edges);
    
    EXPECT_TRUE(EdgeExists(all_edges, nodes[0], nodes[1]));
    EXPECT_TRUE(EdgeExists(all_edges, nodes[0], nodes[2]));
    EXPECT_TRUE(EdgeExists(all_edges, nodes[1], nodes[3]));
    EXPECT_TRUE(EdgeExists(all_edges, nodes[2], nodes[3]));
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 11: Thread safety test
TEST_F(GraphGetEdgesTest, ThreadSafety) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&graph, &nodes);
    
    size_t expected_num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &expected_num_edges));
    
    std::atomic<bool> test_passed{true};
    std::vector<std::future<void>> futures;
    const int num_threads = 8;
    const int iterations_per_thread = 50;
    
    for (int i = 0; i < num_threads; i++) {
        futures.push_back(std::async(std::launch::async, [&]() {
            for (int j = 0; j < iterations_per_thread; j++) {
                size_t num_edges = 0;
                cudaError_t result = cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges);
                if (result != cudaSuccess || num_edges != expected_num_edges) {
                    test_passed.store(false);
                    break;
                }
            }
        }));
    }
    
    for (auto& future : futures) {
        future.wait();
    }
    
    EXPECT_TRUE(test_passed.load());
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 12: Edge consistency after graph modifications
TEST_F(GraphGetEdgesTest, EdgeConsistencyAfterModification) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaGraphNode_t node1, node2, node3;
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&node1, graph, nullptr, 0, &kernelParams));
    CUDA_CHECK(cudaGraphAddKernelNode(&node2, graph, &node1, 1, &kernelParams));
    
    size_t num_edges_before = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges_before));
    EXPECT_EQ(num_edges_before, 1);
    
    CUDA_CHECK(cudaGraphAddKernelNode(&node3, graph, &node2, 1, &kernelParams));
    
    size_t num_edges_after = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges_after));
    EXPECT_EQ(num_edges_after, 2);
    
    std::vector<cudaGraphNode_t> edges_from(num_edges_after);
    std::vector<cudaGraphNode_t> edges_to(num_edges_after);
    
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from.data(), edges_to.data(), &num_edges_after));
    
    auto all_edges = GetAllEdges(edges_from, edges_to, num_edges_after);
    
    EXPECT_TRUE(EdgeExists(all_edges, node1, node2));
    EXPECT_TRUE(EdgeExists(all_edges, node2, node3));
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 13: Stress test with large graph
TEST_F(GraphGetEdgesTest, StressTestLargeGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    const int num_nodes = 1000;
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)kernel_a;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[0], graph, nullptr, 0, &kernelParams));
    
    for (int i = 1; i < num_nodes; i++) {
        int dep_idx = i / 2; // Each node depends on its parent in a binary tree
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, &nodes[dep_idx], 1, &kernelParams));
    }
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    EXPECT_EQ(num_edges, num_nodes - 1);
    
    std::vector<cudaGraphNode_t> edges_from(num_edges);
    std::vector<cudaGraphNode_t> edges_to(num_edges);
    
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from.data(), edges_to.data(), &num_edges));
    
    std::set<std::pair<cudaGraphNode_t, cudaGraphNode_t>> edge_set;
    for (size_t i = 0; i < num_edges; i++) {
        edge_set.insert({edges_from[i], edges_to[i]});
    }
    
    EXPECT_EQ(edge_set.size(), num_edges);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 14: Memory consistency check
TEST_F(GraphGetEdgesTest, MemoryConsistencyCheck) {
    cudaGraph_t graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&graph, &nodes);
    
    size_t num_edges = 0;
    CUDA_CHECK(cudaGraphGetEdges(graph, nullptr, nullptr, &num_edges));
    
    std::vector<cudaGraphNode_t> edges_from1(num_edges);
    std::vector<cudaGraphNode_t> edges_to1(num_edges);
    std::vector<cudaGraphNode_t> edges_from2(num_edges);
    std::vector<cudaGraphNode_t> edges_to2(num_edges);
    std::vector<cudaGraphNode_t> edges_from3(num_edges);
    std::vector<cudaGraphNode_t> edges_to3(num_edges);
    
    size_t num_edges1 = num_edges;
    size_t num_edges2 = num_edges;
    size_t num_edges3 = num_edges;
    
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from1.data(), edges_to1.data(), &num_edges1));
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from2.data(), edges_to2.data(), &num_edges2));
    CUDA_CHECK(cudaGraphGetEdges(graph, edges_from3.data(), edges_to3.data(), &num_edges3));
    
    EXPECT_EQ(num_edges1, num_edges2);
    EXPECT_EQ(num_edges2, num_edges3);
    
    for (size_t i = 0; i < num_edges; i++) {
        EXPECT_EQ(edges_from1[i], edges_from2[i]);
        EXPECT_EQ(edges_from1[i], edges_from3[i]);
        EXPECT_EQ(edges_to1[i], edges_to2[i]);
        EXPECT_EQ(edges_to1[i], edges_to3[i]);
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 15: Integration test with cloned graph
TEST_F(GraphGetEdgesTest, ClonedGraphEdges) {
    cudaGraph_t original_graph, cloned_graph;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&original_graph, &nodes);
    
    CUDA_CHECK(cudaGraphClone(&cloned_graph, original_graph));
    
    size_t original_num_edges = 0;
    size_t cloned_num_edges = 0;
    
    CUDA_CHECK(cudaGraphGetEdges(original_graph, nullptr, nullptr, &original_num_edges));
    CUDA_CHECK(cudaGraphGetEdges(cloned_graph, nullptr, nullptr, &cloned_num_edges));
    
    EXPECT_EQ(original_num_edges, cloned_num_edges);
    
    CUDA_CHECK(cudaGraphDestroy(original_graph));
    CUDA_CHECK(cudaGraphDestroy(cloned_graph));
}