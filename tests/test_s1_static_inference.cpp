#include "common.h"
#include <vector>

// --- Functional Convolution Kernel ---
__global__ void conv_kernel(const float* input, const float* weights, float* output, int image_size, int kernel_size) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < image_size && y < image_size) {
        float sum = 0.0f;
        int half_kernel = kernel_size / 2;
        for (int ky = 0; ky < kernel_size; ++ky) {
            for (int kx = 0; kx < kernel_size; ++kx) {
                int iy = y + ky - half_kernel;
                int ix = x + kx - half_kernel;
                if (iy >= 0 && iy < image_size && ix >= 0 && ix < image_size) {
                    sum += input[iy * image_size + ix] * weights[ky * kernel_size + kx];
                }
            }
        }
        output[y * image_size + x] = sum;
    }
}

// --- ResNet-50 simulation with functional kernels ---

// Simulates a single convolutional layer
void conv_layer(const float* in_data, const float* weights, float* out_data, int image_size, int kernel_size, cudaStream_t stream) {
    dim3 threads(16, 16);
    dim3 blocks((image_size + threads.x - 1) / threads.x, (image_size + threads.y - 1) / threads.y);
    // Note: This is a simplified conv and doesn't handle channels for brevity.
    // The goal is to have a realistic workload, not a correct ResNet implementation.
    conv_kernel<<<blocks, threads, 0, stream>>>(in_data, weights, out_data, image_size, kernel_size);
}

// Simulates a ResNet identity block
void identity_block(float* data, const float* weights, float* buffer, int image_size, cudaStream_t stream) {
    conv_layer(data, weights, buffer, image_size, 1, stream);
    conv_layer(buffer, weights, data, image_size, 3, stream);
    conv_layer(data, weights, buffer, image_size, 1, stream);
    // In a real ResNet, there would be an addition with the original input here.
    // For workload simulation, we just copy back asynchronously.
    CHECK_CUDA(cudaMemcpyAsync(data, buffer, image_size * image_size * sizeof(float), cudaMemcpyDeviceToDevice, stream));
}

// Simulates a ResNet convolutional block
void conv_block(float* data, const float* weights, float* buffer, int& image_size, cudaStream_t stream) {
    // Down-sampling is typically done in the first conv layer of the block
    int prev_image_size = image_size;
    image_size /= 2;
    conv_layer(data, weights, buffer, prev_image_size, 1, stream);
    conv_layer(buffer, weights, data, image_size, 3, stream);
    conv_layer(data, weights, buffer, image_size, 1, stream);
    CHECK_CUDA(cudaMemcpyAsync(data, buffer, image_size * image_size * sizeof(float), cudaMemcpyDeviceToDevice, stream));
}

// Simulates a more realistic ResNet-50 workload
void resnet50_workload(float* data, const float* weights, float* buffer, cudaStream_t stream) {
    int image_size = 224;

    // Stage 1
    conv_layer(data, weights, buffer, image_size, 7, stream);
    CHECK_CUDA(cudaMemcpyAsync(data, buffer, image_size * image_size * sizeof(float), cudaMemcpyDeviceToDevice, stream));
    image_size /= 2; // Max pooling

    // Stage 2
    conv_block(data, weights, buffer, image_size, stream);
    identity_block(data, weights, buffer, image_size, stream);
    identity_block(data, weights, buffer, image_size, stream);

    // Stage 3
    conv_block(data, weights, buffer, image_size, stream);
    for (int i = 0; i < 3; ++i) identity_block(data, weights, buffer, image_size, stream);

    // Stage 4
    conv_block(data, weights, buffer, image_size, stream);
    for (int i = 0; i < 5; ++i) identity_block(data, weights, buffer, image_size, stream);

    // Stage 5
    conv_block(data, weights, buffer, image_size, stream);
    for (int i = 0; i < 2; ++i) identity_block(data, weights, buffer, image_size, stream);
}

void run_inference_test(int batch_size) {
    // Note: batch_size is now implicit in the data size for this simplified test
    const int image_size = 224;
    const int data_size = image_size * image_size * 3; // Simplified to single channel for conv
    const int max_kernel_size = 7 * 7;
    const int iterations = 100;
    const int warmup_iterations = 5;

    float *d_data, *d_buffer, *d_weights;
    CHECK_CUDA(cudaMalloc(&d_data, data_size * sizeof(float)));
    CHECK_CUDA(cudaMalloc(&d_buffer, data_size * sizeof(float))); // Buffer for out-of-place ops
    CHECK_CUDA(cudaMalloc(&d_weights, max_kernel_size * sizeof(float)));
    
    // Initialize data and weights
    std::vector<float> h_weights(max_kernel_size, 0.1f);
    CHECK_CUDA(cudaMemset(d_data, 0, data_size * sizeof(float)));
    CHECK_CUDA(cudaMemcpy(d_weights, h_weights.data(), max_kernel_size * sizeof(float), cudaMemcpyHostToDevice));

    Timer timer;
    std::vector<double> baseline_timings, graph_timings;
    
    cudaStream_t stream;
    CHECK_CUDA(cudaStreamCreate(&stream));

    // --- Baseline: Traditional Kernel Launch ---
    for (int i = 0; i < iterations + warmup_iterations; ++i) {
        timer.start();
        resnet50_workload(d_data, d_weights, d_buffer, stream);
        CHECK_CUDA(cudaStreamSynchronize(stream));
        timer.stop();
        if (i >= warmup_iterations) {
            baseline_timings.push_back(timer.elapsed_ms());
        }
    }

    // --- Graph Mode ---
    cudaGraph_t graph;
    cudaGraphExec_t instance;

    // 1. Capture
    CHECK_CUDA(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    resnet50_workload(d_data, d_weights, d_buffer, stream);
    CHECK_CUDA(cudaStreamEndCapture(stream, &graph));
    
    // 2. Instantiate
    CHECK_CUDA(cudaGraphInstantiate(&instance, graph, NULL, NULL, 0));

    // 3. Run
    for (int i = 0; i < iterations + warmup_iterations; ++i) {
        timer.start();
        CHECK_CUDA(cudaGraphLaunch(instance, stream));
        CHECK_CUDA(cudaStreamSynchronize(stream));
        timer.stop();
        if (i >= warmup_iterations) {
            graph_timings.push_back(timer.elapsed_ms());
        }
    }

    // --- Correctness Verification ---
    std::vector<float> h_baseline_result(data_size);
    CHECK_CUDA(cudaMemcpy(h_baseline_result.data(), d_data, data_size * sizeof(float), cudaMemcpyDeviceToHost));

    // Reset device data for the graph run
    CHECK_CUDA(cudaMemset(d_data, 0, data_size * sizeof(float)));
    CHECK_CUDA(cudaMemset(d_buffer, 0, data_size * sizeof(float)));

    // Execute graph one more time to get final state
    CHECK_CUDA(cudaGraphLaunch(instance, stream));
    CHECK_CUDA(cudaStreamSynchronize(stream));

    std::vector<float> h_graph_result(data_size);
    CHECK_CUDA(cudaMemcpy(h_graph_result.data(), d_data, data_size * sizeof(float), cudaMemcpyDeviceToHost));

    bool correct = true;
    for (size_t i = 0; i < data_size; ++i) {
        if (std::abs(h_baseline_result[i] - h_graph_result[i]) > 1e-5) {
            correct = false;
            break;
        }
    }

    // --- Analysis ---
    std::cout << "\n--- S1: Static Inference Test (Batch Size: " << batch_size << ") ---" << std::endl;
    print_stats("Baseline (Launch)", baseline_timings, batch_size);
    print_stats("Graph", graph_timings, batch_size);

    double baseline_avg = std::accumulate(baseline_timings.begin(), baseline_timings.end(), 0.0) / baseline_timings.size();
    double graph_avg = std::accumulate(graph_timings.begin(), graph_timings.end(), 0.0) / graph_timings.size();
    
    double perf_improvement = (baseline_avg - graph_avg) / baseline_avg * 100.0;
    
    std::cout << "\n--- Results & Pass Criteria ---" << std::endl;
    std::cout << "Correctness Check: " << (correct ? "PASS" : "FAIL") << std::endl;
    std::cout << "Performance Improvement: " << std::fixed << std::setprecision(2) << perf_improvement << "%" << std::endl;
    
    if (perf_improvement >= 15.0 && correct) {
        std::cout << "Overall Result: PASS" << std::endl;
    } else {
        std::cout << "Overall Result: FAIL" << std::endl;
    }
    std::cout << "--------------------------------------------------\n" << std::endl;


    // Cleanup
    CHECK_CUDA(cudaGraphExecDestroy(instance));
    CHECK_CUDA(cudaGraphDestroy(graph));
    CHECK_CUDA(cudaStreamDestroy(stream));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_buffer));
    CHECK_CUDA(cudaFree(d_weights));
}

TEST(S1_StaticInference, Performance) {
    // Batch size is now implicit in the test setup for simplicity
    std::cout << "Running ResNet-50 simulation with effective batch size 1." << std::endl;
    run_inference_test(1); 
}
