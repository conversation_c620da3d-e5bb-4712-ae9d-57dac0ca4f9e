#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <chrono>
#include <thread>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

// --- KERNELS for multi-stream tests ---
__global__ void setValue(float* data, float value, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        data[i] = value;
    }
}

__global__ void addValue(float* data, float value, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        data[i] += value;
    }
}

// A dummy kernel that just consumes GPU time.
// Note: __do_nothing() is an undocumented PTX instruction.
// A more portable way would be a loop with volatile variables.
__global__ void simple_spin(int cycles) {
    for (int i=0; i < cycles; ++i) {
        #if defined(__CUDA_ARCH__) && __CUDA_ARCH__ >= 600
        __do_nothing();
        #else
        // Fallback for older architectures
        for(volatile int j=0; j<100; ++j);
        #endif
    }
}


class MultiStreamTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaFree(nullptr)); // Initialize context
        n = 256;
        size = n * sizeof(float);

        CUDA_CHECK(cudaStreamCreateWithFlags(&streamA, cudaStreamNonBlocking));
        CUDA_CHECK(cudaStreamCreateWithFlags(&streamB, cudaStreamNonBlocking));

        CUDA_CHECK(cudaMalloc(&d_a, size));
        CUDA_CHECK(cudaMalloc(&d_b, size));
        
        std::vector<float> h_zeros(n, 0.0f);
        CUDA_CHECK(cudaMemcpy(d_a, h_zeros.data(), size, cudaMemcpyHostToDevice));
        CUDA_CHECK(cudaMemcpy(d_b, h_zeros.data(), size, cudaMemcpyHostToDevice));
    }

    void TearDown() override {
        CUDA_CHECK(cudaFree(d_a));
        CUDA_CHECK(cudaFree(d_b));
        CUDA_CHECK(cudaStreamDestroy(streamA));
        CUDA_CHECK(cudaStreamDestroy(streamB));
    }

    cudaStream_t streamA, streamB;
    float *d_a, *d_b;
    int n;
    size_t size;
};

// --- Basic Concurrency and Synchronization Cases ---

// Test Case 1: Launch two independent graphs concurrently on different streams.
TEST_F(MultiStreamTest, ConcurrentGraphLaunchOnDifferentStreams) {
    cudaGraph_t graphA, graphB;
    CUDA_CHECK(cudaGraphCreate(&graphA, 0));
    CUDA_CHECK(cudaGraphCreate(&graphB, 0));

    float valA = 100.0f;
    void* argsA[] = {&d_a, &valA, &n};
    cudaKernelNodeParams paramsA = {(void*)setValue, {1,1,1}, {256,1,1}, 0, argsA};
    cudaGraphNode_t nodeA;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeA, graphA, nullptr, 0, &paramsA));
    
    float valB = 200.0f;
    void* argsB[] = {&d_b, &valB, &n};
    cudaKernelNodeParams paramsB = {(void*)setValue, {1,1,1}, {256,1,1}, 0, argsB};
    cudaGraphNode_t nodeB;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeB, graphB, nullptr, 0, &paramsB));

    cudaGraphExec_t execA, execB;
    CUDA_CHECK(cudaGraphInstantiate(&execA, graphA, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphInstantiate(&execB, graphB, nullptr, nullptr, 0));

    CUDA_CHECK(cudaGraphLaunch(execA, streamA));
    CUDA_CHECK(cudaGraphLaunch(execB, streamB));

    CUDA_CHECK(cudaStreamSynchronize(streamA));
    CUDA_CHECK(cudaStreamSynchronize(streamB));
    
    std::vector<float> h_a(n), h_b(n);
    CUDA_CHECK(cudaMemcpy(h_a.data(), d_a, size, cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(h_b.data(), d_b, size, cudaMemcpyDeviceToHost));

    for (int i = 0; i < n; ++i) {
        ASSERT_FLOAT_EQ(h_a[i], 100.0f);
        ASSERT_FLOAT_EQ(h_b[i], 200.0f);
    }

    CUDA_CHECK(cudaGraphExecDestroy(execA));
    CUDA_CHECK(cudaGraphExecDestroy(execB));
    CUDA_CHECK(cudaGraphDestroy(graphA));
    CUDA_CHECK(cudaGraphDestroy(graphB));
}

// Test Case 2: A graph waits for an event from an external, non-captured stream.
TEST_F(MultiStreamTest, GraphWaitsForExternalStream) {
    cudaEvent_t start_event;
    CUDA_CHECK(cudaEventCreate(&start_event));
    float valA = 50.0f;
    setValue<<<1, 256, 0, streamA>>>((float*)d_a, valA, n);
    CUDA_CHECK(cudaEventRecord(start_event, streamA));

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaGraphNode_t wait_node;
    CUDA_CHECK(cudaGraphAddEventWaitNode(&wait_node, graph, nullptr, 0, start_event));
    
    float valB = 25.0f;
    void* argsB[] = {&d_a, &valB, &n};
    cudaKernelNodeParams paramsB = {(void*)addValue, {1,1,1}, {256,1,1}, 0, argsB};
    cudaGraphNode_t add_node;
    CUDA_CHECK(cudaGraphAddKernelNode(&add_node, graph, &wait_node, 1, &paramsB));

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphLaunch(exec, streamB));

    CUDA_CHECK(cudaStreamSynchronize(streamB));

    std::vector<float> h_result(n);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_a, size, cudaMemcpyDeviceToHost));
    for (int i = 0; i < n; ++i) {
        ASSERT_FLOAT_EQ(h_result[i], 75.0f);
    }
    
    CUDA_CHECK(cudaEventDestroy(start_event));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test Case 3: Launch two graphs on the same stream, separated by an event.
TEST_F(MultiStreamTest, MultipleGraphsOnSameStreamWithEvents) {
    cudaGraph_t graphA, graphB;
    CUDA_CHECK(cudaGraphCreate(&graphA, 0));
    CUDA_CHECK(cudaGraphCreate(&graphB, 0));

    float valA = 10.0f;
    void* argsA[] = {&d_a, &valA, &n};
    cudaKernelNodeParams paramsA = {(void*)setValue, {1,1,1}, {256,1,1}, 0, argsA};
    cudaGraphNode_t nodeA;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeA, graphA, nullptr, 0, &paramsA));
    
    float valB = 5.0f;
    void* argsB[] = {&d_a, &valB, &n};
    cudaKernelNodeParams paramsB = {(void*)addValue, {1,1,1}, {256,1,1}, 0, argsB};
    cudaGraphNode_t nodeB;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeB, graphB, nullptr, 0, &paramsB));
    
    cudaGraphExec_t execA, execB;
    CUDA_CHECK(cudaGraphInstantiate(&execA, graphA, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphInstantiate(&execB, graphB, nullptr, nullptr, 0));

    cudaEvent_t event;
    CUDA_CHECK(cudaEventCreate(&event));

    CUDA_CHECK(cudaGraphLaunch(execA, streamA));
    CUDA_CHECK(cudaEventRecord(event, streamA));
    CUDA_CHECK(cudaStreamWaitEvent(streamA, event, 0));
    CUDA_CHECK(cudaGraphLaunch(execB, streamA));
    CUDA_CHECK(cudaStreamSynchronize(streamA));

    std::vector<float> h_result(n);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_a, size, cudaMemcpyDeviceToHost));
    for (int i = 0; i < n; ++i) {
        ASSERT_FLOAT_EQ(h_result[i], 15.0f);
    }
    
    CUDA_CHECK(cudaEventDestroy(event));
    CUDA_CHECK(cudaGraphExecDestroy(execA));
    CUDA_CHECK(cudaGraphExecDestroy(execB));
    CUDA_CHECK(cudaGraphDestroy(graphA));
    CUDA_CHECK(cudaGraphDestroy(graphB));
}

// Test Case 4: Demonstrate potential for overlapping execution (timing-based).
TEST_F(MultiStreamTest, OverlappingExecutionWithoutSync) {
    int cyclesA = 1000000, cyclesB = 100;
    
    cudaGraph_t graphA, graphB;
    CUDA_CHECK(cudaGraphCreate(&graphA, 0));
    CUDA_CHECK(cudaGraphCreate(&graphB, 0));

    void* argsA[] = {&cyclesA};
    cudaKernelNodeParams paramsA = {(void*)simple_spin, {1,1,1}, {1,1,1}, 0, argsA};
    cudaGraphNode_t nodeA;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeA, graphA, nullptr, 0, &paramsA));

    void* argsB[] = {&cyclesB};
    cudaKernelNodeParams paramsB = {(void*)simple_spin, {1,1,1}, {1,1,1}, 0, argsB};
    cudaGraphNode_t nodeB;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeB, graphB, nullptr, 0, &paramsB));

    cudaGraphExec_t execA, execB;
    CUDA_CHECK(cudaGraphInstantiate(&execA, graphA, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphInstantiate(&execB, graphB, nullptr, nullptr, 0));

    cudaEvent_t startA, endA, startB, endB;
    CUDA_CHECK(cudaEventCreate(&startA));
    CUDA_CHECK(cudaEventCreate(&endA));
    CUDA_CHECK(cudaEventCreate(&startB));
    CUDA_CHECK(cudaEventCreate(&endB));

    CUDA_CHECK(cudaEventRecord(startA, streamA));
    CUDA_CHECK(cudaGraphLaunch(execA, streamA));
    CUDA_CHECK(cudaEventRecord(endA, streamA));

    std::this_thread::sleep_for(std::chrono::microseconds(10));

    CUDA_CHECK(cudaEventRecord(startB, streamB));
    CUDA_CHECK(cudaGraphLaunch(execB, streamB));
    CUDA_CHECK(cudaEventRecord(endB, streamB));
    
    CUDA_CHECK(cudaEventSynchronize(endA));
    CUDA_CHECK(cudaEventSynchronize(endB));
    
    float timeA, timeB, start_offset;
    CUDA_CHECK(cudaEventElapsedTime(&timeA, startA, endA));
    CUDA_CHECK(cudaEventElapsedTime(&timeB, startB, endB));
    CUDA_CHECK(cudaEventElapsedTime(&start_offset, startA, startB));
    
    ASSERT_LT(start_offset, timeA);
    ASSERT_LT(timeB, timeA);

    CUDA_CHECK(cudaEventDestroy(startA));
    CUDA_CHECK(cudaEventDestroy(endA));
    CUDA_CHECK(cudaEventDestroy(startB));
    CUDA_CHECK(cudaEventDestroy(endB));
    CUDA_CHECK(cudaGraphExecDestroy(execA));
    CUDA_CHECK(cudaGraphExecDestroy(execB));
    CUDA_CHECK(cudaGraphDestroy(graphA));
    CUDA_CHECK(cudaGraphDestroy(graphB));
}

// Test Case 5: Parameter validation for stream arguments.
TEST_F(MultiStreamTest, LaunchGraphWithInvalidStream) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    ASSERT_EQ(cudaGraphLaunch(exec, nullptr), cudaErrorInvalidResourceHandle);
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// --- Large Scale & Stress Tests ---

// Test Case 6: High concurrency with 16 identical graphs.
TEST_F(MultiStreamTest, ConcurrentLaunchOnSixteenStreams) {
    const int num_streams = 16;
    std::vector<cudaStream_t> streams(num_streams);
    std::vector<cudaGraphExec_t> execs(num_streams);
    std::vector<cudaGraph_t> graphs(num_streams);
    std::vector<float*> d_data(num_streams);
    std::vector<void*> kernel_args_list;

    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamCreateWithFlags(&streams[i], cudaStreamNonBlocking));
        CUDA_CHECK(cudaMalloc(&d_data[i], size));
        
        float val = (float)(i + 1);
        float* p_val = new float(val);
        int* p_n = new int(n);
        kernel_args_list.push_back(new void*[3]{&d_data[i], p_val, p_n});

        CUDA_CHECK(cudaGraphCreate(&graphs[i], 0));
        cudaKernelNodeParams params = {(void*)setValue, {1,1,1}, {256,1,1}, 0, (void**)kernel_args_list.back()};
        cudaGraphNode_t node;
        CUDA_CHECK(cudaGraphAddKernelNode(&node, graphs[i], nullptr, 0, &params));
        CUDA_CHECK(cudaGraphInstantiate(&execs[i], graphs[i], nullptr, nullptr, 0));
    }

    for (int i = 0; i < num_streams; ++i) CUDA_CHECK(cudaGraphLaunch(execs[i], streams[i]));
    
    std::vector<std::vector<float>> h_data(num_streams, std::vector<float>(n));
    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamSynchronize(streams[i]));
        CUDA_CHECK(cudaMemcpy(h_data[i].data(), d_data[i], size, cudaMemcpyDeviceToHost));
        for(int j=0; j<n; ++j) {
            ASSERT_FLOAT_EQ(h_data[i][j], (float)(i + 1));
        }
    }

    for (int i = 0; i < num_streams; ++i) {
        void** args = (void**)kernel_args_list[i];
        delete (float*)args[1];
        delete (int*)args[2];
        delete[] args;
        CUDA_CHECK(cudaFree(d_data[i]));
        CUDA_CHECK(cudaGraphExecDestroy(execs[i]));
        CUDA_CHECK(cudaGraphDestroy(graphs[i]));
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
    }
}

// Test Case 7: High concurrency with 16 *heterogeneous* graphs.
TEST_F(MultiStreamTest, ConcurrentLaunchOnSixteenHeterogeneousGraphs) {
    const int num_streams = 16;
    std::vector<cudaStream_t> streams(num_streams);
    std::vector<cudaGraphExec_t> execs(num_streams);
    std::vector<cudaGraph_t> graphs(num_streams);
    std::vector<float*> d_data(num_streams);
    std::vector<std::vector<void*>> kernel_args_storage;

    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamCreateWithFlags(&streams[i], cudaStreamNonBlocking));
        CUDA_CHECK(cudaMalloc(&d_data[i], size));
        CUDA_CHECK(cudaGraphCreate(&graphs[i], 0));
        CUDA_CHECK(cudaMemset(d_data[i], 0, size));

        cudaGraphNode_t last_node = nullptr;
        for (int j = 0; j < i + 1; ++j) {
            float* p_val = new float((float)(j + 1));
            int* p_n = new int(n);
            kernel_args_storage.push_back({&d_data[i], p_val, p_n});
            void** args = kernel_args_storage.back().data();
            
            cudaKernelNodeParams params = {(void*)addValue, {1,1,1}, {1,1,1}, 0, args};
            cudaGraphNode_t new_node;
            CUDA_CHECK(cudaGraphAddKernelNode(&new_node, graphs[i], (last_node ? &last_node : nullptr), (last_node ? 1 : 0), &params));
            last_node = new_node;
        }
        CUDA_CHECK(cudaGraphInstantiate(&execs[i], graphs[i], nullptr, nullptr, 0));
    }

    for (int i = 0; i < num_streams; ++i) CUDA_CHECK(cudaGraphLaunch(execs[i], streams[i]));

    std::vector<float> h_result(n);
    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamSynchronize(streams[i]));
        CUDA_CHECK(cudaMemcpy(h_result.data(), d_data[i], size, cudaMemcpyDeviceToHost));
        float expected_val = (float)((i + 1) * (i + 2) / 2.0);
        for(int j=0; j<n; ++j) {
            ASSERT_FLOAT_EQ(h_result[j], expected_val);
        }
    }

    for(auto& args_vec : kernel_args_storage) {
        delete (float*)args_vec[1];
        delete (int*)args_vec[2];
    }
    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaFree(d_data[i]));
        CUDA_CHECK(cudaGraphExecDestroy(execs[i]));
        CUDA_CHECK(cudaGraphDestroy(graphs[i]));
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
    }
}

// Test Case 8: Dynamic stream lifecycle to check for resource leaks.
TEST_F(MultiStreamTest, DynamicStreamLifecycleWithGraphs) {
    const int cycles = 5;
    const int num_streams_per_cycle = 4;

    for (int i = 0; i < cycles; ++i) {
        std::vector<cudaStream_t> streams(num_streams_per_cycle);
        std::vector<cudaGraphExec_t> execs(num_streams_per_cycle);
        std::vector<cudaGraph_t> graphs(num_streams_per_cycle);
        
        for (int j = 0; j < num_streams_per_cycle; ++j) {
            CUDA_CHECK(cudaStreamCreate(&streams[j]));
            CUDA_CHECK(cudaGraphCreate(&graphs[j], 0));
            float val = 1.0f;
            void* args[] = {&d_a, &val, &n};
            cudaKernelNodeParams params = {(void*)addValue, {1,1,1}, {1,1,1}, 0, args};
            cudaGraphNode_t node;
            CUDA_CHECK(cudaGraphAddKernelNode(&node, graphs[j], nullptr, 0, &params));
            CUDA_CHECK(cudaGraphInstantiate(&execs[j], graphs[j], nullptr, nullptr, 0));
        }

        for (int j = 0; j < num_streams_per_cycle; ++j) CUDA_CHECK(cudaGraphLaunch(execs[j], streams[j]));

        for (int j = 0; j < num_streams_per_cycle; ++j) {
            CUDA_CHECK(cudaStreamSynchronize(streams[j]));
            CUDA_CHECK(cudaGraphExecDestroy(execs[j]));
            CUDA_CHECK(cudaGraphDestroy(graphs[j]));
            CUDA_CHECK(cudaStreamDestroy(streams[j]));
        }
    }
    
    std::vector<float> h_result(n);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_a, size, cudaMemcpyDeviceToHost));
    ASSERT_FLOAT_EQ(h_result[0], (float)cycles * num_streams_per_cycle);
}

// --- Advanced Synchronization Patterns ---

// Test Case 9: Chained "ping-pong" execution between two streams.
TEST_F(MultiStreamTest, PingPongExecutionWithEvents) {
    cudaEvent_t eventA, eventB;
    CUDA_CHECK(cudaEventCreate(&eventA));
    CUDA_CHECK(cudaEventCreate(&eventB));

    cudaGraph_t gA1; CUDA_CHECK(cudaGraphCreate(&gA1, 0));
    float vA1 = 1.0f; void* argsA1[]={&d_a, &vA1, &n};
    cudaKernelNodeParams pA1 = {(void*)setValue,{1,1,1},{256,1,1},0,argsA1};
    cudaGraphNode_t nA1;
    CUDA_CHECK(cudaGraphAddKernelNode(&nA1, gA1, nullptr, 0, &pA1));
    cudaGraphExec_t eA1; CUDA_CHECK(cudaGraphInstantiate(&eA1, gA1, nullptr, nullptr, 0));
    
    cudaGraph_t gB1; CUDA_CHECK(cudaGraphCreate(&gB1, 0));
    float vB1 = 2.0f; void* argsB1[]={&d_b, &vB1, &n};
    cudaKernelNodeParams pB1 = {(void*)setValue,{1,1,1},{256,1,1},0,argsB1};
    cudaGraphNode_t nB1;
    CUDA_CHECK(cudaGraphAddKernelNode(&nB1, gB1, nullptr, 0, &pB1));
    cudaGraphExec_t eB1; CUDA_CHECK(cudaGraphInstantiate(&eB1, gB1, nullptr, nullptr, 0));

    cudaGraph_t gA2; CUDA_CHECK(cudaGraphCreate(&gA2, 0));
    void* argsA2[]={&d_a, &d_b, &n};
    cudaKernelNodeParams pA2 = {(void*)addValue,{1,1,1},{256,1,1},0,argsA2};
    cudaGraphNode_t nA2;
    CUDA_CHECK(cudaGraphAddKernelNode(&nA2, gA2, nullptr, 0, &pA2));
    cudaGraphExec_t eA2; CUDA_CHECK(cudaGraphInstantiate(&eA2, gA2, nullptr, nullptr, 0));

    CUDA_CHECK(cudaGraphLaunch(eA1, streamA));
    CUDA_CHECK(cudaEventRecord(eventA, streamA));
    
    CUDA_CHECK(cudaStreamWaitEvent(streamB, eventA, 0));
    CUDA_CHECK(cudaGraphLaunch(eB1, streamB));
    CUDA_CHECK(cudaEventRecord(eventB, streamB));

    CUDA_CHECK(cudaStreamWaitEvent(streamA, eventB, 0));
    CUDA_CHECK(cudaGraphLaunch(eA2, streamA));
    CUDA_CHECK(cudaStreamSynchronize(streamA));

    std::vector<float> h_a(n);
    CUDA_CHECK(cudaMemcpy(h_a.data(), d_a, size, cudaMemcpyDeviceToHost));
    for(int i=0; i<n; ++i) ASSERT_FLOAT_EQ(h_a[i], 3.0f);

    CUDA_CHECK(cudaEventDestroy(eventA));
    CUDA_CHECK(cudaEventDestroy(eventB));
    CUDA_CHECK(cudaGraphExecDestroy(eA1)); CUDA_CHECK(cudaGraphDestroy(gA1));
    CUDA_CHECK(cudaGraphExecDestroy(eB1)); CUDA_CHECK(cudaGraphDestroy(gB1));
    CUDA_CHECK(cudaGraphExecDestroy(eA2)); CUDA_CHECK(cudaGraphDestroy(gA2));
}

// Test Case 10: Fan-In sync - one graph waits for multiple external events.
TEST_F(MultiStreamTest, FanInSynchronizationWithEvents) {
    cudaStream_t streamC;
    CUDA_CHECK(cudaStreamCreateWithFlags(&streamC, cudaStreamNonBlocking));
    float *d_c;
    CUDA_CHECK(cudaMalloc(&d_c, size));

    cudaEvent_t eventA, eventB, eventC;
    CUDA_CHECK(cudaEventCreate(&eventA));
    CUDA_CHECK(cudaEventCreate(&eventB));
    CUDA_CHECK(cudaEventCreate(&eventC));

    setValue<<<1, 256, 0, streamA>>>((float*)d_a, 10.0f, n);
    CUDA_CHECK(cudaEventRecord(eventA, streamA));

    setValue<<<1, 256, 0, streamB>>>((float*)d_b, 20.0f, n);
    CUDA_CHECK(cudaEventRecord(eventB, streamB));
    
    setValue<<<1, 256, 0, streamC>>>((float*)d_c, 30.0f, n);
    CUDA_CHECK(cudaEventRecord(eventC, streamC));

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    cudaGraphNode_t waitA, waitB, waitC;
    CUDA_CHECK(cudaGraphAddEventWaitNode(&waitA, graph, nullptr, 0, eventA));
    CUDA_CHECK(cudaGraphAddEventWaitNode(&waitB, graph, nullptr, 0, eventB));
    CUDA_CHECK(cudaGraphAddEventWaitNode(&waitC, graph, nullptr, 0, eventC));
    
    cudaGraphNode_t deps[] = {waitA, waitB, waitC};
    
    void* args1[] = {&d_a, &d_b, &n};
    cudaKernelNodeParams params1 = {(void*)addValue, {1,1,1},{256,1,1},0,args1};
    cudaGraphNode_t addNode1;
    CUDA_CHECK(cudaGraphAddKernelNode(&addNode1, graph, deps, 3, &params1));

    void* args2[] = {&d_a, &d_c, &n};
    cudaKernelNodeParams params2 = {(void*)addValue, {1,1,1},{256,1,1},0,args2};
    cudaGraphNode_t addNode2;
    CUDA_CHECK(cudaGraphAddKernelNode(&addNode2, graph, &addNode1, 1, &params2));

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    cudaStream_t launchStream;
    CUDA_CHECK(cudaStreamCreate(&launchStream));
    CUDA_CHECK(cudaGraphLaunch(exec, launchStream));
    CUDA_CHECK(cudaStreamSynchronize(launchStream));

    std::vector<float> h_result(n);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_a, size, cudaMemcpyDeviceToHost));
    for(int i=0; i<n; ++i) ASSERT_FLOAT_EQ(h_result[i], 60.0f);
    
    CUDA_CHECK(cudaFree(d_c));
    CUDA_CHECK(cudaStreamDestroy(streamC));
    CUDA_CHECK(cudaStreamDestroy(launchStream));
    CUDA_CHECK(cudaEventDestroy(eventA));
    CUDA_CHECK(cudaEventDestroy(eventB));
    CUDA_CHECK(cudaEventDestroy(eventC));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test Case 11: Fan-Out sync - one event triggers multiple graphs.
TEST_F(MultiStreamTest, FanOutSynchronizationWithEvent) {
    cudaEvent_t trigger;
    CUDA_CHECK(cudaEventCreate(&trigger));

    cudaGraph_t gA; CUDA_CHECK(cudaGraphCreate(&gA, 0));
    cudaGraphNode_t waitA; CUDA_CHECK(cudaGraphAddEventWaitNode(&waitA, gA, nullptr, 0, trigger));
    float vA=100.0f; void* argsA[]={&d_a,&vA,&n};
    cudaKernelNodeParams pA = {(void*)setValue,{1,1,1},{256,1,1},0,argsA};
    cudaGraphNode_t nodeA;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeA, gA, &waitA, 1, &pA));
    cudaGraphExec_t eA; CUDA_CHECK(cudaGraphInstantiate(&eA, gA, nullptr, nullptr, 0));

    cudaGraph_t gB; CUDA_CHECK(cudaGraphCreate(&gB, 0));
    cudaGraphNode_t waitB; CUDA_CHECK(cudaGraphAddEventWaitNode(&waitB, gB, nullptr, 0, trigger));
    float vB=200.0f; void* argsB[]={&d_b,&vB,&n};
    cudaKernelNodeParams pB = {(void*)setValue,{1,1,1},{256,1,1},0,argsB};
    cudaGraphNode_t nodeB;
    CUDA_CHECK(cudaGraphAddKernelNode(&nodeB, gB, &waitB, 1, &pB));
    cudaGraphExec_t eB; CUDA_CHECK(cudaGraphInstantiate(&eB, gB, nullptr, nullptr, 0));

    CUDA_CHECK(cudaGraphLaunch(eA, streamA));
    CUDA_CHECK(cudaGraphLaunch(eB, streamB));

    cudaStream_t triggerStream;
    CUDA_CHECK(cudaStreamCreate(&triggerStream));
    simple_spin<<<1,1,0,triggerStream>>>(1000);
    CUDA_CHECK(cudaEventRecord(trigger, triggerStream));

    CUDA_CHECK(cudaStreamSynchronize(streamA));
    CUDA_CHECK(cudaStreamSynchronize(streamB));

    std::vector<float> h_a(n), h_b(n);
    CUDA_CHECK(cudaMemcpy(h_a.data(), d_a, size, cudaMemcpyDeviceToHost));
    CUDA_CHECK(cudaMemcpy(h_b.data(), d_b, size, cudaMemcpyDeviceToHost));
    for(int i=0; i<n; ++i) {
        ASSERT_FLOAT_EQ(h_a[i], 100.0f);
        ASSERT_FLOAT_EQ(h_b[i], 200.0f);
    }
    
    CUDA_CHECK(cudaStreamDestroy(triggerStream));
    CUDA_CHECK(cudaEventDestroy(trigger));
    CUDA_CHECK(cudaGraphExecDestroy(eA)); CUDA_CHECK(cudaGraphDestroy(gA));
    CUDA_CHECK(cudaGraphExecDestroy(eB)); CUDA_CHECK(cudaGraphDestroy(gB));
}

// Test Case 12: Sync graph on non-blocking stream with default stream.
TEST_F(MultiStreamTest, SyncWithDefaultStream) {
    cudaEvent_t event_on_default;
    CUDA_CHECK(cudaEventCreate(&event_on_default));
    
    setValue<<<1, 256, 0, 0>>>((float*)d_a, 42.0f, n);
    CUDA_CHECK(cudaEventRecord(event_on_default, 0));
    
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaGraphNode_t wait_node;
    CUDA_CHECK(cudaGraphAddEventWaitNode(&wait_node, graph, nullptr, 0, event_on_default));
    
    float val_to_add = 8.0f;
    void* args[] = {&d_a, &val_to_add, &n};
    cudaKernelNodeParams params = {(void*)addValue, {1,1,1}, {256,1,1}, 0, args};
    cudaGraphNode_t add_node;
    CUDA_CHECK(cudaGraphAddKernelNode(&add_node, graph, &wait_node, 1, &params));
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    CUDA_CHECK(cudaGraphLaunch(exec, streamA));
    CUDA_CHECK(cudaStreamSynchronize(streamA));
    
    std::vector<float> h_result(n);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_a, size, cudaMemcpyDeviceToHost));
    for(int i=0; i<n; ++i) {
        ASSERT_FLOAT_EQ(h_result[i], 50.0f);
    }
    
    CUDA_CHECK(cudaEventDestroy(event_on_default));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}
