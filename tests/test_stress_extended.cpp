#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <random>
#include <algorithm>
#include <iostream>
#include <thread>
#include <tuple>

// Re-usable CUDA error checking macro
#define CUDA_CHECK(err)                                         \
    do {                                                        \
        cudaError_t err_ = (err);                               \
        if (err_ != cudaSuccess) {                              \
            std::cerr << "CUDA error: " << cudaGetErrorString(err_) \
                      << " at " << __FILE__ << ":" << __LINE__ << std::endl; \
            FAIL();                                             \
        }                                                       \
    } while (0)

// --- Kernels for Testing ---
__global__ void simple_noop_kernel() {}
__global__ void another_simple_noop_kernel() {}

__global__ void simple_vector_add(float* c, const float* a, const float* b, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        c[i] = a[i] + b[i];
    }
}

// --- Test Suite 1: Parameterized Graph Topology Stress (30 Tests) ---
enum class GraphTopology { DEEP_CHAIN, WIDE_FAN_OUT, WIDE_FAN_IN, BINARY_TREE, RANDOM_ACYCLIC };

struct TopologyTestParams {
    GraphTopology topology;
    int num_nodes;
};

std::string topologyTestParamsToString(const ::testing::TestParamInfo<TopologyTestParams>& info) {
    std::string name;
    switch (info.param.topology) {
        case GraphTopology::DEEP_CHAIN:      name = "DeepChain"; break;
        case GraphTopology::WIDE_FAN_OUT:    name = "WideFanOut"; break;
        case GraphTopology::WIDE_FAN_IN:     name = "WideFanIn"; break;
        case GraphTopology::BINARY_TREE:     name = "BinaryTree"; break;
        case GraphTopology::RANDOM_ACYCLIC:  name = "RandomAcyclic"; break;
    }
    return name + "_" + std::to_string(info.param.num_nodes) + "Nodes";
}

class ExtendedStressTest : public ::testing::TestWithParam<TopologyTestParams> {};

TEST_P(ExtendedStressTest, GraphCreationAndInstantiation) {
    auto params = GetParam();
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    std::vector<cudaGraphNode_t> nodes(params.num_nodes);
    cudaKernelNodeParams kernel_params = {};
    kernel_params.func = (void*)simple_noop_kernel;
    kernel_params.gridDim = {1, 1, 1};
    kernel_params.blockDim = {1, 1, 1};

    for (int i = 0; i < params.num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, nullptr, 0, &kernel_params));
    }

    switch (params.topology) {
        case GraphTopology::DEEP_CHAIN:
            for (int i = 1; i < params.num_nodes; ++i) CUDA_CHECK(cudaGraphAddDependencies(graph, &nodes[i], &nodes[i - 1], 1));
            break;
        case GraphTopology::WIDE_FAN_OUT:
            if (params.num_nodes > 1) for (int i = 1; i < params.num_nodes; ++i) CUDA_CHECK(cudaGraphAddDependencies(graph, &nodes[i], &nodes[0], 1));
            break;
        case GraphTopology::WIDE_FAN_IN:
            if (params.num_nodes > 1) for (int i = 0; i < params.num_nodes - 1; ++i) CUDA_CHECK(cudaGraphAddDependencies(graph, &nodes[params.num_nodes - 1], &nodes[i], 1));
            break;
        case GraphTopology::BINARY_TREE:
             for (int i = 0; i < params.num_nodes / 2; ++i) {
                if (2 * i + 1 < params.num_nodes) CUDA_CHECK(cudaGraphAddDependencies(graph, &nodes[2 * i + 1], &nodes[i], 1));
                if (2 * i + 2 < params.num_nodes) CUDA_CHECK(cudaGraphAddDependencies(graph, &nodes[2 * i + 2], &nodes[i], 1));
            }
            break;
        case GraphTopology::RANDOM_ACYCLIC:
            if (params.num_nodes > 1) {
                std::mt19937 rng(std::random_device{}());
                for (int i = 1; i < params.num_nodes; ++i) {
                    int num_deps = std::uniform_int_distribution<int>(1, std::min(i, 4))(rng);
                    for(int k=0; k < num_deps; ++k) {
                        int dep_idx = std::uniform_int_distribution<int>(0, i - 1)(rng);
                        cudaGraphAddDependencies(graph, &nodes[i], &nodes[dep_idx], 1);
                    }
                }
            }
            break;
    }

    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    // Basic launch to ensure it works
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(TopologySuite, ExtendedStressTest,
    ::testing::Values(
        TopologyTestParams{GraphTopology::DEEP_CHAIN, 512}, TopologyTestParams{GraphTopology::DEEP_CHAIN, 1024}, TopologyTestParams{GraphTopology::DEEP_CHAIN, 2048}, TopologyTestParams{GraphTopology::DEEP_CHAIN, 4096}, TopologyTestParams{GraphTopology::DEEP_CHAIN, 8192},
        TopologyTestParams{GraphTopology::WIDE_FAN_OUT, 512}, TopologyTestParams{GraphTopology::WIDE_FAN_OUT, 1024}, TopologyTestParams{GraphTopology::WIDE_FAN_OUT, 2048}, TopologyTestParams{GraphTopology::WIDE_FAN_OUT, 4096}, TopologyTestParams{GraphTopology::WIDE_FAN_OUT, 8192},
        TopologyTestParams{GraphTopology::WIDE_FAN_IN, 512}, TopologyTestParams{GraphTopology::WIDE_FAN_IN, 1024}, TopologyTestParams{GraphTopology::WIDE_FAN_IN, 2048}, TopologyTestParams{GraphTopology::WIDE_FAN_IN, 4096}, TopologyTestParams{GraphTopology::WIDE_FAN_IN, 8192},
        TopologyTestParams{GraphTopology::BINARY_TREE, 511}, TopologyTestParams{GraphTopology::BINARY_TREE, 1023}, TopologyTestParams{GraphTopology::BINARY_TREE, 2047}, TopologyTestParams{GraphTopology::BINARY_TREE, 4095}, TopologyTestParams{GraphTopology::BINARY_TREE, 8191},
        TopologyTestParams{GraphTopology::RANDOM_ACYCLIC, 512}, TopologyTestParams{GraphTopology::RANDOM_ACYCLIC, 1024}, TopologyTestParams{GraphTopology::RANDOM_ACYCLIC, 2048}, TopologyTestParams{GraphTopology::RANDOM_ACYCLIC, 4096}, TopologyTestParams{GraphTopology::RANDOM_ACYCLIC, 8192}
    ),
    topologyTestParamsToString
);

// --- Test Suite 2: Memory and Concurrency Stress (4*4*5 = 80 Tests) ---
using MemConParams = std::tuple<int, int, int>; // nodes, data_size_kb, concurrency

class MemoryConcurrencyStressTest : public ::testing::TestWithParam<MemConParams> {};

TEST_P(MemoryConcurrencyStressTest, GraphWithMemoryLoad) {
    auto p = GetParam();
    int num_nodes = std::get<0>(p);
    size_t data_size_kb = std::get<1>(p);
    int concurrency_level = std::get<2>(p);

    const size_t data_size = data_size_kb * 1024;
    const int n = data_size / sizeof(float);
    if (n == 0) SUCCEED();

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    std::vector<float*> d_a, d_b, d_c;
    std::vector<void*> all_kernel_args;

    for (int i = 0; i < num_nodes; ++i) {
        float *da, *db, *dc;
        CUDA_CHECK(cudaMalloc(&da, data_size));
        CUDA_CHECK(cudaMalloc(&db, data_size));
        CUDA_CHECK(cudaMalloc(&dc, data_size));
        d_a.push_back(da); d_b.push_back(db); d_c.push_back(dc);

        void** kernel_args = new void*[4];
        kernel_args[0] = &d_c[i]; kernel_args[1] = &d_a[i]; kernel_args[2] = &d_b[i]; kernel_args[3] = (void*)&n;
        all_kernel_args.push_back(kernel_args);
        
        cudaKernelNodeParams params = {(void*)simple_vector_add, {(unsigned int)(n + 255) / 256, 1, 1}, {256, 1, 1}, 0, kernel_args};
        CUDA_CHECK(cudaGraphAddKernelNode(nullptr, graph, nullptr, 0, &params));
    }
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    std::vector<cudaStream_t> streams(concurrency_level);
    for(int i=0; i < concurrency_level; ++i) {
        CUDA_CHECK(cudaStreamCreate(&streams[i]));
        CUDA_CHECK(cudaGraphLaunch(exec, streams[i]));
    }
    
    for(int i=0; i < concurrency_level; ++i) {
        CUDA_CHECK(cudaStreamSynchronize(streams[i]));
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
    }

    for(size_t i = 0; i < d_a.size(); ++i) {
        CUDA_CHECK(cudaFree(d_a[i])); CUDA_CHECK(cudaFree(d_b[i])); CUDA_CHECK(cudaFree(d_c[i]));
        delete[] (void**)all_kernel_args[i];
    }
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(MemorySuite, MemoryConcurrencyStressTest,
    ::testing::Combine(
        ::testing::Values(16, 64, 128, 256),
        ::testing::Values(64, 128, 256, 512),
        ::testing::Values(1, 2, 4, 8, 16)
    ),
    [](const auto& info) {
        return "Nodes" + std::to_string(std::get<0>(info.param)) +
               "__Data" + std::to_string(std::get<1>(info.param)) + "KB" +
               "__Concurrency" + std::to_string(std::get<2>(info.param));
    }
);

// --- Test Suite 3: Dynamic Graph Update Stress (4*4*5 = 80 Tests) ---
using GraphUpdateParams = std::tuple<int, int, int>; // nodes, update_iters, update_pct

class GraphUpdateStressTest : public ::testing::TestWithParam<GraphUpdateParams> {};

TEST_P(GraphUpdateStressTest, RepetitiveKernelParamUpdate) {
    auto p = GetParam();
    int num_nodes = std::get<0>(p);
    int update_iterations = std::get<1>(p);
    int update_percentage = std::get<2>(p);

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    std::vector<cudaGraphNode_t> nodes(num_nodes);
    cudaKernelNodeParams kernel_params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};

    for (int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, nullptr, 0, &kernel_params));
    }
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));

    std::vector<cudaGraphNode_t> nodes_to_update;
    int num_to_update = num_nodes * update_percentage / 100;
    std::shuffle(nodes.begin(), nodes.end(), std::mt19937(std::random_device{}()));
    for(int i = 0; i < num_to_update; ++i) nodes_to_update.push_back(nodes[i]);

    cudaKernelNodeParams new_params = {(void*)another_simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};

    for (int i = 0; i < update_iterations; ++i) {
        CUDA_CHECK(cudaGraphLaunch(exec, stream));
        
        new_params.func = (i % 2 == 0) ? (void*)another_simple_noop_kernel : (void*)simple_noop_kernel;
        for(const auto& node : nodes_to_update) {
            CUDA_CHECK(cudaGraphKernelNodeSetParams(node, &new_params));
        }

        cudaGraphExecUpdateResult update_result;
        CUDA_CHECK(cudaGraphExecUpdate(exec, graph, nullptr, &update_result));
        ASSERT_EQ(update_result, cudaGraphExecUpdateSuccess);
    }
    
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(UpdateSuite, GraphUpdateStressTest,
    ::testing::Combine(
        ::testing::Values(64, 256, 1024, 2048),
        ::testing::Values(10, 50, 100, 200),
        ::testing::Values(10, 25, 50, 75, 100)
    ),
    [](const auto& info) {
        return "Nodes" + std::to_string(std::get<0>(info.param)) +
               "__Iters" + std::to_string(std::get<1>(info.param)) +
               "__Update" + std::to_string(std::get<2>(info.param)) + "pct";
    }
);

// --- Test Suite 4: Graph Cloning Stress (4*5 = 20 Tests) ---
using CloneParams = std::tuple<int, int>; // num_nodes, clone_count

class GraphCloneStressTest : public ::testing::TestWithParam<CloneParams> {};

TEST_P(GraphCloneStressTest, RepetitiveCloning) {
    auto p = GetParam();
    int num_nodes = std::get<0>(p);
    int clone_count = std::get<1>(p);

    cudaGraph_t base_graph;
    CUDA_CHECK(cudaGraphCreate(&base_graph, 0));
    cudaKernelNodeParams kernel_params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};
    for(int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(nullptr, base_graph, nullptr, 0, &kernel_params));
    }
    
    for(int i = 0; i < clone_count; ++i) {
        cudaGraph_t cloned_graph;
        CUDA_CHECK(cudaGraphClone(&cloned_graph, base_graph));
        // Also instantiate to make it a more meaningful test
        cudaGraphExec_t exec;
        CUDA_CHECK(cudaGraphInstantiate(&exec, cloned_graph, nullptr, nullptr, 0));
        CUDA_CHECK(cudaGraphExecDestroy(exec));
        CUDA_CHECK(cudaGraphDestroy(cloned_graph));
    }
    
    CUDA_CHECK(cudaGraphDestroy(base_graph));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(CloneSuite, GraphCloneStressTest,
    ::testing::Combine(
        ::testing::Values(256, 1024, 4096, 8192),
        ::testing::Values(10, 50, 100, 250, 500)
    ),
    [](const auto& info) {
        return "Nodes" + std::to_string(std::get<0>(info.param)) +
               "__Clones" + std::to_string(std::get<1>(info.param));
    }
);

// --- Test Suite 5: Mixed Node Type Stress (4*4*4*4 = 256 Tests) ---
using MixedNodeParams = std::tuple<int, int, int, int>; // kernel_nodes, memcpy_nodes, event_nodes, memset_nodes

class MixedNodeStressTest : public ::testing::TestWithParam<MixedNodeParams> {};

TEST_P(MixedNodeStressTest, GraphWithVariousNodeTypes) {
    auto p = GetParam();
    int num_kernel_nodes = std::get<0>(p);
    int num_memcpy_nodes = std::get<1>(p);
    int num_event_nodes = std::get<2>(p);
    int num_memset_nodes = std::get<3>(p);
    
    if (num_kernel_nodes == 0 && num_memcpy_nodes == 0 && num_event_nodes == 0 && num_memset_nodes == 0) {
        SUCCEED();
        return;
    }

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));

    std::vector<cudaGraphNode_t> all_nodes;
    cudaGraphNode_t last_node = nullptr;

    // Add Kernel Nodes
    cudaKernelNodeParams kernel_params = {(void*)simple_noop_kernel, {1,1,1}, {1,1,1}, 0, nullptr};
    for(int i = 0; i < num_kernel_nodes; ++i) {
        cudaGraphNode_t kernel_node;
        CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph, last_node ? &last_node : nullptr, last_node ? 1 : 0, &kernel_params));
        all_nodes.push_back(kernel_node);
        last_node = kernel_node;
    }

    // Add Memcpy Nodes
    const size_t data_size = 1024;
    char *d_src, *d_dst;
    CUDA_CHECK(cudaMalloc(&d_src, data_size));
    CUDA_CHECK(cudaMalloc(&d_dst, data_size));
    for(int i = 0; i < num_memcpy_nodes; ++i) {
        cudaGraphNode_t memcpy_node;
        CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpy_node, graph, last_node ? &last_node : nullptr, last_node ? 1 : 0, d_dst, d_src, data_size, cudaMemcpyDeviceToDevice));
        all_nodes.push_back(memcpy_node);
        last_node = memcpy_node;
    }

    // Add Memset Nodes
    for(int i = 0; i < num_memset_nodes; ++i) {
        cudaGraphNode_t memset_node;
        cudaMemsetParams memset_params = {};
        memset_params.dst = d_dst;
        memset_params.value = 0;
        memset_params.pitch = 0;
        memset_params.elementSize = sizeof(char);
        memset_params.width = data_size;
        memset_params.height = 1;
        CUDA_CHECK(cudaGraphAddMemsetNode(&memset_node, graph, last_node ? &last_node : nullptr, last_node ? 1 : 0, &memset_params));
        all_nodes.push_back(memset_node);
        last_node = memset_node;
    }

    // Add Event Nodes
    std::vector<cudaEvent_t> events(num_event_nodes);
    for(int i = 0; i < num_event_nodes; ++i) {
        CUDA_CHECK(cudaEventCreate(&events[i]));
        cudaGraphNode_t event_rec_node, event_wait_node;
        CUDA_CHECK(cudaGraphAddEventRecordNode(&event_rec_node, graph, last_node ? &last_node : nullptr, last_node ? 1 : 0, events[i]));
        last_node = event_rec_node;
        // The wait node depends on the record node
        CUDA_CHECK(cudaGraphAddEventWaitNode(&event_wait_node, graph, &last_node, 1, events[i]));
        last_node = event_wait_node;
    }

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaFree(d_src));
    CUDA_CHECK(cudaFree(d_dst));
    for(const auto& event : events) {
        CUDA_CHECK(cudaEventDestroy(event));
    }
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(MixedNodeSuite, MixedNodeStressTest,
    ::testing::Combine(
        ::testing::Values(0, 10, 50, 100),         // num_kernel_nodes
        ::testing::Values(0, 10, 50, 100),         // num_memcpy_nodes
        ::testing::Values(0, 10, 20, 40),          // num_event_nodes
        ::testing::Values(0, 10, 50, 100)          // num_memset_nodes
    ),
    [](const auto& info) {
        return "Kernels" + std::to_string(std::get<0>(info.param)) +
               "__Memcpy" + std::to_string(std::get<1>(info.param)) +
               "__Events" + std::to_string(std::get<2>(info.param)) +
               "__Memset" + std::to_string(std::get<3>(info.param));
    }
);

// --- Test Suite 6: Graph Update with Topology Change Stress (4*4*4 = 64 Tests) ---
using TopologyUpdateParams = std::tuple<int, int, int>; // base_nodes, nodes_to_add, update_iterations

class TopologyUpdateStressTest : public ::testing::TestWithParam<TopologyUpdateParams> {};

TEST_P(TopologyUpdateStressTest, AddRemoveNodesAndUpdate) {
    auto p = GetParam();
    int base_nodes = std::get<0>(p);
    int nodes_to_add = std::get<1>(p);
    int update_iterations = std::get<2>(p);

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    std::vector<cudaGraphNode_t> base_node_vec(base_nodes);
    cudaKernelNodeParams kernel_params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};

    for (int i = 0; i < base_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&base_node_vec[i], graph, nullptr, 0, &kernel_params));
    }
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));

    for (int i = 0; i < update_iterations; ++i) {
        // Run the original graph
        CUDA_CHECK(cudaGraphLaunch(exec, stream));
        CUDA_CHECK(cudaStreamSynchronize(stream));

        // Add new nodes to the graph object
        std::vector<cudaGraphNode_t> new_nodes(nodes_to_add);
        for(int j = 0; j < nodes_to_add; ++j) {
            // New nodes are independent
            CUDA_CHECK(cudaGraphAddKernelNode(&new_nodes[j], graph, nullptr, 0, &kernel_params));
        }

        // According to CUDA docs, adding nodes that don't change the existing topology
        // should result in a successful update.
        cudaGraphExecUpdateResult update_result;
        cudaGraphExecUpdateInfo update_info = {};
        cudaError_t update_err = cudaGraphExecUpdate(exec, graph, &update_info, &update_result);
        
        ASSERT_EQ(update_err, cudaSuccess);
        ASSERT_EQ(update_result, cudaGraphExecUpdateSuccess);

        // Run the updated graph
        CUDA_CHECK(cudaGraphLaunch(exec, stream));
        CUDA_CHECK(cudaStreamSynchronize(stream));

        // Now, remove the added nodes to revert the graph for the next iteration
        for(int j = 0; j < nodes_to_add; ++j) {
             CUDA_CHECK(cudaGraphRemoveNodes(graph, &new_nodes[j], 1));
        }
        
        // Update again to restore the original state for the executable
        update_err = cudaGraphExecUpdate(exec, graph, &update_info, &update_result);
        ASSERT_EQ(update_err, cudaSuccess);
        ASSERT_EQ(update_result, cudaGraphExecUpdateSuccess);
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(TopologyUpdateSuite, TopologyUpdateStressTest,
    ::testing::Combine(
        ::testing::Values(128, 512, 1024, 2048),     // base_nodes
        ::testing::Values(16, 32, 64, 128),         // nodes_to_add
        ::testing::Values(5, 10, 20, 50)            // update_iterations
    ),
    [](const auto& info) {
        return "Base" + std::to_string(std::get<0>(info.param)) +
               "__Add" + std::to_string(std::get<1>(info.param)) +
               "__Iters" + std::to_string(std::get<2>(info.param));
    }
);

// --- Test Suite 8: Child Graph (Nested Graph) Stress (4*4*4 = 64 Tests) ---
using ChildGraphParams = std::tuple<int, int, int>; // nesting_depth, nodes_per_graph, breadth

// Helper function to create a simple graph with a chain of kernel nodes
static cudaGraph_t create_simple_chain_graph(int num_nodes) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    if (num_nodes == 0) return graph;

    cudaKernelNodeParams params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    
    CUDA_CHECK(cudaGraphAddKernelNode(&nodes[0], graph, nullptr, 0, &params));
    for (int i = 1; i < num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, &nodes[i - 1], 1, &params));
    }
    return graph;
}

class ChildGraphStressTest : public ::testing::TestWithParam<ChildGraphParams> {};

TEST_P(ChildGraphStressTest, DeepAndWideNesting) {
    auto p = GetParam();
    int nesting_depth = std::get<0>(p);
    int nodes_per_graph = std::get<1>(p);
    int breadth = std::get<2>(p);
    
    if (nesting_depth == 0) {
        SUCCEED();
        return;
    }

    // Create the deepest-level child graphs first
    std::vector<cudaGraph_t> child_graph_templates;
    for (int i = 0; i < breadth; ++i) {
        child_graph_templates.push_back(create_simple_chain_graph(nodes_per_graph));
    }

    // Nest upwards
    cudaGraph_t current_parent_graph = nullptr;
    for (int d = 0; d < nesting_depth; ++d) {
        current_parent_graph = create_simple_chain_graph(2); // Parent has a couple of its own nodes
        
        // Add all children from the previous level to this new parent
        for (const auto& child_template : child_graph_templates) {
            CUDA_CHECK(cudaGraphAddChildGraphNode(nullptr, current_parent_graph, nullptr, 0, child_template));
        }

        // Clean up the templates from the previous level
        for (auto& tpl : child_graph_templates) {
            CUDA_CHECK(cudaGraphDestroy(tpl));
        }
        child_graph_templates.clear();
        
        // If we are not at the top level yet, the current parent becomes the template for the next level up
        if (d < nesting_depth - 1) {
             child_graph_templates.push_back(current_parent_graph);
        }
    }

    ASSERT_NE(current_parent_graph, nullptr);
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, current_parent_graph, nullptr, nullptr, 0));
    
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(current_parent_graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(ChildGraphSuite, ChildGraphStressTest,
    ::testing::Combine(
        // Expanded values for more comprehensive child graph testing
        ::testing::Values(1, 2, 3, 4, 6, 8, 10),      // nesting_depth
        ::testing::Values(8, 16, 32, 64, 128, 256),    // nodes_per_graph
        ::testing::Values(1, 2, 4, 8, 16)             // breadth
    ),
    [](const auto& info) {
        return "Depth" + std::to_string(std::get<0>(info.param)) +
               "__Nodes" + std::to_string(std::get<1>(info.param)) +
               "__Breadth" + std::to_string(std::get<2>(info.param));
    }
);

// --- Test Suite 9: Kernel Parameter Update with Different Sizes (4*4*4 = 64 Tests) ---
using KernelParamUpdateParams = std::tuple<int, int, int>; // num_nodes, base_size_kb, update_size_kb

class KernelParamUpdateStressTest : public ::testing::TestWithParam<KernelParamUpdateParams> {};

TEST_P(KernelParamUpdateStressTest, UpdateKernelNodeWithDifferentDataSizes) {
    auto p = GetParam();
    int num_nodes = std::get<0>(p);
    size_t base_size_kb = std::get<1>(p);
    size_t update_size_kb = std::get<2>(p);

    const size_t base_n = base_size_kb * 1024 / sizeof(float);
    const size_t update_n = update_size_kb * 1024 / sizeof(float);

    // --- Setup initial graph with base size ---
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    std::vector<cudaGraphNode_t> nodes(num_nodes);
    std::vector<void*> all_kernel_args;
    std::vector<float*> d_a_base, d_b_base, d_c_base;

    for (int i = 0; i < num_nodes; ++i) {
        float *da, *db, *dc;
        CUDA_CHECK(cudaMalloc(&da, base_n * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&db, base_n * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&dc, base_n * sizeof(float)));
        d_a_base.push_back(da); d_b_base.push_back(db); d_c_base.push_back(dc);
        
        void** kernel_args = new void*[4];
        kernel_args[0] = &d_c_base[i]; kernel_args[1] = &d_a_base[i]; kernel_args[2] = &d_b_base[i];
        int n_arg = base_n;
        kernel_args[3] = &n_arg; // Important: Need to point to a stable memory location for n
        all_kernel_args.push_back(kernel_args);

        cudaKernelNodeParams params = {(void*)simple_vector_add, {(unsigned int)(base_n + 255) / 256, 1, 1}, {256, 1, 1}, 0, kernel_args};
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, nullptr, 0, &params));
    }

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    // --- Prepare data for the update ---
    std::vector<float*> d_a_update, d_b_update, d_c_update;
    for (int i = 0; i < num_nodes; ++i) {
        float *da, *db, *dc;
        CUDA_CHECK(cudaMalloc(&da, update_n * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&db, update_n * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&dc, update_n * sizeof(float)));
        d_a_update.push_back(da); d_b_update.push_back(db); d_c_update.push_back(dc);
    }
    
    // --- Update the node parameters and the executable ---
    int n_update_arg = update_n;
    for(int i = 0; i < num_nodes; ++i) {
        void** new_args = new void*[4];
        new_args[0] = &d_c_update[i];
        new_args[1] = &d_a_update[i];
        new_args[2] = &d_b_update[i];
        new_args[3] = &n_update_arg;

        cudaKernelNodeParams new_params = {(void*)simple_vector_add, {(unsigned int)(update_n + 255) / 256, 1, 1}, {256, 1, 1}, 0, new_args};
        CUDA_CHECK(cudaGraphKernelNodeSetParams(nodes[i], &new_params));
        delete[] new_args; // Params are copied internally
    }

    cudaGraphExecUpdateResult update_result;
    CUDA_CHECK(cudaGraphExecUpdate(exec, graph, nullptr, &update_result));
    ASSERT_EQ(update_result, cudaGraphExecUpdateSuccess);

    // --- Launch and cleanup ---
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    for(size_t i = 0; i < d_a_base.size(); ++i) {
        CUDA_CHECK(cudaFree(d_a_base[i])); CUDA_CHECK(cudaFree(d_b_base[i])); CUDA_CHECK(cudaFree(d_c_base[i]));
        CUDA_CHECK(cudaFree(d_a_update[i])); CUDA_CHECK(cudaFree(d_b_update[i])); CUDA_CHECK(cudaFree(d_c_update[i]));
        delete[] (void**)all_kernel_args[i];
    }
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(KernelParamUpdateSuite, KernelParamUpdateStressTest,
    ::testing::Combine(
        ::testing::Values(16, 32, 64, 128),      // num_nodes
        ::testing::Values(64, 128, 256, 512),    // base_size_kb
        ::testing::Values(64, 128, 256, 512)     // update_size_kb
    ),
    [](const auto& info) {
        return "Nodes" + std::to_string(std::get<0>(info.param)) +
               "__Base" + std::to_string(std::get<1>(info.param)) + "KB" +
               "__Update" + std::to_string(std::get<2>(info.param)) + "KB";
    }
);

// --- Test Suite 10: Child Graph Dependencies on Parent Nodes (4*4*4 = 64 Tests) ---
using ChildDependencyParams = std::tuple<int, int, int>; // nodes_in_parent, nodes_in_child, num_dependencies

class ChildGraphDependencyStressTest : public ::testing::TestWithParam<ChildDependencyParams> {};

TEST_P(ChildGraphDependencyStressTest, ChildGraphWithExternalDependencies) {
    auto p = GetParam();
    int nodes_in_parent = std::get<0>(p);
    int nodes_in_child = std::get<1>(p);
    int num_dependencies = std::get<2>(p);
    
    // Ensure there are enough nodes to create dependencies
    if (nodes_in_parent < num_dependencies || nodes_in_child < 1) {
        SUCCEED();
        return;
    }

    cudaGraph_t parent_graph;
    CUDA_CHECK(cudaGraphCreate(&parent_graph, 0));

    // Create parent nodes
    std::vector<cudaGraphNode_t> parent_nodes(nodes_in_parent);
    cudaKernelNodeParams kernel_params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};
    for (int i = 0; i < nodes_in_parent; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&parent_nodes[i], parent_graph, nullptr, 0, &kernel_params));
    }
    
    // Create a child graph template
    cudaGraph_t child_graph = create_simple_chain_graph(nodes_in_child);
    
    // Select parent nodes to be dependencies for the child graph
    std::vector<cudaGraphNode_t> dep_nodes;
    std::mt19937 rng(std::random_device{}());
    std::shuffle(parent_nodes.begin(), parent_nodes.end(), rng);
    for(int i = 0; i < num_dependencies; ++i) {
        dep_nodes.push_back(parent_nodes[i]);
    }
    
    // Add the child graph with dependencies
    cudaGraphNode_t child_graph_node;
    CUDA_CHECK(cudaGraphAddChildGraphNode(&child_graph_node, parent_graph, dep_nodes.data(), dep_nodes.size(), child_graph));
    
    // Create a final parent node that depends on the child graph completion
    cudaGraphNode_t final_node;
    CUDA_CHECK(cudaGraphAddKernelNode(&final_node, parent_graph, &child_graph_node, 1, &kernel_params));

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, parent_graph, nullptr, nullptr, 0));

    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(parent_graph));
    CUDA_CHECK(cudaGraphDestroy(child_graph)); // Destroy the template
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(ChildDependencySuite, ChildGraphDependencyStressTest,
    ::testing::Combine(
        ::testing::Values(32, 64, 128, 256),       // nodes_in_parent
        ::testing::Values(16, 32, 64, 128),        // nodes_in_child
        ::testing::Values(1, 4, 8, 16)             // num_dependencies
    ),
    [](const auto& info) {
        return "ParentNodes" + std::to_string(std::get<0>(info.param)) +
               "__ChildNodes" + std::to_string(std::get<1>(info.param)) +
               "__Deps" + std::to_string(std::get<2>(info.param));
    }
);

// --- Test Suite 11: Update Executable Graph with Child Graph (4*4*2 = 32 Tests) ---
enum class UpdateTarget { PARENT_NODE, CHILD_NODE };

using UpdateWithChildParams = std::tuple<int, int, UpdateTarget>; // nodes_in_parent, nodes_in_child, update_target

class UpdateWithChildGraphStressTest : public ::testing::TestWithParam<UpdateWithChildParams> {};

TEST_P(UpdateWithChildGraphStressTest, UpdateWithChildGraph) {
    auto p = GetParam();
    int nodes_in_parent = std::get<0>(p);
    int nodes_in_child = std::get<1>(p);
    UpdateTarget update_target = std::get<2>(p);
    
    if (nodes_in_parent < 1 || nodes_in_child < 1) {
        SUCCEED();
        return;
    }

    cudaGraph_t parent_graph;
    CUDA_CHECK(cudaGraphCreate(&parent_graph, 0));

    // Create parent nodes
    std::vector<cudaGraphNode_t> parent_nodes(nodes_in_parent);
    cudaKernelNodeParams kernel_params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};
    for (int i = 0; i < nodes_in_parent; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&parent_nodes[i], parent_graph, nullptr, 0, &kernel_params));
    }

    // Create child graph template
    cudaGraph_t child_graph;
    cudaGraphNode_t node_to_update_in_child;
    CUDA_CHECK(cudaGraphCreate(&child_graph, 0));
    cudaKernelNodeParams child_kernel_params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node_to_update_in_child, child_graph, nullptr, 0, &child_kernel_params));

    // Add child graph to parent
    cudaGraphNode_t child_graph_node;
    CUDA_CHECK(cudaGraphAddChildGraphNode(&child_graph_node, parent_graph, &parent_nodes[0], 1, child_graph));
    
    // Instantiate the executable graph
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, parent_graph, nullptr, nullptr, 0));

    // Choose what to update
    cudaKernelNodeParams new_kernel_params = {(void*)another_simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};
    if (update_target == UpdateTarget::PARENT_NODE) {
        CUDA_CHECK(cudaGraphKernelNodeSetParams(parent_nodes[0], &new_kernel_params));
    } else { // CHILD_NODE
        // Note: We update the node in the original child graph object, not the child_graph_node handle
        CUDA_CHECK(cudaGraphKernelNodeSetParams(node_to_update_in_child, &new_kernel_params));
    }
    
    // Perform the update
    cudaGraphExecUpdateResult update_result;
    cudaError_t update_err = cudaGraphExecUpdate(exec, parent_graph, nullptr, &update_result);

    // This is the key check: the update should succeed
    ASSERT_EQ(update_err, cudaSuccess);
    ASSERT_EQ(update_result, cudaGraphExecUpdateSuccess);
    
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Cleanup
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(parent_graph));
    CUDA_CHECK(cudaGraphDestroy(child_graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(UpdateWithChildSuite, UpdateWithChildGraphStressTest,
    ::testing::Combine(
        ::testing::Values(16, 64, 128, 256),            // nodes_in_parent
        ::testing::Values(16, 64, 128, 256),            // nodes_in_child
        ::testing::Values(UpdateTarget::PARENT_NODE, UpdateTarget::CHILD_NODE) // update_target
    ),
    [](const auto& info) {
        return "Parent" + std::to_string(std::get<0>(info.param)) +
               "__Child" + std::to_string(std::get<1>(info.param)) +
               "__Update" + (std::get<2>(info.param) == UpdateTarget::PARENT_NODE ? "Parent" : "Child");
    }
);

// --- Test Suite 12: Multiple Distinct Child Graphs with Dependencies (4*4*4 = 64 Tests) ---
using MultiChildParams = std::tuple<int, int, int>; // num_child_graphs, base_nodes_per_child, node_increment_per_child

class MultiChildGraphStressTest : public ::testing::TestWithParam<MultiChildParams> {};

TEST_P(MultiChildGraphStressTest, MultipleDistinctChildrenWithChainedDependencies) {
    auto p = GetParam();
    int num_child_graphs = std::get<0>(p);
    int base_nodes_per_child = std::get<1>(p);
    int node_increment_per_child = std::get<2>(p);
    
    if (num_child_graphs == 0) {
        SUCCEED();
        return;
    }

    cudaGraph_t parent_graph;
    CUDA_CHECK(cudaGraphCreate(&parent_graph, 0));

    // Create a set of distinct child graph templates
    std::vector<cudaGraph_t> child_templates;
    for (int i = 0; i < num_child_graphs; ++i) {
        int num_nodes = base_nodes_per_child + i * node_increment_per_child;
        child_templates.push_back(create_simple_chain_graph(num_nodes));
    }

    // Add child graphs to the parent with chained dependencies
    std::vector<cudaGraphNode_t> child_graph_nodes(num_child_graphs);
    cudaGraphNode_t last_child_node = nullptr;
    for (int i = 0; i < num_child_graphs; ++i) {
        CUDA_CHECK(cudaGraphAddChildGraphNode(
            &child_graph_nodes[i], 
            parent_graph, 
            last_child_node ? &last_child_node : nullptr, 
            last_child_node ? 1 : 0, 
            child_templates[i]
        ));
        last_child_node = child_graph_nodes[i];
    }
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, parent_graph, nullptr, nullptr, 0));

    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Cleanup
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(parent_graph));
    for(auto& tpl : child_templates) {
        CUDA_CHECK(cudaGraphDestroy(tpl));
    }
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(MultiChildSuite, MultiChildGraphStressTest,
    ::testing::Combine(
        ::testing::Values(2, 4, 8, 16),          // num_child_graphs
        ::testing::Values(16, 32, 64, 128),      // base_nodes_per_child
        ::testing::Values(0, 4, 8, 16)           // node_increment_per_child
    ),
    [](const auto& info) {
        return "NumChildren" + std::to_string(std::get<0>(info.param)) +
               "__BaseNodes" + std::to_string(std::get<1>(info.param)) +
               "__Increment" + std::to_string(std::get<2>(info.param));
    }
);

// --- Test Suite 13: Ultimate Child Graph Comprehensive Stress Test (4*3*4*2 = 96 Tests) ---
using UltimateChildGraphParams = std::tuple<int, int, int, UpdateTarget>; // num_child_in_chain, nodes_per_child, nodes_in_parent, update_target

class UltimateChildGraphStressTest : public ::testing::TestWithParam<UltimateChildGraphParams> {};

TEST_P(UltimateChildGraphStressTest, ComplexInteractionsAndUpdate) {
    auto p = GetParam();
    int num_child_in_chain = std::get<0>(p);
    int nodes_per_child = std::get<1>(p);
    int nodes_in_parent = std::get<2>(p);
    UpdateTarget update_target = std::get<3>(p);
    
    if (nodes_in_parent < 1 || nodes_per_child < 1 || num_child_in_chain < 1) {
        SUCCEED();
        return;
    }

    cudaGraph_t parent_graph;
    CUDA_CHECK(cudaGraphCreate(&parent_graph, 0));

    // 1. Create initial parent nodes
    std::vector<cudaGraphNode_t> parent_nodes(nodes_in_parent);
    cudaKernelNodeParams parent_params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};
    for (int i = 0; i < nodes_in_parent; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&parent_nodes[i], parent_graph, nullptr, 0, &parent_params));
    }

    // 2. Create child graph templates and add them in a chain
    std::vector<cudaGraph_t> child_templates;
    std::vector<cudaGraphNode_t> child_graph_nodes(num_child_in_chain);
    std::vector<cudaGraphNode_t> updatable_child_nodes; // Track a node from each child for potential update

    cudaGraphNode_t last_dependency_node = parent_nodes.back(); // Chain starts after parent nodes

    for (int i = 0; i < num_child_in_chain; ++i) {
        cudaGraph_t child_graph = create_simple_chain_graph(nodes_per_child);
        child_templates.push_back(child_graph);
        
        // Find the first node in the child graph to mark as updatable
        size_t num_child_nodes_in_graph = 0;
        CUDA_CHECK(cudaGraphGetNodes(child_graph, nullptr, &num_child_nodes_in_graph));
        std::vector<cudaGraphNode_t> child_nodes_vec(num_child_nodes_in_graph);
        CUDA_CHECK(cudaGraphGetNodes(child_graph, child_nodes_vec.data(), &num_child_nodes_in_graph));
        updatable_child_nodes.push_back(child_nodes_vec[0]);

        CUDA_CHECK(cudaGraphAddChildGraphNode(&child_graph_nodes[i], parent_graph, &last_dependency_node, 1, child_graph));
        last_dependency_node = child_graph_nodes[i];
    }
    
    // 3. Add a final parent node that depends on the last child graph
    cudaGraphNode_t final_parent_node;
    CUDA_CHECK(cudaGraphAddKernelNode(&final_parent_node, parent_graph, &last_dependency_node, 1, &parent_params));

    // 4. Instantiate the entire complex graph
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, parent_graph, nullptr, nullptr, 0));

    // 5. Pick a node and update it
    cudaKernelNodeParams new_params = {(void*)another_simple_noop_kernel, {1,1,1}, {1,1,1}, 0, nullptr};
    if (update_target == UpdateTarget::PARENT_NODE) {
        CUDA_CHECK(cudaGraphKernelNodeSetParams(parent_nodes[0], &new_params));
    } else { // CHILD_NODE
        // Update the first node in the first child graph template
        CUDA_CHECK(cudaGraphKernelNodeSetParams(updatable_child_nodes[0], &new_params));
    }
    
    // 6. Perform the update and check for success
    cudaGraphExecUpdateResult update_result;
    CUDA_CHECK(cudaGraphExecUpdate(exec, parent_graph, nullptr, &update_result));
    ASSERT_EQ(update_result, cudaGraphExecUpdateSuccess);

    // 7. Launch and cleanup
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(parent_graph));
    for(auto& tpl : child_templates) {
        CUDA_CHECK(cudaGraphDestroy(tpl));
    }
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(UltimateChildSuite, UltimateChildGraphStressTest,
    ::testing::Combine(
        ::testing::Values(1, 2, 3, 4),               // num_child_in_chain
        ::testing::Values(16, 64, 128),              // nodes_per_child
        ::testing::Values(8, 16, 32, 64),            // nodes_in_parent
        ::testing::Values(UpdateTarget::PARENT_NODE, UpdateTarget::CHILD_NODE)
    ),
    [](const auto& info) {
        return "Chain" + std::to_string(std::get<0>(info.param)) +
               "__ChildN" + std::to_string(std::get<1>(info.param)) +
               "__ParentN" + std::to_string(std::get<2>(info.param)) +
               "__Update" + (std::get<3>(info.param) == UpdateTarget::PARENT_NODE ? "Parent" : "Child");
    }
);

>>>>>>> Stashed changes

// --- Test Suite 14: Graph Executable Serialization Stress Test (5*4*4 = 80 Tests) ---
using SerializationParams = std::tuple<GraphTopology, int, int>; // topology, num_nodes, cycles

class GraphSerializationStressTest : public ::testing::TestWithParam<SerializationParams> {};

TEST_P(GraphSerializationStressTest, SerializeAndDeserializeLoop) {
    auto p = GetParam();
    GraphTopology topology = std::get<0>(p);
    int num_nodes = std::get<1>(p);
    int cycles = std::get<2>(p);
    
    if (num_nodes == 0) {
        SUCCEED();
        return;
    }

    // 1. Create a graph with the specified topology
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    // This is a simplified topology creation, borrowing from Test Suite 1
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    cudaKernelNodeParams kernel_params = {(void*)simple_noop_kernel, {1, 1, 1}, {1, 1, 1}, 0, nullptr};
    for (int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, nullptr, 0, &kernel_params));
    }
    if (num_nodes > 1) {
        if (topology == GraphTopology::DEEP_CHAIN) {
            for (int i = 1; i < num_nodes; ++i) CUDA_CHECK(cudaGraphAddDependencies(graph, &nodes[i], &nodes[i - 1], 1));
        } else if (topology == GraphTopology::WIDE_FAN_OUT) {
            for (int i = 1; i < num_nodes; ++i) CUDA_CHECK(cudaGraphAddDependencies(graph, &nodes[i], &nodes[0], 1));
        }
    }

    // 2. Instantiate the graph to get an executable
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    // 3. Serialize the executable to a buffer
    void* buffer = nullptr;
    size_t buffer_size = 0;
    CUDA_CHECK(cudaGraphExecSerialize(exec, nullptr, &buffer_size));
    ASSERT_GT(buffer_size, 0);
    CUDA_CHECK(cudaMalloc(&buffer, buffer_size));
    CUDA_CHECK(cudaGraphExecSerialize(exec, buffer, &buffer_size));

    // Original exec and graph are no longer needed
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));

    // 4. Loop of deserialization, launch, and destruction
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    for (int i = 0; i < cycles; ++i) {
        cudaGraphExec_t deserialized_exec;
        CUDA_CHECK(cudaGraphExecDeserialize(&deserialized_exec, buffer, buffer_size));
        ASSERT_NE(deserialized_exec, nullptr);

        CUDA_CHECK(cudaGraphLaunch(deserialized_exec, stream));
        CUDA_CHECK(cudaStreamSynchronize(stream));
        
        CUDA_CHECK(cudaGraphExecDestroy(deserialized_exec));
    }

    // 5. Cleanup
    CUDA_CHECK(cudaFree(buffer));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(SerializationSuite, GraphSerializationStressTest,
    ::testing::Combine(
        ::testing::Values(GraphTopology::DEEP_CHAIN, GraphTopology::WIDE_FAN_OUT, GraphTopology::BINARY_TREE, GraphTopology::WIDE_FAN_IN, GraphTopology::RANDOM_ACYCLIC),
        ::testing::Values(128, 512, 1024, 2048),
        ::testing::Values(5, 10, 25, 50)
    ),
    [](const auto& info) {
        std::string name;
        switch (std::get<0>(info.param)) {
            case GraphTopology::DEEP_CHAIN:      name = "DeepChain"; break;
            case GraphTopology::WIDE_FAN_OUT:    name = "WideFanOut"; break;
            case GraphTopology::BINARY_TREE:     name = "BinaryTree"; break;
            case GraphTopology::WIDE_FAN_IN:     name = "WideFanIn"; break;
            case GraphTopology::RANDOM_ACYCLIC:  name = "RandomAcyclic"; break;
        }
        return name + "_" + std::to_string(std::get<1>(info.param)) +
               "Nodes__" + std::to_string(std::get<2>(info.param)) + "Cycles";
    }
);
