#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <thread>
#include <iostream>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            std::cerr << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__ << std::endl; \
            FAIL(); \
        } \
    } while(0)

class StressTest : public ::testing::Test {};

__global__ void empty_kernel() {}

__global__ void stress_add_vector(float* c, const float* a, const float* b, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) c[i] = a[i] + b[i];
}


TEST_F(StressTest, DISABLED_LargeGraphCreation) {
    const int num_nodes = 5000;
    cudaGraph_t graph;
    cudaGraphExec_t exec;

    ASSERT_NO_THROW({
        CUDA_CHECK(cudaGraphCreate(&graph, 0));
        std::vector<cudaGraphNode_t> nodes(num_nodes);
        
        cudaKernelNodeParams params = {};
        params.func = (void*)empty_kernel;
        params.gridDim = {1,1,1};
        params.blockDim = {1,1,1};

        for (int i = 0; i < num_nodes; ++i) {
            CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, (i > 0 ? &nodes[i-1] : nullptr), (i > 0 ? 1 : 0), &params));
        }
        
        CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    });

    ASSERT_NE(graph, nullptr);
    ASSERT_NE(exec, nullptr);

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

// --- Suite of 10 New Large Graph Stress Tests ---
// Helper function to create a deep dependency chain graph
static void create_deep_chain_graph(cudaGraph_t& graph, int num_nodes) {
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    cudaKernelNodeParams params = {};
    params.func = (void*)empty_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    for (int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, (i > 0 ? &nodes[i-1] : nullptr), (i > 0 ? 1 : 0), &params));
    }
}

// 1. Deep Chain: 1024 nodes in a single dependency chain
TEST_F(StressTest, DISABLED_LargeGraph_DeepChain_1024) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    create_deep_chain_graph(graph, 1024);
    
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

// 2. Wide Fan-Out: 1 root node with 1024 parallel children
TEST_F(StressTest, DISABLED_LargeGraph_WideFanOut_1024) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaKernelNodeParams params = {};
    params.func = (void*)empty_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    cudaGraphNode_t root;
    CUDA_CHECK(cudaGraphAddKernelNode(&root, graph, nullptr, 0, &params));
    
    const int num_children = 1024;
    std::vector<cudaGraphNode_t> children(num_children);
    for (int i = 0; i < num_children; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&children[i], graph, &root, 1, &params));
    }

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

// 3. Wide Fan-In: 1024 parallel nodes feeding into a single node
TEST_F(StressTest, DISABLED_LargeGraph_WideFanIn_1024) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaKernelNodeParams params = {};
    params.func = (void*)empty_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    const int num_parents = 1024;
    std::vector<cudaGraphNode_t> parents(num_parents);
    for (int i = 0; i < num_parents; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&parents[i], graph, nullptr, 0, &params));
    }
    
    cudaGraphNode_t child;
    CUDA_CHECK(cudaGraphAddKernelNode(&child, graph, parents.data(), num_parents, &params));

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

// 4. Pyramid Reduction: 512 -> 256 -> ... -> 1
TEST_F(StressTest, DISABLED_LargeGraph_Pyramid_10Layers) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaKernelNodeParams params = {};
    params.func = (void*)empty_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    std::vector<cudaGraphNode_t> prev_layer(512);
    for(size_t i = 0; i < prev_layer.size(); ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&prev_layer[i], graph, nullptr, 0, &params));
    }

    while(prev_layer.size() > 1) {
        std::vector<cudaGraphNode_t> curr_layer;
        for(size_t i = 0; i < prev_layer.size(); i += 2) {
            cudaGraphNode_t parents[] = {prev_layer[i], prev_layer[i+1]};
            cudaGraphNode_t child;
            CUDA_CHECK(cudaGraphAddKernelNode(&child, graph, parents, 2, &params));
            curr_layer.push_back(child);
        }
        prev_layer = curr_layer;
    }
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

// 5. Inverse Pyramid (Broadcast): 1 -> 2 -> ... -> 512
TEST_F(StressTest, DISABLED_LargeGraph_InversePyramid_10Layers) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaKernelNodeParams params = {};
    params.func = (void*)empty_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    std::vector<cudaGraphNode_t> prev_layer(1);
    CUDA_CHECK(cudaGraphAddKernelNode(&prev_layer[0], graph, nullptr, 0, &params));
    
    for(int i = 0; i < 9; ++i) { // 9 layers to get to 512
        std::vector<cudaGraphNode_t> curr_layer;
        for(auto const& parent : prev_layer) {
            cudaGraphNode_t child1, child2;
            CUDA_CHECK(cudaGraphAddKernelNode(&child1, graph, &parent, 1, &params));
            CUDA_CHECK(cudaGraphAddKernelNode(&child2, graph, &parent, 1, &params));
            curr_layer.push_back(child1);
            curr_layer.push_back(child2);
        }
        prev_layer = curr_layer;
    }
    ASSERT_EQ(prev_layer.size(), 512);

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

// 6. Heavy Nodes: 64 nodes operating on 1GB of data each
TEST_F(StressTest, DISABLED_LargeGraph_HeavyNodes_1GB) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    const int num_nodes = 64;
    const size_t n_heavy = 1ULL * 1024 * 1024 * 256; // 1GB of floats
    const size_t size_heavy = n_heavy * sizeof(float);
    std::vector<float*> d_data(num_nodes);
    std::vector<void*> kernel_args_list;
    
    for (int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaMalloc(&d_data[i], size_heavy));
        kernel_args_list.push_back(new void*[1]{&d_data[i]});
        cudaKernelNodeParams params = {(void*)empty_kernel, {1,1,1}, {1,1,1}, 0, (void**)kernel_args_list.back()};
        cudaGraphNode_t node;
        CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));
    }

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    for (int i = 0; i < num_nodes; ++i) {
        delete[] (void**)kernel_args_list[i];
        CUDA_CHECK(cudaFree(d_data[i]));
    }
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

// 7. Concurrent Deep Chains: 4 concurrent streams, each with a 512-node chain
TEST_F(StressTest, DISABLED_LargeGraph_ConcurrentDeepChains) {
    const int num_streams = 4;
    std::vector<cudaStream_t> streams(num_streams);
    std::vector<cudaGraph_t> graphs(num_streams);
    std::vector<cudaGraphExec_t> execs(num_streams);
    
    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamCreate(&streams[i]));
        CUDA_CHECK(cudaGraphCreate(&graphs[i], 0));
        create_deep_chain_graph(graphs[i], 512);
        CUDA_CHECK(cudaGraphInstantiate(&execs[i], graphs[i], nullptr, nullptr, 0));
    }
    
    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaGraphLaunch(execs[i], streams[i]));
    }

    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaStreamSynchronize(streams[i]));
    }
    
    for (int i = 0; i < num_streams; ++i) {
        CUDA_CHECK(cudaGraphExecDestroy(execs[i]));
        CUDA_CHECK(cudaGraphDestroy(graphs[i]));
        CUDA_CHECK(cudaStreamDestroy(streams[i]));
    }
    SUCCEED();
}

// 8. Deeply Nested Graph: 32 levels of nested sub-graphs
TEST_F(StressTest, DISABLED_LargeGraph_DeeplyNested_32Levels) {
    cudaGraph_t root_graph;
    CUDA_CHECK(cudaGraphCreate(&root_graph, 0));
    
    cudaGraph_t prev_graph = root_graph;
    
    for (int i = 0; i < 32; ++i) {
        cudaGraph_t current_graph;
        cudaGraphNode_t child_graph_node;
        cudaKernelNodeParams params = {};
        params.func = (void*)empty_kernel;
        params.gridDim = {1,1,1};
        params.blockDim = {1,1,1};

        CUDA_CHECK(cudaGraphCreate(&current_graph, 0));
        CUDA_CHECK(cudaGraphAddKernelNode(nullptr, current_graph, nullptr, 0, &params));
        CUDA_CHECK(cudaGraphAddChildGraphNode(&child_graph_node, prev_graph, nullptr, 0, current_graph));
        
        if (prev_graph != root_graph) {
             // We don't destroy the root graph, it is destroyed at the end.
             // Child graphs are cloned, so we can destroy the original.
             CUDA_CHECK(cudaGraphDestroy(prev_graph));
        }
        prev_graph = current_graph;
    }
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, prev_graph, nullptr, nullptr, 0));
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(prev_graph)); // Destroy the last one
    SUCCEED();
}

// 9. Random Dependencies: 512 nodes with random inter-dependencies
TEST_F(StressTest, DISABLED_LargeGraph_RandomDependencies_512) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    const int num_nodes = 512;
    std::vector<cudaGraphNode_t> nodes(num_nodes);
    cudaKernelNodeParams params = {};
    params.func = (void*)empty_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    for(int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph, nullptr, 0, &params));
    }
    
    // Create random dependencies (ensure no cycles by only connecting node i to node j where j < i)
    for(int i = 1; i < num_nodes; ++i) {
        int num_deps = rand() % std::min(i, 5); // Connect to up to 5 previous nodes
        for(int k=0; k < num_deps; ++k) {
            int dep_idx = rand() % i;
            CUDA_CHECK(cudaGraphAddDependencies(graph, &nodes[i], &nodes[dep_idx], 1));
        }
    }
    
    cudaGraphExec_t exec;
    // Instantiation might fail if a random dependency creation was invalid, which is part of the test
    cudaError_t err = cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);
    if (err == cudaSuccess) {
        CUDA_CHECK(cudaGraphExecDestroy(exec));
    }
    
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}

// 10. Brute Force Node Count: 16K independent parallel nodes
TEST_F(StressTest, DISABLED_LargeGraph_BruteForceNodeCount_16K) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    const int num_nodes = 16384;
    cudaKernelNodeParams params = {};
    params.func = (void*)empty_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};

    for(int i = 0; i < num_nodes; ++i) {
        CUDA_CHECK(cudaGraphAddKernelNode(nullptr, graph, nullptr, 0, &params));
    }

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    SUCCEED();
}
>>>>>>> REPLACE

void launch_loop(cudaGraphExec_t exec, cudaStream_t stream, int num_launches) {
    for (int i = 0; i < num_launches; ++i) {
        cudaGraphLaunch(exec, stream);
    }
}

TEST_F(StressTest, DISABLED_HighConcurrencyLaunch) {
    const int num_threads = 8;
    const int launches_per_thread = 1000;

    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {};
    params.func = (void*)empty_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    std::vector<std::thread> threads;
    ASSERT_NO_THROW({
        for (int i = 0; i < num_threads; ++i) {
            threads.emplace_back(launch_loop, exec, stream, launches_per_thread);
        }

        for (auto& t : threads) {
            if (t.joinable()) {
                t.join();
            }
        }
    });
    
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

void independent_graph_task(int thread_id, int n) {
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));

    float *d_a, *d_b, *d_c;
    size_t size = n * sizeof(float);
    CUDA_CHECK(cudaMalloc(&d_a, size));
    CUDA_CHECK(cudaMalloc(&d_b, size));
    CUDA_CHECK(cudaMalloc(&d_c, size));

    std::vector<float> h_a(n, (float)thread_id);
    std::vector<float> h_b(n, (float)thread_id * 2.0f);
    CUDA_CHECK(cudaMemcpyAsync(d_a, h_a.data(), size, cudaMemcpyHostToDevice, stream));
    CUDA_CHECK(cudaMemcpyAsync(d_b, h_b.data(), size, cudaMemcpyHostToDevice, stream));

    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    void* args[] = {&d_c, &d_a, &d_b, &n};
    cudaKernelNodeParams params = {};
    params.func = (void*)stress_add_vector;
    params.gridDim = {(unsigned int)(n + 255) / 256, 1, 1};
    params.blockDim = {256, 1, 1};
    params.kernelParams = args;

    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));

    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));

    const int num_launches = 50;
    for(int i = 0; i < num_launches; ++i) {
        CUDA_CHECK(cudaGraphLaunch(exec, stream));
    }
    
    std::vector<float> h_c(n);
    CUDA_CHECK(cudaMemcpyAsync(h_c.data(), d_c, size, cudaMemcpyDeviceToHost, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    // Verification
    for(int i=0; i<n; ++i) {
        ASSERT_FLOAT_EQ(h_c[i], (float)thread_id + (float)thread_id * 2.0f);
    }
    
    CUDA_CHECK(cudaFree(d_a));
    CUDA_CHECK(cudaFree(d_b));
    CUDA_CHECK(cudaFree(d_c));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
}

TEST_F(StressTest, DISABLED_MultiThreadMultiStream) {
    const int num_threads = 4;
    const int n_per_thread = 1024;
    
    std::vector<std::thread> threads;
    ASSERT_NO_THROW({
        for (int i = 0; i < num_threads; ++i) {
            threads.emplace_back(independent_graph_task, i + 1, n_per_thread);
        }
        for (auto& t : threads) {
            if (t.joinable()) {
                t.join();
            }
        }
    });
    SUCCEED();
}
