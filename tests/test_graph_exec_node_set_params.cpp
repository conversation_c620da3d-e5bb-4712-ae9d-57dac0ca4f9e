#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <thread>
#include <future>
#include <atomic>
#include <memory>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

__global__ void kernel_multiply(float* data, int n, float factor) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = data[idx] * factor;
    }
}

__global__ void kernel_add(float* data, int n, float value) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = data[idx] + value;
    }
}

__global__ void kernel_compute(float* input, float* output, int n, float scale) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        output[idx] = input[idx] * scale + sinf(input[idx]);
    }
}

void CUDART_CB host_callback_increment(void* data) {
    int* counter = static_cast<int*>(data);
    (*counter)++;
}

void CUDART_CB host_callback_multiply(void* data) {
    struct CallbackData {
        float* value;
        float multiplier;
    };
    CallbackData* cb_data = static_cast<CallbackData*>(data);
    *(cb_data->value) *= cb_data->multiplier;
}

class GraphExecNodeSetParamsTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaFree(nullptr));
        
        CUDA_CHECK(cudaMalloc(&d_input, data_size * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&d_output, data_size * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&d_temp1, data_size * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&d_temp2, data_size * sizeof(float)));
        
        h_input.resize(data_size);
        h_output.resize(data_size);
        for (int i = 0; i < data_size; i++) {
            h_input[i] = static_cast<float>(i % 100);
        }
        
        CUDA_CHECK(cudaMemcpy(d_input, h_input.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
        
        CUDA_CHECK(cudaStreamCreate(&stream));
        CUDA_CHECK(cudaEventCreate(&event));
        
        host_counter = 0;
        host_value = 1.0f;
        callback_data.value = &host_value;
        callback_data.multiplier = 2.0f;
    }
    
    void TearDown() override {
        if (d_input) cudaFree(d_input);
        if (d_output) cudaFree(d_output);
        if (d_temp1) cudaFree(d_temp1);
        if (d_temp2) cudaFree(d_temp2);
        if (stream) cudaStreamDestroy(stream);
        if (event) cudaEventDestroy(event);
    }
    
    void CreateGraphWithKernelNode(cudaGraph_t* graph, cudaGraphExec_t* exec, cudaGraphNode_t* kernel_node) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        
        float factor = 2.0f;
        void* kernel_args[] = {&d_input, &data_size, &factor};
        
        cudaKernelNodeParams kernel_params = {};
        kernel_params.func = (void*)kernel_multiply;
        kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
        kernel_params.blockDim = {256, 1, 1};
        kernel_params.sharedMemBytes = 0;
        kernel_params.kernelParams = kernel_args;
        kernel_params.extra = nullptr;
        
        CUDA_CHECK(cudaGraphAddKernelNode(kernel_node, *graph, nullptr, 0, &kernel_params));
        CUDA_CHECK(cudaGraphInstantiate(exec, *graph, nullptr, nullptr, 0));
    }
    
    void CreateGraphWithMemcpyNode(cudaGraph_t* graph, cudaGraphExec_t* exec, cudaGraphNode_t* memcpy_node) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        
        CUDA_CHECK(cudaGraphAddMemcpyNode1D(memcpy_node, *graph, nullptr, 0, 
                                          d_output, d_input, data_size * sizeof(float), 
                                          cudaMemcpyDeviceToDevice));
        CUDA_CHECK(cudaGraphInstantiate(exec, *graph, nullptr, nullptr, 0));
    }
    
    void CreateGraphWithMemsetNode(cudaGraph_t* graph, cudaGraphExec_t* exec, cudaGraphNode_t* memset_node) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        
        cudaMemsetParams memset_params = {};
        memset_params.dst = d_temp1;
        memset_params.value = 42;
        memset_params.pitch = 0;
        memset_params.elementSize = sizeof(float);
        memset_params.width = data_size;
        memset_params.height = 1;
        
        CUDA_CHECK(cudaGraphAddMemsetNode(memset_node, *graph, nullptr, 0, &memset_params));
        CUDA_CHECK(cudaGraphInstantiate(exec, *graph, nullptr, nullptr, 0));
    }
    
    void CreateGraphWithHostNode(cudaGraph_t* graph, cudaGraphExec_t* exec, cudaGraphNode_t* host_node) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        
        cudaHostNodeParams host_params = {};
        host_params.fn = host_callback_increment;
        host_params.userData = &host_counter;
        
        CUDA_CHECK(cudaGraphAddHostNode(host_node, *graph, nullptr, 0, &host_params));
        CUDA_CHECK(cudaGraphInstantiate(exec, *graph, nullptr, nullptr, 0));
    }
    
    void CreateComplexGraph(cudaGraph_t* graph, cudaGraphExec_t* exec, std::vector<cudaGraphNode_t>* nodes) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        nodes->resize(5);
        
        CUDA_CHECK(cudaGraphAddMemcpyNode1D(&(*nodes)[0], *graph, nullptr, 0, 
                                          d_temp1, d_input, data_size * sizeof(float), 
                                          cudaMemcpyDeviceToDevice));
        
        float factor = 3.0f;
        void* kernel_args1[] = {&d_temp1, &data_size, &factor};
        cudaKernelNodeParams kernel_params1 = {};
        kernel_params1.func = (void*)kernel_multiply;
        kernel_params1.gridDim = {(data_size + 255) / 256, 1, 1};
        kernel_params1.blockDim = {256, 1, 1};
        kernel_params1.kernelParams = kernel_args1;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[1], *graph, &(*nodes)[0], 1, &kernel_params1));
        
        float value = 10.0f;
        void* kernel_args2[] = {&d_temp1, &data_size, &value};
        cudaKernelNodeParams kernel_params2 = {};
        kernel_params2.func = (void*)kernel_add;
        kernel_params2.gridDim = {(data_size + 255) / 256, 1, 1};
        kernel_params2.blockDim = {256, 1, 1};
        kernel_params2.kernelParams = kernel_args2;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&(*nodes)[2], *graph, &(*nodes)[1], 1, &kernel_params2));
        
        cudaHostNodeParams host_params = {};
        host_params.fn = host_callback_increment;
        host_params.userData = &host_counter;
        
        CUDA_CHECK(cudaGraphAddHostNode(&(*nodes)[3], *graph, &(*nodes)[2], 1, &host_params));
        
        cudaMemsetParams memset_params = {};
        memset_params.dst = d_temp2;
        memset_params.value = 0;
        memset_params.elementSize = sizeof(float);
        memset_params.width = data_size;
        memset_params.height = 1;
        
        CUDA_CHECK(cudaGraphAddMemsetNode(&(*nodes)[4], *graph, &(*nodes)[3], 1, &memset_params));
        
        CUDA_CHECK(cudaGraphInstantiate(exec, *graph, nullptr, nullptr, 0));
    }
    
    const int data_size = 1024;
    float* d_input = nullptr;
    float* d_output = nullptr;
    float* d_temp1 = nullptr;
    float* d_temp2 = nullptr;
    std::vector<float> h_input;
    std::vector<float> h_output;
    cudaStream_t stream = nullptr;
    cudaEvent_t event = nullptr;
    
    int host_counter = 0;
    float host_value = 1.0f;
    struct CallbackData {
        float* value;
        float multiplier;
    } callback_data;
};

// Test 1: Basic kernel node parameter update
TEST_F(GraphExecNodeSetParamsTest, BasicKernelNodeUpdate) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    // Execute with original parameters (factor = 2.0)
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    CUDA_CHECK(cudaMemcpy(h_output.data(), d_input, data_size * sizeof(float), cudaMemcpyDeviceToHost));
    
    // Reset input data
    CUDA_CHECK(cudaMemcpy(d_input, h_input.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
    
    // Update kernel parameters (factor = 5.0)
    float new_factor = 5.0f;
    void* new_kernel_args[] = {&d_input, &data_size, &new_factor};
    
    cudaKernelNodeParams new_kernel_params = {};
    new_kernel_params.func = (void*)kernel_multiply;
    new_kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
    new_kernel_params.blockDim = {256, 1, 1};
    new_kernel_params.kernelParams = new_kernel_args;
    
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, kernel_node, &new_kernel_params));
    
    // Execute with updated parameters
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    std::vector<float> h_result(data_size);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_input, data_size * sizeof(float), cudaMemcpyDeviceToHost));
    
    // Verify results
    for (int i = 0; i < data_size; i++) {
        EXPECT_FLOAT_EQ(h_result[i], h_input[i] * new_factor);
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 2: Memcpy node parameter update
TEST_F(GraphExecNodeSetParamsTest, MemcpyNodeUpdate) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t memcpy_node;
    
    CreateGraphWithMemcpyNode(&graph, &exec, &memcpy_node);
    
    // Execute with original parameters (input -> output)
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    CUDA_CHECK(cudaMemcpy(h_output.data(), d_output, data_size * sizeof(float), cudaMemcpyDeviceToHost));
    
    // Verify original copy
    for (int i = 0; i < data_size; i++) {
        EXPECT_FLOAT_EQ(h_output[i], h_input[i]);
    }
    
    // Clear output buffer
    CUDA_CHECK(cudaMemset(d_output, 0, data_size * sizeof(float)));
    
    // Update memcpy parameters (temp1 -> output, with half size)
    size_t new_size = (data_size / 2) * sizeof(float);
    CUDA_CHECK(cudaGraphExecMemcpyNodeSetParams1D(exec, memcpy_node, d_output, d_temp1, new_size, cudaMemcpyDeviceToDevice));
    
    // Set some data in temp1
    std::vector<float> temp_data(data_size, 99.0f);
    CUDA_CHECK(cudaMemcpy(d_temp1, temp_data.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
    
    // Execute with updated parameters
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    std::vector<float> h_result(data_size);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_output, data_size * sizeof(float), cudaMemcpyDeviceToHost));
    
    // Verify updated copy (first half should be 99.0, second half should be 0)
    for (int i = 0; i < data_size / 2; i++) {
        EXPECT_FLOAT_EQ(h_result[i], 99.0f);
    }
    for (int i = data_size / 2; i < data_size; i++) {
        EXPECT_FLOAT_EQ(h_result[i], 0.0f);
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 3: Memset node parameter update
TEST_F(GraphExecNodeSetParamsTest, MemsetNodeUpdate) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t memset_node;
    
    CreateGraphWithMemsetNode(&graph, &exec, &memset_node);
    
    // Execute with original parameters (value = 42)
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    std::vector<int> h_result(data_size);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_temp1, data_size * sizeof(int), cudaMemcpyDeviceToHost));
    
    // Verify original memset
    for (int i = 0; i < data_size; i++) {
        EXPECT_EQ(h_result[i], 42);
    }
    
    // Update memset parameters (value = 123, different destination)
    cudaMemsetParams new_memset_params = {};
    new_memset_params.dst = d_temp2;
    new_memset_params.value = 123;
    new_memset_params.elementSize = sizeof(int);
    new_memset_params.width = data_size / 2;
    new_memset_params.height = 1;
    
    CUDA_CHECK(cudaGraphExecMemsetNodeSetParams(exec, memset_node, &new_memset_params));
    
    // Clear temp2 first
    CUDA_CHECK(cudaMemset(d_temp2, 0, data_size * sizeof(int)));
    
    // Execute with updated parameters
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    std::vector<int> h_result2(data_size);
    CUDA_CHECK(cudaMemcpy(h_result2.data(), d_temp2, data_size * sizeof(int), cudaMemcpyDeviceToHost));
    
    // Verify updated memset (first half should be 123, second half should be 0)
    for (int i = 0; i < data_size / 2; i++) {
        EXPECT_EQ(h_result2[i], 123);
    }
    for (int i = data_size / 2; i < data_size; i++) {
        EXPECT_EQ(h_result2[i], 0);
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 4: Host node parameter update
TEST_F(GraphExecNodeSetParamsTest, HostNodeUpdate) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t host_node;
    
    CreateGraphWithHostNode(&graph, &exec, &host_node);
    
    // Execute with original parameters (increment counter)
    host_counter = 10;
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    EXPECT_EQ(host_counter, 11);
    
    // Update host node parameters (multiply value)
    cudaHostNodeParams new_host_params = {};
    new_host_params.fn = host_callback_multiply;
    new_host_params.userData = &callback_data;
    
    CUDA_CHECK(cudaGraphExecHostNodeSetParams(exec, host_node, &new_host_params));
    
    // Execute with updated parameters
    host_value = 3.0f;
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    EXPECT_FLOAT_EQ(host_value, 6.0f);  // 3.0 * 2.0
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 5: Invalid node handle
TEST_F(GraphExecNodeSetParamsTest, InvalidNodeHandle) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    float factor = 2.0f;
    void* kernel_args[] = {&d_input, &data_size, &factor};
    
    cudaKernelNodeParams kernel_params = {};
    kernel_params.func = (void*)kernel_multiply;
    kernel_params.gridDim = {1, 1, 1};
    kernel_params.blockDim = {1, 1, 1};
    kernel_params.kernelParams = kernel_args;
    
    // Test with null node
    EXPECT_EQ(cudaGraphExecKernelNodeSetParams(exec, nullptr, &kernel_params), cudaErrorInvalidValue);
    
    // Test with fake node
    cudaGraphNode_t fake_node = reinterpret_cast<cudaGraphNode_t>(0xDEADBEEF);
    EXPECT_NE(cudaGraphExecKernelNodeSetParams(exec, fake_node, &kernel_params), cudaSuccess);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 6: Invalid graph exec handle
TEST_F(GraphExecNodeSetParamsTest, InvalidGraphExecHandle) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    float factor = 2.0f;
    void* kernel_args[] = {&d_input, &data_size, &factor};
    
    cudaKernelNodeParams kernel_params = {};
    kernel_params.func = (void*)kernel_multiply;
    kernel_params.gridDim = {1, 1, 1};
    kernel_params.blockDim = {1, 1, 1};
    kernel_params.kernelParams = kernel_args;
    
    // Test with null exec
    EXPECT_EQ(cudaGraphExecKernelNodeSetParams(nullptr, kernel_node, &kernel_params), cudaErrorInvalidValue);
    
    // Test with fake exec
    cudaGraphExec_t fake_exec = reinterpret_cast<cudaGraphExec_t>(0xDEADBEEF);
    EXPECT_NE(cudaGraphExecKernelNodeSetParams(fake_exec, kernel_node, &kernel_params), cudaSuccess);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 7: Null parameters
TEST_F(GraphExecNodeSetParamsTest, NullParameters) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    EXPECT_EQ(cudaGraphExecKernelNodeSetParams(exec, kernel_node, nullptr), cudaErrorInvalidValue);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 8: Multiple parameter updates
TEST_F(GraphExecNodeSetParamsTest, MultipleParameterUpdates) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    std::vector<float> factors = {2.0f, 3.0f, 0.5f, 10.0f};
    
    for (float factor : factors) {
        // Reset input data
        CUDA_CHECK(cudaMemcpy(d_input, h_input.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
        
        // Update kernel parameters
        void* kernel_args[] = {&d_input, &data_size, &factor};
        
        cudaKernelNodeParams kernel_params = {};
        kernel_params.func = (void*)kernel_multiply;
        kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
        kernel_params.blockDim = {256, 1, 1};
        kernel_params.kernelParams = kernel_args;
        
        CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, kernel_node, &kernel_params));
        
        // Execute graph
        CUDA_CHECK(cudaGraphLaunch(exec, stream));
        CUDA_CHECK(cudaStreamSynchronize(stream));
        
        // Verify results
        std::vector<float> h_result(data_size);
        CUDA_CHECK(cudaMemcpy(h_result.data(), d_input, data_size * sizeof(float), cudaMemcpyDeviceToHost));
        
        for (int i = 0; i < data_size; i++) {
            EXPECT_FLOAT_EQ(h_result[i], h_input[i] * factor);
        }
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 9: Complex graph parameter updates
TEST_F(GraphExecNodeSetParamsTest, ComplexGraphParameterUpdates) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    std::vector<cudaGraphNode_t> nodes;
    
    CreateComplexGraph(&graph, &exec, &nodes);
    
    // Update kernel node parameters
    float new_factor = 7.0f;
    void* new_kernel_args1[] = {&d_temp1, &data_size, &new_factor};
    
    cudaKernelNodeParams new_kernel_params1 = {};
    new_kernel_params1.func = (void*)kernel_multiply;
    new_kernel_params1.gridDim = {(data_size + 255) / 256, 1, 1};
    new_kernel_params1.blockDim = {256, 1, 1};
    new_kernel_params1.kernelParams = new_kernel_args1;
    
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, nodes[1], &new_kernel_params1));
    
    // Update memcpy node parameters
    CUDA_CHECK(cudaGraphExecMemcpyNodeSetParams1D(exec, nodes[0], d_temp1, d_input, 
                                                (data_size / 2) * sizeof(float), 
                                                cudaMemcpyDeviceToDevice));
    
    // Update memset node parameters
    cudaMemsetParams new_memset_params = {};
    new_memset_params.dst = d_temp2;
    new_memset_params.value = 77;
    new_memset_params.elementSize = sizeof(float);
    new_memset_params.width = data_size / 3;
    new_memset_params.height = 1;
    
    CUDA_CHECK(cudaGraphExecMemsetNodeSetParams(exec, nodes[4], &new_memset_params));
    
    // Execute updated graph
    host_counter = 0;
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    // Verify host callback executed
    EXPECT_EQ(host_counter, 1);
    
    // Verify memset results
    std::vector<int> h_memset_result(data_size);
    CUDA_CHECK(cudaMemcpy(h_memset_result.data(), d_temp2, data_size * sizeof(int), cudaMemcpyDeviceToHost));
    
    for (int i = 0; i < data_size / 3; i++) {
        EXPECT_EQ(h_memset_result[i], 77);
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 10: Thread safety test
TEST_F(GraphExecNodeSetParamsTest, ThreadSafety) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    std::atomic<bool> test_passed{true};
    std::vector<std::future<void>> futures;
    const int num_threads = 4;
    const int iterations_per_thread = 20;
    
    for (int i = 0; i < num_threads; i++) {
        futures.push_back(std::async(std::launch::async, [&, i]() {
            for (int j = 0; j < iterations_per_thread; j++) {
                float factor = static_cast<float>(i + 1);
                void* kernel_args[] = {&d_input, &data_size, &factor};
                
                cudaKernelNodeParams kernel_params = {};
                kernel_params.func = (void*)kernel_multiply;
                kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
                kernel_params.blockDim = {256, 1, 1};
                kernel_params.kernelParams = kernel_args;
                
                cudaError_t result = cudaGraphExecKernelNodeSetParams(exec, kernel_node, &kernel_params);
                if (result != cudaSuccess) {
                    test_passed.store(false);
                    break;
                }
            }
        }));
    }
    
    for (auto& future : futures) {
        future.wait();
    }
    
    EXPECT_TRUE(test_passed.load());
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 11: Parameter update after execution
TEST_F(GraphExecNodeSetParamsTest, ParameterUpdateAfterExecution) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    // Execute multiple times, updating parameters each time
    for (int iteration = 0; iteration < 5; iteration++) {
        // Reset input data
        CUDA_CHECK(cudaMemcpy(d_input, h_input.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
        
        // Execute current graph
        CUDA_CHECK(cudaGraphLaunch(exec, stream));
        CUDA_CHECK(cudaStreamSynchronize(stream));
        
        // Update parameters for next iteration
        float new_factor = static_cast<float>(iteration + 3);
        void* new_kernel_args[] = {&d_input, &data_size, &new_factor};
        
        cudaKernelNodeParams new_kernel_params = {};
        new_kernel_params.func = (void*)kernel_multiply;
        new_kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
        new_kernel_params.blockDim = {256, 1, 1};
        new_kernel_params.kernelParams = new_kernel_args;
        
        CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, kernel_node, &new_kernel_params));
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 12: Large parameter arrays
TEST_F(GraphExecNodeSetParamsTest, LargeParameterArrays) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    const int large_param_count = 20;
    std::vector<void*> large_params(large_param_count);
    std::vector<float> param_values(large_param_count);
    
    // Create many parameters (though kernel won't use them all)
    for (int i = 0; i < large_param_count; i++) {
        param_values[i] = static_cast<float>(i);
        large_params[i] = &param_values[i];
    }
    large_params[0] = &d_input;
    large_params[1] = &data_size;
    
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaKernelNodeParams kernel_params = {};
    kernel_params.func = (void*)kernel_multiply;
    kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
    kernel_params.blockDim = {256, 1, 1};
    kernel_params.kernelParams = large_params.data();
    
    CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph, nullptr, 0, &kernel_params));
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    // Update with different large parameter array
    std::vector<float> new_param_values(large_param_count);
    std::vector<void*> new_large_params(large_param_count);
    
    for (int i = 0; i < large_param_count; i++) {
        new_param_values[i] = static_cast<float>(i * 2);
        new_large_params[i] = &new_param_values[i];
    }
    new_large_params[0] = &d_input;
    new_large_params[1] = &data_size;
    
    cudaKernelNodeParams new_kernel_params = {};
    new_kernel_params.func = (void*)kernel_multiply;
    new_kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
    new_kernel_params.blockDim = {256, 1, 1};
    new_kernel_params.kernelParams = new_large_params.data();
    
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, kernel_node, &new_kernel_params));
    
    // Execute and verify it still works
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 13: Stress test with rapid parameter updates
TEST_F(GraphExecNodeSetParamsTest, StressTestRapidUpdates) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    const int num_updates = 1000;
    
    for (int i = 0; i < num_updates; i++) {
        float factor = static_cast<float>(i % 10 + 1);
        void* kernel_args[] = {&d_input, &data_size, &factor};
        
        cudaKernelNodeParams kernel_params = {};
        kernel_params.func = (void*)kernel_multiply;
        kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
        kernel_params.blockDim = {256, 1, 1};
        kernel_params.kernelParams = kernel_args;
        
        CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, kernel_node, &kernel_params));
    }
    
    // Final execution to ensure everything still works
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 14: Memory consistency check
TEST_F(GraphExecNodeSetParamsTest, MemoryConsistencyCheck) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    // Update parameters multiple times with same values
    float factor = 3.14f;
    void* kernel_args[] = {&d_input, &data_size, &factor};
    
    cudaKernelNodeParams kernel_params = {};
    kernel_params.func = (void*)kernel_multiply;
    kernel_params.gridDim = {(data_size + 255) / 256, 1, 1};
    kernel_params.blockDim = {256, 1, 1};
    kernel_params.kernelParams = kernel_args;
    
    // Multiple identical updates should work consistently
    for (int i = 0; i < 10; i++) {
        CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, kernel_node, &kernel_params));
    }
    
    // Execute multiple times and verify consistent results
    for (int i = 0; i < 3; i++) {
        CUDA_CHECK(cudaMemcpy(d_input, h_input.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
        
        CUDA_CHECK(cudaGraphLaunch(exec, stream));
        CUDA_CHECK(cudaStreamSynchronize(stream));
        
        std::vector<float> h_result(data_size);
        CUDA_CHECK(cudaMemcpy(h_result.data(), d_input, data_size * sizeof(float), cudaMemcpyDeviceToHost));
        
        for (int j = 0; j < data_size; j++) {
            EXPECT_FLOAT_EQ(h_result[j], h_input[j] * factor);
        }
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 15: Integration with different kernel functions
TEST_F(GraphExecNodeSetParamsTest, DifferentKernelFunctions) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    cudaGraphNode_t kernel_node;
    
    CreateGraphWithKernelNode(&graph, &exec, &kernel_node);
    
    // Change to different kernel function
    float scale = 2.0f;
    void* compute_args[] = {&d_input, &d_output, &data_size, &scale};
    
    cudaKernelNodeParams compute_params = {};
    compute_params.func = (void*)kernel_compute;
    compute_params.gridDim = {(data_size + 255) / 256, 1, 1};
    compute_params.blockDim = {256, 1, 1};
    compute_params.kernelParams = compute_args;
    
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, kernel_node, &compute_params));
    
    // Execute with new kernel
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    // Verify results from new kernel
    std::vector<float> h_result(data_size);
    CUDA_CHECK(cudaMemcpy(h_result.data(), d_output, data_size * sizeof(float), cudaMemcpyDeviceToHost));
    
    for (int i = 0; i < data_size; i++) {
        float expected = h_input[i] * scale + sinf(h_input[i]);
        EXPECT_NEAR(h_result[i], expected, 1e-5f);
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}