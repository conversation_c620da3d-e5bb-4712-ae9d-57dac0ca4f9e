#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <numeric>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

// --- KERNELS for this test file ---
__global__ void vector_add(float *c, const float *a, const float *b, int n) {
    int i = blockIdx.x * blockDim.x + threadIdx.x;
    if (i < n) {
        c[i] = a[i] + b[i];
    }
}

__global__ void multiply_kernel(float* data, float factor) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        *data *= factor;
    }
}

// Test fixture for standard integration tests that require pre-allocated vectors
class IntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaStreamCreate(&stream));
        n = 1024;
        size = n * sizeof(float);

        CUDA_CHECK(cudaMalloc(&d_a, size));
        CUDA_CHECK(cudaMalloc(&d_b, size));
        CUDA_CHECK(cudaMalloc(&d_c, size));

        h_a.resize(n);
        h_b.resize(n);
        h_c.resize(n, 0.0f);

        for (int i = 0; i < n; ++i) {
            h_a[i] = static_cast<float>(i);
            h_b[i] = static_cast<float>(i * 2);
        }

        CUDA_CHECK(cudaMemcpy(d_a, h_a.data(), size, cudaMemcpyHostToDevice));
        CUDA_CHECK(cudaMemcpy(d_b, h_b.data(), size, cudaMemcpyHostToDevice));
    }

    void TearDown() override {
        CUDA_CHECK(cudaFree(d_a));
        CUDA_CHECK(cudaFree(d_b));
        CUDA_CHECK(cudaFree(d_c));
        CUDA_CHECK(cudaStreamDestroy(stream));
    }

    cudaStream_t stream;
    float *d_a, *d_b, *d_c;
    std::vector<float> h_a, h_b, h_c;
    int n;
    size_t size;
};

// --- Standard Success Cases ---

TEST_F(IntegrationTest, SingleKernelGraphExecution) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    void* kernel_args[] = { (void*)&d_c, (void*)&d_a, (void*)&d_b, (void*)&n };
    cudaKernelNodeParams kernelParams = {0};
    kernelParams.func = (void*)vector_add;
    kernelParams.gridDim = dim3((n + 255) / 256, 1, 1);
    kernelParams.blockDim = dim3(256, 1, 1);
    kernelParams.kernelParams = kernel_args;

    cudaGraphNode_t kernel_node;
    CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph, nullptr, 0, &kernelParams));
    
    cudaGraphExec_t exec_graph;
    CUDA_CHECK(cudaGraphInstantiate(&exec_graph, graph, nullptr, nullptr, 0));
    
    CUDA_CHECK(cudaGraphLaunch(exec_graph, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    CUDA_CHECK(cudaMemcpy(h_c.data(), d_c, size, cudaMemcpyDeviceToHost));

    for (int i = 0; i < n; ++i) {
        ASSERT_FLOAT_EQ(h_c[i], h_a[i] + h_b[i]);
    }
    
    CUDA_CHECK(cudaGraphExecDestroy(exec_graph));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(IntegrationTest, FullH2DKernelD2HGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    cudaGraphNode_t memcpy_h2d_a, memcpy_h2d_b;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpy_h2d_a, graph, nullptr, 0, d_a, h_a.data(), size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpy_h2d_b, graph, nullptr, 0, d_b, h_b.data(), size, cudaMemcpyHostToDevice));

    void* kernel_args[] = {&d_c, &d_a, &d_b, &n};
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)vector_add;
    kernelParams.gridDim = dim3((n + 255) / 256, 1, 1);
    kernelParams.blockDim = dim3(256, 1, 1);
    kernelParams.kernelParams = kernel_args;
    
    cudaGraphNode_t kernel_node;
    cudaGraphNode_t deps[] = {memcpy_h2d_a, memcpy_h2d_b};
    CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph, deps, 2, &kernelParams));

    cudaGraphNode_t memcpy_d2h_c;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpy_d2h_c, graph, &kernel_node, 1, h_c.data(), d_c, size, cudaMemcpyDeviceToHost));
    
    cudaGraphExec_t exec_graph;
    CUDA_CHECK(cudaGraphInstantiate(&exec_graph, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphLaunch(exec_graph, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    for (int i = 0; i < n; ++i) {
        ASSERT_FLOAT_EQ(h_c[i], h_a[i] + h_b[i]);
    }

    CUDA_CHECK(cudaGraphExecDestroy(exec_graph));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(IntegrationTest, RelaunchGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    void* kernel_args[] = { (void*)&d_c, (void*)&d_a, (void*)&d_b, (void*)&n };
    cudaKernelNodeParams kParams = {0};
    kParams.func = (void*)vector_add;
    kParams.gridDim = dim3((n + 255) / 256, 1, 1);
    kParams.blockDim = dim3(256, 1, 1);
    kParams.kernelParams = kernel_args;
    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &kParams));
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    // Launch 1
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaMemcpy(h_c.data(), d_c, size, cudaMemcpyDeviceToHost));
    for (int i = 0; i < n; ++i) ASSERT_FLOAT_EQ(h_c[i], h_a[i] + h_b[i]);

    // Modify source data and relaunch
    for (int i = 0; i < n; ++i) { h_a[i] = 1.0f; h_b[i] = -1.0f; }
    CUDA_CHECK(cudaMemcpy(d_a, h_a.data(), size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_b, h_b.data(), size, cudaMemcpyHostToDevice));

    // Launch 2
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaMemcpy(h_c.data(), d_c, size, cudaMemcpyDeviceToHost));
    for (int i = 0; i < n; ++i) ASSERT_FLOAT_EQ(h_c[i], 0.0f);

    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// --- More Success Cases (No Fixture Needed) ---

class IntegrationSimpleTest : public ::testing::Test {};

TEST_F(IntegrationSimpleTest, EmptyGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
}

TEST_F(IntegrationSimpleTest, MemcpyOnlyGraph) {
    const size_t size = 1024;
    char *h_src = new char[size];
    char *h_dst = new char[size];
    memset(h_src, 'A', size);
    memset(h_dst, 0, size);
    char *d_src, *d_dst;
    CUDA_CHECK(cudaMalloc(&d_src, size));
    CUDA_CHECK(cudaMalloc(&d_dst, size));
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaGraphNode_t h2d, d2d, d2h;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&h2d, graph, nullptr, 0, d_src, h_src, size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&d2d, graph, &h2d, 1, d_dst, d_src, size, cudaMemcpyDeviceToDevice));
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&d2h, graph, &d2d, 1, h_dst, d_dst, size, cudaMemcpyDeviceToHost));
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    for(size_t i = 0; i < size; ++i) ASSERT_EQ(h_dst[i], 'A');
    delete[] h_src;
    delete[] h_dst;
    CUDA_CHECK(cudaFree(d_src));
    CUDA_CHECK(cudaFree(d_dst));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
}

TEST_F(IntegrationSimpleTest, MemsetGraph) {
    const size_t n = 1024;
    const size_t size = n * sizeof(int);
    int* d_mem;
    CUDA_CHECK(cudaMalloc(&d_mem, size));
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaMemsetParams memset_params = {};
    memset_params.dst = d_mem;
    memset_params.value = 0xFF;
    memset_params.elementSize = sizeof(int);
    memset_params.width = n;
    memset_params.height = 1;
    cudaGraphNode_t memset_node;
    CUDA_CHECK(cudaGraphAddMemsetNode(&memset_node, graph, nullptr, 0, &memset_params));
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    int* h_mem = new int[n];
    CUDA_CHECK(cudaMemcpy(h_mem, d_mem, size, cudaMemcpyDeviceToHost));
    for(size_t i=0; i<n; ++i) ASSERT_EQ(h_mem[i], -1);
    delete[] h_mem;
    CUDA_CHECK(cudaFree(d_mem));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
}

TEST_F(IntegrationSimpleTest, UpdateAndRelaunch) {
    float* d_data;
    CUDA_CHECK(cudaMalloc(&d_data, sizeof(float)));
    float h_data = 5.0f;
    CUDA_CHECK(cudaMemcpy(d_data, &h_data, sizeof(float), cudaMemcpyHostToDevice));
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    float factor = 2.0f;
    void* args[] = { &d_data, &factor };
    cudaKernelNodeParams params = {};
    params.func = (void*)multiply_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    params.kernelParams = args;
    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaMemcpy(&h_data, d_data, sizeof(float), cudaMemcpyDeviceToHost));
    ASSERT_FLOAT_EQ(h_data, 10.0f);
    factor = 3.0f;
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, node, &params));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaMemcpy(&h_data, d_data, sizeof(float), cudaMemcpyDeviceToHost));
    ASSERT_FLOAT_EQ(h_data, 30.0f);
    CUDA_CHECK(cudaFree(d_data));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream));
}

TEST_F(IntegrationSimpleTest, CloneAndDestroyOriginal) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {};
    params.func = (void*)multiply_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));
    cudaGraph_t clone;
    CUDA_CHECK(cudaGraphClone(&clone, graph));
    CUDA_CHECK(cudaGraphDestroy(graph)); // Original destroyed
    cudaGraphExec_t exec;
    // Cloned graph should still be valid
    ASSERT_EQ(cudaGraphInstantiate(&exec, clone, nullptr, nullptr, 0), cudaSuccess);
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(clone));
}


// --- Failure and Error Cases ---

class IntegrationErrorTest : public ::testing::Test {};

TEST_F(IntegrationErrorTest, LaunchDestroyedExec) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    ASSERT_NE(cudaGraphLaunch(exec, stream), cudaSuccess);
    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(IntegrationErrorTest, InvalidKernelParameters) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaKernelNodeParams params = {};
    params.func = (void*)vector_add;
    params.gridDim = {1,1,1};
    params.blockDim = {2048, 1, 1}; // Invalid: > 1024 threads
    cudaGraphNode_t node;
    cudaError_t add_err = cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params);
    if (add_err == cudaSuccess) {
        cudaGraphExec_t exec;
        ASSERT_NE(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0), cudaSuccess);
    } else {
        ASSERT_NE(add_err, cudaSuccess);
    }
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(IntegrationErrorTest, InstantiateWithInvalidDependency) {
    cudaGraph_t graph1, graph2;
    CUDA_CHECK(cudaGraphCreate(&graph1, 0));
    CUDA_CHECK(cudaGraphCreate(&graph2, 0));
    cudaKernelNodeParams params = {};
    params.func = (void*)vector_add;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    cudaGraphNode_t node1, node_from_graph2;
    CUDA_CHECK(cudaGraphAddKernelNode(&node1, graph1, nullptr, 0, &params));
    CUDA_CHECK(cudaGraphAddKernelNode(&node_from_graph2, graph2, nullptr, 0, &params));
    // Should fail to add a dependency between nodes of different graphs.
    ASSERT_NE(cudaGraphAddDependencies(graph1, &node1, &node_from_graph2, 1), cudaSuccess);
    CUDA_CHECK(cudaGraphDestroy(graph1));
    CUDA_CHECK(cudaGraphDestroy(graph2));
}

TEST_F(IntegrationErrorTest, MemcpyNodeNullDevicePtr) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    char h_buf[16];
    cudaGraphNode_t node;
    cudaError_t err = cudaGraphAddMemcpyNode1D(&node, graph, nullptr, 0, nullptr, h_buf, 16, cudaMemcpyHostToDevice);
    if (err == cudaSuccess) {
        cudaGraphExec_t exec;
        ASSERT_NE(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0), cudaSuccess);
    } else {
        ASSERT_NE(err, cudaSuccess);
    }
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(IntegrationErrorTest, MemcpyNodeZeroSize) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    char* d_buf;
    char* h_buf = new char[1];
    CUDA_CHECK(cudaMalloc(&d_buf, 1));
    cudaGraphNode_t node;
    // A zero-size copy is a valid no-op. This should succeed.
    ASSERT_EQ(cudaGraphAddMemcpyNode1D(&node, graph, nullptr, 0, d_buf, h_buf, 0, cudaMemcpyHostToDevice), cudaSuccess);
    cudaGraphExec_t exec;
    ASSERT_EQ(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0), cudaSuccess);
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaFree(d_buf));
    CUDA_CHECK(cudaGraphDestroy(graph));
    delete[] h_buf;
}

TEST_F(IntegrationErrorTest, LaunchOnDestroyedStream) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaStreamDestroy(stream));
    ASSERT_NE(cudaGraphLaunch(exec, stream), cudaSuccess);
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}
