#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

class MultiGpuTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaGetDeviceCount(&device_count));
        if (device_count < 2) {
            GTEST_SKIP() << "Skipping Multi-GPU test, requires at least 2 GPUs.";
        }
    }

    int device_count;
};

// --- KERNEL for verification ---
__global__ void check_value(const int* data, int expected) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        if (*data != expected) {
            printf("Assertion failed: device value %d does not match expected value %d\\n", *data, expected);
            asm("trap;");
        }
    }
}


TEST_F(MultiGpuTest, PeerToPeerCopyInGraph) {
    const int val = 123;
    const size_t size = sizeof(int);
    int *d0_ptr, *d1_ptr;

    // Allocate on GPU 0
    CUDA_CHECK(cudaSetDevice(0));
    CUDA_CHECK(cudaMalloc(&d0_ptr, size));

    // Allocate on GPU 1
    CUDA_CHECK(cudaSetDevice(1));
    CUDA_CHECK(cudaMalloc(&d1_ptr, size));
    
    // Enable peer access
    CUDA_CHECK(cudaSetDevice(0));
    cudaError_t p2p_err1 = cudaDeviceEnablePeerAccess(1, 0);
    CUDA_CHECK(cudaSetDevice(1));
    cudaError_t p2p_err2 = cudaDeviceEnablePeerAccess(0, 0);

    if (p2p_err1 != cudaSuccess || p2p_err2 != cudaSuccess) {
        // If peer access is not supported (e.g. on consumer GPUs), we can't run this test.
        cudaFree(d0_ptr); // Need to free memory allocated on GPU 0
        cudaSetDevice(1);
        cudaFree(d1_ptr); // And on GPU 1
        GTEST_SKIP() << "Skipping test, P2P access not supported between GPUs.";
    }

    // Create a stream on GPU 1
    cudaStream_t stream1;
    CUDA_CHECK(cudaStreamCreate(&stream1));

    // Create a graph
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    // 1. Memset on GPU 0
    cudaGraphNode_t memset_node;
    cudaMemsetParams memset_params = {};
    memset_params.dst = d0_ptr;
    memset_params.value = val;
    memset_params.elementSize = 1;
    memset_params.width = size;
    memset_params.height = 1;
    CUDA_CHECK(cudaGraphAddMemsetNode(&memset_node, graph, nullptr, 0, &memset_params));
    
    // 2. Peer-to-Peer copy from GPU 0 to GPU 1
    cudaGraphNode_t p2p_copy_node;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&p2p_copy_node, graph, &memset_node, 1, d1_ptr, d0_ptr, size, cudaMemcpyDeviceToDevice));
    
    // 3. Verification kernel on GPU 1
    cudaGraphNode_t kernel_node;
    void* kernel_args[] = {&d1_ptr, (void*)&val};
    cudaKernelNodeParams kernel_params = {};
    kernel_params.func = (void*)check_value;
    kernel_params.gridDim = {1,1,1};
    kernel_params.blockDim = {1,1,1};
    kernel_params.kernelParams = kernel_args;
    CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph, &p2p_copy_node, 1, &kernel_params));

    // Instantiate and Launch on GPU 1's stream
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphLaunch(exec, stream1));
    CUDA_CHECK(cudaStreamSynchronize(stream1));

    // Cleanup
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaStreamDestroy(stream1));
    CUDA_CHECK(cudaSetDevice(0));
    CUDA_CHECK(cudaFree(d0_ptr));
    CUDA_CHECK(cudaSetDevice(1));
    CUDA_CHECK(cudaFree(d1_ptr));
}
