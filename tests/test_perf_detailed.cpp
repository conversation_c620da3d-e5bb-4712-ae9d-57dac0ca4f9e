#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <chrono>
#include <vector>
#include <iostream>
#include <iomanip>

// A simple kernel for performance testing
__global__ void SimpleAdd(float* a, const float* b, const float* c, size_t n) {
    size_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        a[idx] = b[idx] + c[idx];
    }
}

class DetailedPerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        cudaMalloc(&d_a, size_bytes);
        cudaMalloc(&d_b, size_bytes);
        cudaMalloc(&d_c, size_bytes);
        
        cudaEventCreate(&start_event);
        cudaEventCreate(&stop_event);
    }

    void TearDown() override {
        cudaFree(d_a);
        cudaFree(d_b);
        cudaFree(d_c);

        cudaEventDestroy(start_event);
        cudaEventDestroy(stop_event);
    }

    const size_t N = 1024;
    const size_t size_bytes = N * sizeof(float);
    float *d_a = nullptr, *d_b = nullptr, *d_c = nullptr;
    const int iterations = 100; // Number of kernels/operations in one loop
    const int test_loops = 50;  // Number of times we repeat the test loop

    cudaEvent_t start_event, stop_event;
};

TEST_F(DetailedPerformanceTest, ManySmallKernelsDetailed) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);

    std::vector<float*> d_a_vec(iterations), d_b_vec(iterations), d_c_vec(iterations);
    std::vector<void*> kernel_args_vec(iterations);
    for(int i = 0; i < iterations; ++i) {
        cudaMalloc(&d_a_vec[i], size_bytes);
        cudaMalloc(&d_b_vec[i], size_bytes);
        cudaMalloc(&d_c_vec[i], size_bytes);
        kernel_args_vec[i] = {d_a_vec[i], d_b_vec[i], d_c_vec[i], (void*)&N};
    }

    dim3 block(256, 1, 1);
    dim3 grid((N + block.x - 1) / block.x, 1, 1);
    
    // --- 1. Without CUDA Graph ---
    cudaEventRecord(start_event, stream);
    auto host_start_no_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        for (int j = 0; j < iterations; ++j) {
            SimpleAdd<<<grid, block, 0, stream>>>((float*)kernel_args_vec[j][0], (float*)kernel_args_vec[j][1], (float*)kernel_args_vec[j][2], N);
        }
    }
    auto host_end_no_graph = std::chrono::high_resolution_clock::now();
    cudaEventRecord(stop_event, stream);
    cudaEventSynchronize(stop_event);
    
    float device_ms_no_graph = 0;
    cudaEventElapsedTime(&device_ms_no_graph, start_event, stop_event);
    std::chrono::duration<double, std::milli> host_ms_no_graph = host_end_no_graph - host_start_no_graph;

    // --- 2. With CUDA Graph ---
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    for (int i = 0; i < iterations; ++i) {
        cudaKernelNodeParams kernelParams = {};
        kernelParams.func = (void*)SimpleAdd;
        kernelParams.gridDim = grid;
        kernelParams.blockDim = block;
        kernelParams.args = kernel_args_vec.data();
        cudaGraphAddKernelNode(nullptr, graph, nullptr, 0, &kernelParams);
    }
    
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    cudaEventRecord(start_event, stream);
    auto host_start_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        cudaGraphLaunch(exec, stream);
    }
    auto host_end_graph = std::chrono::high_resolution_clock::now();
    cudaEventRecord(stop_event, stream);
    cudaEventSynchronize(stop_event);

    float device_ms_graph = 0;
    cudaEventElapsedTime(&device_ms_graph, start_event, stop_event);
    std::chrono::duration<double, std::milli> host_ms_graph = host_end_graph - host_start_graph;

    // --- 3. Report Results ---
    double host_gain = (host_ms_no_graph.count() - host_ms_graph.count()) / host_ms_no_graph.count() * 100.0;
    double device_gain = (device_ms_no_graph - device_ms_graph) / device_ms_no_graph * 100.0;

    std::cout << std::fixed << std::setprecision(3);
    std::cout << "\n[   INFO   ] Detailed Performance: ManySmallKernels" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;
    std::cout << "| Metric         | No Graph (ms) | With Graph (ms) | Gain (%) |" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;
    std::cout << "| Host Launch    | " << std::setw(13) << host_ms_no_graph.count() 
              << " | " << std::setw(15) << host_ms_graph.count() 
              << " | " << std::setw(6) << host_gain << " |" << std::endl;
    std::cout << "| Device Exec    | " << std::setw(13) << device_ms_no_graph 
              << " | " << std::setw(15) << device_ms_graph 
              << " | " << std::setw(6) << device_gain << " |" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;

    for(int i = 0; i < iterations; ++i) {
        cudaFree(d_a_vec[i]);
        cudaFree(d_b_vec[i]);
        cudaFree(d_c_vec[i]);
    }
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
    cudaStreamDestroy(stream);
}

TEST_F(DetailedPerformanceTest, DependentKernelAndMemcpyDetailed) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);

    float* h_a = new float[N];
    float* h_b = new float[N];
    for(size_t i = 0; i < N; ++i) h_b[i] = (float)i;
    
    dim3 grid((N + 255) / 256, 1, 1);
    dim3 block(256, 1, 1);
    void* kernelArgs[] = {&d_a, &d_b, &d_c, (void*)&N};

    // --- 1. Without CUDA Graph ---
    cudaEventRecord(start_event, stream);
    auto host_start_no_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        cudaMemcpyAsync(d_b, h_b, size_bytes, cudaMemcpyHostToDevice, stream);
        SimpleAdd<<<grid, block, 0, stream>>>(d_a, d_b, d_c, N);
        cudaMemcpyAsync(h_a, d_a, size_bytes, cudaMemcpyDeviceToHost, stream);
    }
    auto host_end_no_graph = std::chrono::high_resolution_clock::now();
    cudaEventRecord(stop_event, stream);
    cudaEventSynchronize(stop_event);

    float device_ms_no_graph = 0;
    cudaEventElapsedTime(&device_ms_no_graph, start_event, stop_event);
    std::chrono::duration<double, std::milli> host_ms_no_graph = host_end_no_graph - host_start_no_graph;

    // --- 2. With CUDA Graph ---
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    cudaGraphNode_t memcpyH2DNode, kernelNode, memcpyD2HNode;
    cudaGraphAddMemcpyNode1D(&memcpyH2DNode, graph, nullptr, 0, d_b, h_b, size_bytes, cudaMemcpyHostToDevice);
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)SimpleAdd;
    kernelParams.gridDim = grid;
    kernelParams.blockDim = block;
    kernelParams.args = kernelArgs;
    cudaGraphAddKernelNode(&kernelNode, graph, &memcpyH2DNode, 1, &kernelParams);
    cudaGraphAddMemcpyNode1D(&memcpyD2HNode, graph, &kernelNode, 1, h_a, d_a, size_bytes, cudaMemcpyDeviceToHost);
    
    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    cudaEventRecord(start_event, stream);
    auto host_start_graph = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        cudaGraphLaunch(exec, stream);
    }
    auto host_end_graph = std::chrono::high_resolution_clock::now();
    cudaEventRecord(stop_event, stream);
    cudaEventSynchronize(stop_event);

    float device_ms_graph = 0;
    cudaEventElapsedTime(&device_ms_graph, start_event, stop_event);
    std::chrono::duration<double, std::milli> host_ms_graph = host_end_graph - host_start_graph;

    // --- 3. Report Results ---
    double host_gain = (host_ms_no_graph.count() - host_ms_graph.count()) / host_ms_no_graph.count() * 100.0;
    double device_gain = (device_ms_no_graph - device_ms_graph) / device_ms_no_graph * 100.0;

    std::cout << std::fixed << std::setprecision(3);
    std::cout << "\n[   INFO   ] Detailed Performance: DependentKernelAndMemcpy" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;
    std::cout << "| Metric         | No Graph (ms) | With Graph (ms) | Gain (%) |" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;
    std::cout << "| Host Launch    | " << std::setw(13) << host_ms_no_graph.count() 
              << " | " << std::setw(15) << host_ms_graph.count() 
              << " | " << std::setw(6) << host_gain << " |" << std::endl;
    std::cout << "| Device Exec    | " << std::setw(13) << device_ms_no_graph 
              << " | " << std::setw(15) << device_ms_graph 
              << " | " << std::setw(6) << device_gain << " |" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;

    delete[] h_a;
    delete[] h_b;
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
    cudaStreamDestroy(stream);
}

// A second kernel to make the graph more complex
__global__ void SimpleMultiply(float* a, const float* b, float factor, size_t n) {
    size_t idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        a[idx] = b[idx] * factor;
    }
}

TEST_F(DetailedPerformanceTest, UpdatableGraphDetailed) {
    cudaStream_t stream;
    cudaStreamCreate(&stream);

    float *d_intermediate, *d_final_output;
    cudaMalloc(&d_intermediate, size_bytes);
    cudaMalloc(&d_final_output, size_bytes);

    std::vector<float> h_input1(N, 1.0f), h_input2(N, 2.0f);
    float *d_input1, *d_input2;
    cudaMalloc(&d_input1, size_bytes);
    cudaMalloc(&d_input2, size_bytes);
    cudaMemcpy(d_input1, h_input1.data(), size_bytes, cudaMemcpyHostToDevice);
    cudaMemcpy(d_input2, h_input2.data(), size_bytes, cudaMemcpyHostToDevice);

    dim3 grid((N + 255) / 256, 1, 1);
    dim3 block(256, 1, 1);
    size_t n_val = N;

    // --- 1. Without Graph (Re-instantiating) ---
    // This is a proxy for non-graph updates. We re-create the graph each time.
    cudaEventRecord(start_event, stream);
    auto host_start_reinstantiate = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        float factor = (i % 2 == 0) ? 10.0f : 20.0f;
        
        cudaGraph_t temp_graph;
        cudaGraphCreate(&temp_graph, 0);
        
        void* add_args[] = {&d_intermediate, &d_input1, &d_input2, &n_val};
        cudaKernelNodeParams add_params = { (void*)SimpleAdd, grid, block, 0, add_args, nullptr };
        cudaGraphNode_t add_node;
        cudaGraphAddKernelNode(&add_node, temp_graph, nullptr, 0, &add_params);

        void* mul_args[] = {&d_final_output, &d_intermediate, &factor, &n_val};
        cudaKernelNodeParams mul_params = { (void*)SimpleMultiply, grid, block, 0, mul_args, nullptr };
        cudaGraphNode_t mul_node;
        cudaGraphAddKernelNode(&mul_node, temp_graph, &add_node, 1, &mul_params);

        cudaGraphExec_t temp_exec;
        cudaGraphInstantiate(&temp_exec, temp_graph, nullptr, nullptr, 0);
        cudaGraphLaunch(temp_exec, stream);
        
        cudaGraphExecDestroy(temp_exec);
        cudaGraphDestroy(temp_graph);
    }
    auto host_end_reinstantiate = std::chrono::high_resolution_clock::now();
    cudaEventRecord(stop_event, stream);
    cudaStreamSynchronize(stream); // Use stream sync here as events are on the stream

    float device_ms_reinstantiate = 0;
    cudaEventElapsedTime(&device_ms_reinstantiate, start_event, stop_event);
    std::chrono::duration<double, std::milli> host_ms_reinstantiate = host_end_reinstantiate - host_start_reinstantiate;

    // --- 2. With Graph Update ---
    cudaGraph_t graph;
    cudaGraphCreate(&graph, 0);
    
    void* add_args_tpl[] = {&d_intermediate, &d_input1, &d_input2, &n_val};
    cudaKernelNodeParams add_params_tpl = { (void*)SimpleAdd, grid, block, 0, add_args_tpl, nullptr };
    cudaGraphNode_t add_node_tpl;
    cudaGraphAddKernelNode(&add_node_tpl, graph, nullptr, 0, &add_params_tpl);

    float factor_for_update = 10.0f;
    void* mul_args_tpl[] = {&d_final_output, &d_intermediate, &factor_for_update, &n_val};
    cudaKernelNodeParams mul_params_tpl = { (void*)SimpleMultiply, grid, block, 0, mul_args_tpl, nullptr };
    cudaGraphNode_t mul_node_tpl;
    cudaGraphAddKernelNode(&mul_node_tpl, graph, &add_node_tpl, 1, &mul_params_tpl);

    cudaGraphExec_t exec;
    cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0);

    cudaEventRecord(start_event, stream);
    auto host_start_update = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < test_loops; ++i) {
        factor_for_update = (i % 2 == 0) ? 10.0f : 20.0f;
        // The kernel args for mul_params_tpl are updated in place
        cudaGraphExecKernelNodeSetParams(exec, mul_node_tpl, &mul_params_tpl);
        cudaGraphLaunch(exec, stream);
    }
    auto host_end_update = std::chrono::high_resolution_clock::now();
    cudaEventRecord(stop_event, stream);
    cudaStreamSynchronize(stream);

    float device_ms_update = 0;
    cudaEventElapsedTime(&device_ms_update, start_event, stop_event);
    std::chrono::duration<double, std::milli> host_ms_update = host_end_update - host_start_update;

    // --- 3. Report Results ---
    double host_gain = (host_ms_reinstantiate.count() - host_ms_update.count()) / host_ms_reinstantiate.count() * 100.0;
    double device_gain = (device_ms_reinstantiate - device_ms_update) / device_ms_reinstantiate * 100.0;

    std::cout << std::fixed << std::setprecision(3);
    std::cout << "\n[   INFO   ] Detailed Performance: UpdatableGraph" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;
    std::cout << "| Metric         | Re-instantiate(ms)| Update (ms)     | Gain (%) |" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;
    std::cout << "| Host Launch    | " << std::setw(13) << host_ms_reinstantiate.count() 
              << " | " << std::setw(15) << host_ms_update.count() 
              << " | " << std::setw(6) << host_gain << " |" << std::endl;
    std::cout << "| Device Exec    | " << std::setw(13) << device_ms_reinstantiate 
              << " | " << std::setw(15) << device_ms_update 
              << " | " << std::setw(6) << device_gain << " |" << std::endl;
    std::cout << "----------------------------------------------------------" << std::endl;

    cudaFree(d_intermediate);
    cudaFree(d_final_output);
    cudaFree(d_input1);
    cudaFree(d_input2);
    cudaGraphExecDestroy(exec);
    cudaGraphDestroy(graph);
    cudaStreamDestroy(stream);
}
