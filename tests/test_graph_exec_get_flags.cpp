#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <thread>
#include <future>
#include <atomic>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

__global__ void simple_kernel(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        data[idx] = data[idx] * 2.0f;
    }
}

__global__ void compute_heavy_kernel(float* data, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        float val = data[idx];
        for (int i = 0; i < 1000; i++) {
            val = sinf(val) * cosf(val);
        }
        data[idx] = val;
    }
}

class GraphExecGetFlagsTest : public ::testing::Test {
protected:
    void SetUp() override {
        CUDA_CHECK(cudaFree(nullptr));
        
        CUDA_CHECK(cudaMalloc(&d_data, data_size * sizeof(float)));
        CUDA_CHECK(cudaMalloc(&d_temp, data_size * sizeof(float)));
        
        h_data.resize(data_size);
        for (int i = 0; i < data_size; i++) {
            h_data[i] = static_cast<float>(i);
        }
        CUDA_CHECK(cudaMemcpy(d_data, h_data.data(), data_size * sizeof(float), cudaMemcpyHostToDevice));
        
        CUDA_CHECK(cudaStreamCreate(&stream));
        CUDA_CHECK(cudaEventCreate(&event));
    }
    
    void TearDown() override {
        if (d_data) cudaFree(d_data);
        if (d_temp) cudaFree(d_temp);
        if (stream) cudaStreamDestroy(stream);
        if (event) cudaEventDestroy(event);
    }
    
    void CreateBasicGraph(cudaGraph_t* graph, cudaGraphExec_t* exec, unsigned long long flags = 0) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        
        cudaGraphNode_t kernelNode;
        cudaKernelNodeParams kernelParams = {};
        kernelParams.func = (void*)simple_kernel;
        kernelParams.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams.blockDim = {256, 1, 1};
        kernelParams.sharedMemBytes = 0;
        kernelParams.kernelParams = (void**)kernel_args;
        kernelParams.extra = nullptr;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&kernelNode, *graph, nullptr, 0, &kernelParams));
        CUDA_CHECK(cudaGraphInstantiate(exec, *graph, nullptr, nullptr, flags));
    }
    
    void CreateComplexGraph(cudaGraph_t* graph, cudaGraphExec_t* exec, unsigned long long flags = 0) {
        CUDA_CHECK(cudaGraphCreate(graph, 0));
        
        cudaGraphNode_t memcpyNode, kernelNode1, kernelNode2, memsetNode;
        
        CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpyNode, *graph, nullptr, 0, d_temp, d_data, 
                                          data_size * sizeof(float), cudaMemcpyDeviceToDevice));
        
        cudaKernelNodeParams kernelParams1 = {};
        kernelParams1.func = (void*)simple_kernel;
        kernelParams1.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams1.blockDim = {256, 1, 1};
        kernelParams1.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&kernelNode1, *graph, &memcpyNode, 1, &kernelParams1));
        
        cudaKernelNodeParams kernelParams2 = {};
        kernelParams2.func = (void*)compute_heavy_kernel;
        kernelParams2.gridDim = {(data_size + 255) / 256, 1, 1};
        kernelParams2.blockDim = {256, 1, 1};
        kernelParams2.kernelParams = (void**)kernel_args;
        
        CUDA_CHECK(cudaGraphAddKernelNode(&kernelNode2, *graph, &kernelNode1, 1, &kernelParams2));
        
        cudaMemsetParams memsetParams = {};
        memsetParams.dst = d_temp;
        memsetParams.value = 0;
        memsetParams.pitch = 0;
        memsetParams.elementSize = sizeof(float);
        memsetParams.width = data_size;
        memsetParams.height = 1;
        
        CUDA_CHECK(cudaGraphAddMemsetNode(&memsetNode, *graph, &kernelNode2, 1, &memsetParams));
        
        CUDA_CHECK(cudaGraphInstantiate(exec, *graph, nullptr, nullptr, flags));
    }
    
    const int data_size = 1024;
    float* d_data = nullptr;
    float* d_temp = nullptr;
    std::vector<float> h_data;
    cudaStream_t stream = nullptr;
    cudaEvent_t event = nullptr;
    
    void* kernel_args[2] = {&d_data, &data_size};
};

// Test 1: Basic functionality - Get flags from a simple graph
TEST_F(GraphExecGetFlagsTest, BasicFunctionality) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    
    CreateBasicGraph(&graph, &exec, 0);
    
    unsigned long long flags;
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags));
    
    EXPECT_EQ(flags, 0ULL);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 2: Test with various instantiation flags
TEST_F(GraphExecGetFlagsTest, VariousInstantiationFlags) {
    struct FlagTest {
        unsigned long long input_flags;
        const char* description;
    };
    
    std::vector<FlagTest> flag_tests = {
        {0, "No flags"},
        {cudaGraphInstantiateFlagAutoFreeOnLaunch, "Auto free on launch"},
        {cudaGraphInstantiateFlagUpload, "Upload"},
        {cudaGraphInstantiateFlagDeviceLaunch, "Device launch"},
        {cudaGraphInstantiateFlagUseNodePriority, "Use node priority"},
        {cudaGraphInstantiateFlagAutoFreeOnLaunch | cudaGraphInstantiateFlagUpload, "Multiple flags"}
    };
    
    for (const auto& test : flag_tests) {
        cudaGraph_t graph;
        cudaGraphExec_t exec;
        
        CreateBasicGraph(&graph, &exec, test.input_flags);
        
        unsigned long long retrieved_flags;
        CUDA_CHECK(cudaGraphExecGetFlags(exec, &retrieved_flags));
        
        EXPECT_EQ(retrieved_flags, test.input_flags) << "Failed for: " << test.description;
        
        CUDA_CHECK(cudaGraphExecDestroy(exec));
        CUDA_CHECK(cudaGraphDestroy(graph));
    }
}

// Test 3: Null pointer validation
TEST_F(GraphExecGetFlagsTest, NullPointerValidation) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    unsigned long long flags;
    
    CreateBasicGraph(&graph, &exec, 0);
    
    EXPECT_EQ(cudaGraphExecGetFlags(nullptr, &flags), cudaErrorInvalidValue);
    EXPECT_EQ(cudaGraphExecGetFlags(exec, nullptr), cudaErrorInvalidValue);
    EXPECT_EQ(cudaGraphExecGetFlags(nullptr, nullptr), cudaErrorInvalidValue);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 4: Invalid graph exec handle
TEST_F(GraphExecGetFlagsTest, InvalidGraphExecHandle) {
    cudaGraphExec_t invalid_exec = nullptr;
    unsigned long long flags;
    
    EXPECT_EQ(cudaGraphExecGetFlags(invalid_exec, &flags), cudaErrorInvalidValue);
    
    cudaGraphExec_t fake_exec = reinterpret_cast<cudaGraphExec_t>(0xDEADBEEF);
    EXPECT_NE(cudaGraphExecGetFlags(fake_exec, &flags), cudaSuccess);
}

// Test 5: Complex graph with multiple node types
TEST_F(GraphExecGetFlagsTest, ComplexGraphFlags) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    
    CreateComplexGraph(&graph, &exec, cudaGraphInstantiateFlagAutoFreeOnLaunch);
    
    unsigned long long flags;
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags));
    
    EXPECT_EQ(flags, cudaGraphInstantiateFlagAutoFreeOnLaunch);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 6: Flags persistence after graph execution
TEST_F(GraphExecGetFlagsTest, FlagsPersistenceAfterExecution) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    const unsigned long long test_flags = cudaGraphInstantiateFlagUpload;
    
    CreateBasicGraph(&graph, &exec, test_flags);
    
    unsigned long long flags_before;
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags_before));
    EXPECT_EQ(flags_before, test_flags);
    
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    unsigned long long flags_after;
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags_after));
    EXPECT_EQ(flags_after, test_flags);
    EXPECT_EQ(flags_before, flags_after);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 7: Multiple graph exec instances from same graph
TEST_F(GraphExecGetFlagsTest, MultipleExecInstances) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaGraphNode_t kernelNode;
    cudaKernelNodeParams kernelParams = {};
    kernelParams.func = (void*)simple_kernel;
    kernelParams.gridDim = {(data_size + 255) / 256, 1, 1};
    kernelParams.blockDim = {256, 1, 1};
    kernelParams.kernelParams = (void**)kernel_args;
    
    CUDA_CHECK(cudaGraphAddKernelNode(&kernelNode, graph, nullptr, 0, &kernelParams));
    
    cudaGraphExec_t exec1, exec2, exec3;
    const unsigned long long flags1 = 0;
    const unsigned long long flags2 = cudaGraphInstantiateFlagAutoFreeOnLaunch;
    const unsigned long long flags3 = cudaGraphInstantiateFlagUpload;
    
    CUDA_CHECK(cudaGraphInstantiate(&exec1, graph, nullptr, nullptr, flags1));
    CUDA_CHECK(cudaGraphInstantiate(&exec2, graph, nullptr, nullptr, flags2));
    CUDA_CHECK(cudaGraphInstantiate(&exec3, graph, nullptr, nullptr, flags3));
    
    unsigned long long retrieved_flags1, retrieved_flags2, retrieved_flags3;
    CUDA_CHECK(cudaGraphExecGetFlags(exec1, &retrieved_flags1));
    CUDA_CHECK(cudaGraphExecGetFlags(exec2, &retrieved_flags2));
    CUDA_CHECK(cudaGraphExecGetFlags(exec3, &retrieved_flags3));
    
    EXPECT_EQ(retrieved_flags1, flags1);
    EXPECT_EQ(retrieved_flags2, flags2);
    EXPECT_EQ(retrieved_flags3, flags3);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec1));
    CUDA_CHECK(cudaGraphExecDestroy(exec2));
    CUDA_CHECK(cudaGraphExecDestroy(exec3));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 8: Thread safety test
TEST_F(GraphExecGetFlagsTest, ThreadSafety) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    const unsigned long long test_flags = cudaGraphInstantiateFlagUpload;
    
    CreateBasicGraph(&graph, &exec, test_flags);
    
    std::atomic<bool> test_passed{true};
    std::vector<std::future<void>> futures;
    const int num_threads = 10;
    const int iterations_per_thread = 100;
    
    for (int i = 0; i < num_threads; i++) {
        futures.push_back(std::async(std::launch::async, [&]() {
            for (int j = 0; j < iterations_per_thread; j++) {
                unsigned long long flags;
                cudaError_t result = cudaGraphExecGetFlags(exec, &flags);
                if (result != cudaSuccess || flags != test_flags) {
                    test_passed.store(false);
                    break;
                }
            }
        }));
    }
    
    for (auto& future : futures) {
        future.wait();
    }
    
    EXPECT_TRUE(test_passed.load());
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 9: Flags after graph updates
TEST_F(GraphExecGetFlagsTest, FlagsAfterGraphUpdates) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    const unsigned long long test_flags = cudaGraphInstantiateFlagDeviceLaunch;
    
    CreateBasicGraph(&graph, &exec, test_flags);
    
    unsigned long long flags_before;
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags_before));
    EXPECT_EQ(flags_before, test_flags);
    
    cudaGraphNode_t nodes[1];
    size_t num_nodes = 1;
    CUDA_CHECK(cudaGraphGetNodes(graph, nodes, &num_nodes));
    
    cudaKernelNodeParams new_params = {};
    new_params.func = (void*)compute_heavy_kernel;
    new_params.gridDim = {(data_size + 255) / 256, 1, 1};
    new_params.blockDim = {256, 1, 1};
    new_params.kernelParams = (void**)kernel_args;
    
    cudaGraphExecUpdateResult update_result;
    cudaGraphNode_t error_node;
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(exec, nodes[0], &new_params));
    
    unsigned long long flags_after;
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags_after));
    EXPECT_EQ(flags_after, test_flags);
    EXPECT_EQ(flags_before, flags_after);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 10: Boundary test with maximum flag values
TEST_F(GraphExecGetFlagsTest, MaximumFlagValues) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    
    const unsigned long long all_flags = 
        cudaGraphInstantiateFlagAutoFreeOnLaunch |
        cudaGraphInstantiateFlagUpload |
        cudaGraphInstantiateFlagDeviceLaunch |
        cudaGraphInstantiateFlagUseNodePriority;
    
    CreateBasicGraph(&graph, &exec, all_flags);
    
    unsigned long long retrieved_flags;
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &retrieved_flags));
    
    EXPECT_EQ(retrieved_flags, all_flags);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 11: Stress test with many graph exec instances
TEST_F(GraphExecGetFlagsTest, StressTestManyInstances) {
    const int num_instances = 100;
    std::vector<cudaGraph_t> graphs(num_instances);
    std::vector<cudaGraphExec_t> execs(num_instances);
    std::vector<unsigned long long> expected_flags(num_instances);
    
    for (int i = 0; i < num_instances; i++) {
        expected_flags[i] = i % 4;  // Cycle through different flag combinations
        CreateBasicGraph(&graphs[i], &execs[i], expected_flags[i]);
    }
    
    for (int i = 0; i < num_instances; i++) {
        unsigned long long retrieved_flags;
        CUDA_CHECK(cudaGraphExecGetFlags(execs[i], &retrieved_flags));
        EXPECT_EQ(retrieved_flags, expected_flags[i]) << "Failed for instance " << i;
    }
    
    for (int i = 0; i < num_instances; i++) {
        CUDA_CHECK(cudaGraphExecDestroy(execs[i]));
        CUDA_CHECK(cudaGraphDestroy(graphs[i]));
    }
}

// Test 12: Memory consistency check
TEST_F(GraphExecGetFlagsTest, MemoryConsistencyCheck) {
    cudaGraph_t graph;
    cudaGraphExec_t exec;
    const unsigned long long test_flags = cudaGraphInstantiateFlagAutoFreeOnLaunch;
    
    CreateBasicGraph(&graph, &exec, test_flags);
    
    unsigned long long flags1, flags2, flags3;
    
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags1));
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags2));
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &flags3));
    
    EXPECT_EQ(flags1, test_flags);
    EXPECT_EQ(flags2, test_flags);
    EXPECT_EQ(flags3, test_flags);
    EXPECT_EQ(flags1, flags2);
    EXPECT_EQ(flags2, flags3);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// Test 13: Integration test with captured graph
TEST_F(GraphExecGetFlagsTest, CapturedGraphIntegration) {
    cudaGraph_t captured_graph;
    cudaGraphExec_t exec;
    const unsigned long long test_flags = cudaGraphInstantiateFlagUpload;
    
    CUDA_CHECK(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal));
    
    simple_kernel<<<(data_size + 255) / 256, 256, 0, stream>>>(d_data, data_size);
    CUDA_CHECK(cudaGetLastError());
    
    CUDA_CHECK(cudaStreamEndCapture(stream, &captured_graph));
    CUDA_CHECK(cudaGraphInstantiate(&exec, captured_graph, nullptr, nullptr, test_flags));
    
    unsigned long long retrieved_flags;
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &retrieved_flags));
    EXPECT_EQ(retrieved_flags, test_flags);
    
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    
    CUDA_CHECK(cudaGraphExecGetFlags(exec, &retrieved_flags));
    EXPECT_EQ(retrieved_flags, test_flags);
    
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(captured_graph));
}