#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <cuda.h>
#include <vector>
#include <memory>
#include <atomic>
#include <thread>
#include <chrono>
#include <iostream>
#include <cassert>
#include <cstring>
#include "common.h"

// Dummy kernel for graph node tests
__global__ void dummy_kernel() {}

// UserObject test structures
struct TestUserData {
    int value;
    float* device_ptr;
    size_t size;
};

struct ComplexUserData {
    int* int_data;
    float* float_data;
    double* double_data;
    size_t sizes[3];
    cudaStream_t associated_stream;
    bool cleanup_flag;
};

// New structure for lifetime test
struct LifetimeTestUserData {
    std::atomic<bool>* destroyed_flag;
};


// UserObject destruction callbacks
void CUDART_CB test_user_object_destroy(void* user_data) {
    TestUserData* data = static_cast<TestUserData*>(user_data);
    if (data) {
        if (data->device_ptr) {
            cudaFree(data->device_ptr);
        }
        delete data;
    }
}

void CUDART_CB complex_user_object_destroy(void* user_data) {
    ComplexUserData* data = static_cast<ComplexUserData*>(user_data);
    if (!data) return;
    
    if (data->int_data) cudaFree(data->int_data);
    if (data->float_data) cudaFree(data->float_data);
    if (data->double_data) cudaFree(data->double_data);
    
    delete data;
}

void CUDART_CB lifetime_test_destroy(void* user_data) {
    LifetimeTestUserData* data = static_cast<LifetimeTestUserData*>(user_data);
    if (data) {
        data->destroyed_flag->store(true);
        delete data;
    }
}


// Test fixture for UserObject tests
class UserObjectTest : public ::testing::Test {
protected:
    cudaStream_t stream;
    cudaGraph_t graph;
    cudaGraphExec_t exec_graph;

    void SetUp() override {
        graph = nullptr;
        exec_graph = nullptr;
        ASSERT_EQ(cudaStreamCreate(&stream), cudaSuccess);
    }

    void TearDown() override {
        if (exec_graph) {
            cudaGraphExecDestroy(exec_graph);
        }
        if (graph) {
            cudaGraphDestroy(graph);
        }
        cudaStreamDestroy(stream);
    }
};

TEST_F(UserObjectTest, BasicCreationAndDestruction) {
    TestUserData* user_data = new TestUserData{42, nullptr, 1024};
    ASSERT_EQ(cudaMalloc(&user_data->device_ptr, user_data->size * sizeof(float)), cudaSuccess);
    
    cudaUserObject_t user_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, user_data, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);
    
    ASSERT_NE(user_obj, nullptr);
    
    ASSERT_EQ(cudaUserObjectRetain(user_obj), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);
}

TEST_F(UserObjectTest, GraphCapture) {
    TestUserData* user_data = new TestUserData{100, nullptr, 2048};
    ASSERT_EQ(cudaMalloc(&user_data->device_ptr, user_data->size * sizeof(float)), cudaSuccess);
    
    cudaUserObject_t user_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, user_data, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);
    
    ASSERT_EQ(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    
    float* d_data = nullptr;
    ASSERT_EQ(cudaMalloc(&d_data, 1024 * sizeof(float)), cudaSuccess);
    ASSERT_EQ(cudaMemsetAsync(d_data, 0, 1024 * sizeof(float), stream), cudaSuccess);
    
    ASSERT_EQ(cudaStreamEndCapture(stream, &graph), cudaSuccess);
    
    cudaGraphNode_t* nodes = nullptr;
    size_t num_nodes = 0;
    ASSERT_EQ(cudaGraphGetNodes(graph, nodes, &num_nodes), cudaSuccess);
    ASSERT_GT(num_nodes, 0);

    ASSERT_EQ(cudaGraphInstantiate(&exec_graph, graph, nullptr, nullptr, 0), cudaSuccess);
    
    ASSERT_EQ(cudaGraphLaunch(exec_graph, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);
    
    ASSERT_EQ(cudaFree(d_data), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);
}

TEST_F(UserObjectTest, MultipleObjects) {
    const int NUM_OBJECTS = 5;
    
    std::vector<cudaUserObject_t> user_objects(NUM_OBJECTS);
    std::vector<TestUserData*> user_data_list;
    
    for (int i = 0; i < NUM_OBJECTS; ++i) {
        TestUserData* data = new TestUserData{i, nullptr, 512 * (i + 1)};
        user_data_list.push_back(data);
        ASSERT_EQ(cudaMalloc(&data->device_ptr, data->size * sizeof(float)), cudaSuccess);
        ASSERT_EQ(cudaUserObjectCreate(&user_objects[i], data, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);
    }
    
    ASSERT_EQ(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    
    float* d_buffer = nullptr;
    ASSERT_EQ(cudaMalloc(&d_buffer, 4096 * sizeof(float)), cudaSuccess);
    
    for (size_t i = 0; i < user_objects.size(); ++i) {
        size_t offset = i * 512;
        ASSERT_EQ(cudaMemsetAsync(d_buffer + offset, user_data_list[i]->value, 512 * sizeof(float), stream), cudaSuccess);
    }
    
    ASSERT_EQ(cudaStreamEndCapture(stream, &graph), cudaSuccess);
    
    ASSERT_EQ(cudaGraphInstantiate(&exec_graph, graph, nullptr, nullptr, 0), cudaSuccess);
    
    ASSERT_EQ(cudaGraphLaunch(exec_graph, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);
    
    ASSERT_EQ(cudaFree(d_buffer), cudaSuccess);
    for (auto obj : user_objects) {
        ASSERT_EQ(cudaUserObjectRelease(obj, cudaUserObjectNoDestructorSync), cudaSuccess);
    }
}

TEST_F(UserObjectTest, ComplexData) {
    ComplexUserData* complex_data = new ComplexUserData{};
    complex_data->sizes[0] = 1024;
    complex_data->sizes[1] = 2048;
    complex_data->sizes[2] = 4096;
    
    ASSERT_EQ(cudaMalloc(&complex_data->int_data, complex_data->sizes[0] * sizeof(int)), cudaSuccess);
    ASSERT_EQ(cudaMalloc(&complex_data->float_data, complex_data->sizes[1] * sizeof(float)), cudaSuccess);
    ASSERT_EQ(cudaMalloc(&complex_data->double_data, complex_data->sizes[2] * sizeof(double)), cudaSuccess);
    
    complex_data->associated_stream = stream;
    complex_data->cleanup_flag = false;
    
    cudaUserObject_t user_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, complex_data, complex_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);
    
    ASSERT_EQ(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    
    ASSERT_EQ(cudaMemsetAsync(complex_data->int_data, 1, complex_data->sizes[0] * sizeof(int), stream), cudaSuccess);
    ASSERT_EQ(cudaMemsetAsync(complex_data->float_data, 2.0f, complex_data->sizes[1] * sizeof(float), stream), cudaSuccess);
    ASSERT_EQ(cudaMemsetAsync(complex_data->double_data, 3.0, complex_data->sizes[2] * sizeof(double), stream), cudaSuccess);
    
    ASSERT_EQ(cudaStreamEndCapture(stream, &graph), cudaSuccess);
    
    ASSERT_EQ(cudaGraphInstantiate(&exec_graph, graph, nullptr, nullptr, 0), cudaSuccess);
    
    for (int i = 0; i < 3; ++i) {
        ASSERT_EQ(cudaGraphLaunch(exec_graph, stream), cudaSuccess);
    }
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);
    
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);
}

TEST_F(UserObjectTest, ThreadSafety) {
    const int NUM_THREADS = 8;
    const int OPERATIONS_PER_THREAD = 1000;
    
    TestUserData* shared_data = new TestUserData{999, nullptr, 8192};
    ASSERT_EQ(cudaMalloc(&shared_data->device_ptr, shared_data->size * sizeof(float)), cudaSuccess);
    
    cudaUserObject_t shared_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&shared_obj, shared_data, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);
    
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    for (int t = 0; t < NUM_THREADS; ++t) {
        threads.emplace_back([shared_obj, &success_count, OPERATIONS_PER_THREAD]() {
            for (int i = 0; i < OPERATIONS_PER_THREAD; ++i) {
                if (cudaUserObjectRetain(shared_obj) == cudaSuccess) {
                    cudaUserObjectRelease(shared_obj, cudaUserObjectNoDestructorSync);
                    success_count++;
                }
            }
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    int expected_operations = NUM_THREADS * OPERATIONS_PER_THREAD;
    ASSERT_EQ(success_count.load(), expected_operations);
    
    ASSERT_EQ(cudaUserObjectRelease(shared_obj, cudaUserObjectNoDestructorSync), cudaSuccess);
}

TEST_F(UserObjectTest, GraphUpdateConceptual) {
    TestUserData* data1 = new TestUserData{1, nullptr, 1024};
    ASSERT_EQ(cudaMalloc(&data1->device_ptr, data1->size * sizeof(float)), cudaSuccess);
    cudaUserObject_t obj1 = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&obj1, data1, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);

    ASSERT_EQ(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    float* d_buffer = nullptr;
    ASSERT_EQ(cudaMalloc(&d_buffer, 1024 * sizeof(float)), cudaSuccess);
    ASSERT_EQ(cudaMemsetAsync(d_buffer, 0, 1024 * sizeof(float), stream), cudaSuccess);
    ASSERT_EQ(cudaStreamEndCapture(stream, &graph), cudaSuccess);
    
    ASSERT_EQ(cudaGraphInstantiate(&exec_graph, graph, nullptr, nullptr, 0), cudaSuccess);
    
    ASSERT_EQ(cudaGraphLaunch(exec_graph, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    TestUserData* data2 = new TestUserData{2, nullptr, 2048};
    ASSERT_EQ(cudaMalloc(&data2->device_ptr, data2->size * sizeof(float)), cudaSuccess);
    cudaUserObject_t obj2 = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&obj2, data2, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);

    cudaGraphExec_t exec_graph2;
    ASSERT_EQ(cudaGraphInstantiate(&exec_graph2, graph, nullptr, nullptr, 0), cudaSuccess);

    ASSERT_EQ(cudaGraphLaunch(exec_graph2, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    ASSERT_EQ(cudaGraphExecDestroy(exec_graph2), cudaSuccess);
    ASSERT_EQ(cudaFree(d_buffer), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(obj1, cudaUserObjectNoDestructorSync), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(obj2, cudaUserObjectNoDestructorSync), cudaSuccess);
}

TEST_F(UserObjectTest, GraphManagesObjectLifetime) {
    // 1. Create a UserObject with a flag to track destruction.
    std::atomic<bool> destroyed_flag(false);
    LifetimeTestUserData* user_data = new LifetimeTestUserData{&destroyed_flag};
    
    cudaUserObject_t user_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, user_data, lifetime_test_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);

    // Create a simple graph to associate the object with.
    ASSERT_EQ(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    // Add a dummy operation to make the graph valid.
    float* d_dummy = nullptr;
    ASSERT_EQ(cudaMalloc(&d_dummy, sizeof(float)), cudaSuccess);
    ASSERT_EQ(cudaFreeAsync(d_dummy, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamEndCapture(stream, &graph), cudaSuccess);

    // 2. Instantiate the graph, passing the UserObject to it.
    // This makes the graph take a reference.
    ASSERT_EQ(cudaGraphInstantiate(&exec_graph, graph, nullptr, nullptr, 0), cudaSuccess);
    // In a real scenario, you would associate the user object with the graph exec.
    // For this test, we simulate the lifetime management by retaining and releasing.
    ASSERT_EQ(cudaUserObjectRetain(user_obj), cudaSuccess); // Simulate graph taking ownership

    // 3. Release the original reference.
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);

    // 4. Assert that the object is NOT yet destroyed.
    ASSERT_FALSE(destroyed_flag.load());

    // 5. Destroy the graph executable (simulating the end of its use).
    // This should release the graph's reference to the object.
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess); // Simulate graph releasing ownership

    // 6. Assert that the destructor has now been called.
    ASSERT_TRUE(destroyed_flag.load());
}

// New test for cudaGraphNodeSetEnabled and cudaGraphNodeGetEnabled
TEST_F(UserObjectTest, NodeEnabledState) {
    cudaGraph_t local_graph;
    ASSERT_EQ(cudaGraphCreate(&local_graph, 0), cudaSuccess);

    cudaGraphNode_t kernel_node;
    cudaKernelNodeParams kernel_params = {0};
    void* kernel_args[] = {};
    
    kernel_params.func = (void*)dummy_kernel;
    kernel_params.gridDim = {1, 1, 1};
    kernel_params.blockDim = {1, 1, 1};
    kernel_params.sharedMemBytes = 0;
    kernel_params.kernelParams = kernel_args;
    kernel_params.extra = nullptr;

    ASSERT_EQ(cudaGraphAddKernelNode(&kernel_node, local_graph, nullptr, 0, &kernel_params), cudaSuccess);

    // Check initial state (should be enabled)
    unsigned int enabled = 0;
    ASSERT_EQ(cudaGraphNodeGetEnabled(kernel_node, &enabled), cudaSuccess);
    ASSERT_EQ(enabled, 1);

    // Disable the node
    ASSERT_EQ(cudaGraphNodeSetEnabled(kernel_node, 0), cudaSuccess);
    
    // Check if disabled
    ASSERT_EQ(cudaGraphNodeGetEnabled(kernel_node, &enabled), cudaSuccess);
    ASSERT_EQ(enabled, 0);

    // Enable the node again
    ASSERT_EQ(cudaGraphNodeSetEnabled(kernel_node, 1), cudaSuccess);

    // Check if enabled
    ASSERT_EQ(cudaGraphNodeGetEnabled(kernel_node, &enabled), cudaSuccess);
    ASSERT_EQ(enabled, 1);

    // Error cases
    ASSERT_EQ(cudaGraphNodeSetEnabled(nullptr, 1), cudaErrorInvalidValue);
    ASSERT_EQ(cudaGraphNodeGetEnabled(nullptr, &enabled), cudaErrorInvalidValue);

    ASSERT_EQ(cudaGraphDestroy(local_graph), cudaSuccess);
}

// Test for error handling of UserObject APIs
TEST_F(UserObjectTest, UserObjectErrorHandling) {
    cudaUserObject_t obj = nullptr;
    TestUserData* user_data = new TestUserData{42, nullptr, 1024};
    
    // cudaUserObjectCreate error cases
    ASSERT_EQ(cudaUserObjectCreate(&obj, user_data, test_user_object_destroy, 0, cudaUserObjectNoDestructorSync), cudaErrorInvalidValue); // Invalid refcount
    ASSERT_EQ(cudaUserObjectCreate(nullptr, user_data, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaErrorInvalidValue); // Null object handle
    
    // Null user_data is ok, but destructor should handle it
    ASSERT_EQ(cudaUserObjectCreate(&obj, nullptr, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(obj, cudaUserObjectNoDestructorSync), cudaSuccess);
    
    delete user_data; // clean up since first create failed

    // cudaUserObjectRetain / cudaUserObjectRelease error cases
    ASSERT_EQ(cudaUserObjectCreate(&obj, nullptr, nullptr, 1, cudaUserObjectNoDestructorSync), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(obj, cudaUserObjectNoDestructorSync), cudaSuccess);
    // Note: After the last release, the handle is invalid.
    ASSERT_EQ(cudaUserObjectRelease(obj, cudaUserObjectNoDestructorSync), cudaErrorInvalidResourceHandle); // Over-release
    ASSERT_EQ(cudaUserObjectRetain(obj), cudaErrorInvalidResourceHandle); // Retain after final release

    // Test with nullptrs
    ASSERT_EQ(cudaUserObjectRetain(nullptr), cudaErrorInvalidValue);
    ASSERT_EQ(cudaUserObjectRelease(nullptr, cudaUserObjectNoDestructorSync), cudaErrorInvalidValue);
}

// Test for graph-level user object management (conceptual)
// These APIs (cudaGraphAddUserObject, cudaGraphReleaseUserObject, cudaGraphRemoveUserObject)
// are not part of the standard public CUDA runtime API. This test is written based on the user's request
// and will likely fail to compile. It serves as a template for what such a test would look like.
TEST_F(UserObjectTest, GraphUserObjectManagementConceptual) {
    // This test is disabled by default because it uses non-public APIs.
    // To enable, you would need to ensure these functions are available in your CUDA environment.
#if 0 // Disabled
    TestUserData* user_data = new TestUserData{123, nullptr, 512};
    cudaUserObject_t user_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, user_data, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);

    cudaGraph_t local_graph;
    ASSERT_EQ(cudaGraphCreate(&local_graph, 0), cudaSuccess);

    // Hypothetical API to associate a user object with a graph
    cudaGraphNode_t user_object_node;
    ASSERT_EQ(cudaGraphAddUserObject(local_graph, user_obj, 0, nullptr, &user_object_node), cudaSuccess);

    // Graph should have a reference now. Releasing the original reference should not destroy the object.
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);

    // Instantiate and run
    cudaGraphExec_t local_exec_graph;
    ASSERT_EQ(cudaGraphInstantiate(&local_exec_graph, local_graph, nullptr, nullptr, 0), cudaSuccess);
    ASSERT_EQ(cudaGraphLaunch(local_exec_graph, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    // Hypothetical APIs to manage the user object's relationship with the graph
    ASSERT_EQ(cudaGraphReleaseUserObject(local_graph, user_obj), cudaSuccess);
    ASSERT_EQ(cudaGraphRemoveUserObject(local_graph, user_obj), cudaSuccess);

    // After removing from graph, the object should be destroyed as its ref count goes to 0.
    // We would need a way to check the destruction flag to confirm this.

    ASSERT_EQ(cudaGraphExecDestroy(local_exec_graph), cudaSuccess);
    ASSERT_EQ(cudaGraphDestroy(local_graph), cudaSuccess);
#endif
    GTEST_SKIP() << "Skipping test for non-public CUDA APIs: cudaGraph(Add/Release/Remove)UserObject";
}

// Stress test for UserObject
TEST_F(UserObjectTest, UserObjectStress) {
    const int NUM_THREADS = 16;
    const int OBJECTS_PER_THREAD = 50;
    std::vector<std::thread> threads;
    std::atomic<int> create_success_count{0};

    for (int t = 0; t < NUM_THREADS; ++t) {
        threads.emplace_back([&]() {
            std::vector<cudaUserObject_t> objects;
            for (int i = 0; i < OBJECTS_PER_THREAD; ++i) {
                TestUserData* data = new TestUserData{t * 100 + i, nullptr, 128};
                cudaUserObject_t obj = nullptr;
                if (cudaUserObjectCreate(&obj, data, test_user_object_destroy, 1, cudaUserObjectNoDestructorSync) == cudaSuccess) {
                    objects.push_back(obj);
                    create_success_count++;
                } else {
                    delete data; // cleanup if create failed
                }
            }
            
            // Randomly retain and release to increase pressure
            if (!objects.empty()) {
                for(int i=0; i<OBJECTS_PER_THREAD * 2; ++i) {
                    int idx = rand() % objects.size();
                    if (cudaUserObjectRetain(objects[idx]) == cudaSuccess) {
                        cudaUserObjectRelease(objects[idx], cudaUserObjectNoDestructorSync);
                    }
                }
            }

            for (auto obj : objects) {
                cudaUserObjectRelease(obj, cudaUserObjectNoDestructorSync);
            }
        });
    }

    for (auto& th : threads) {
        th.join();
    }

    ASSERT_EQ(create_success_count.load(), NUM_THREADS * OBJECTS_PER_THREAD);
}

// Integration test for cudaGraphNodeSetEnabled
TEST_F(UserObjectTest, NodeEnabledIntegration) {
    const size_t SIZE = 1024;
    const int HOST_VALUE = 123;
    
    int* h_src = new int[SIZE];
    int* h_dst = new int[SIZE];
    int* d_src;
    int* d_dst;

    for(size_t i = 0; i < SIZE; ++i) {
        h_src[i] = HOST_VALUE;
        h_dst[i] = 0;
    }

    ASSERT_EQ(cudaMalloc(&d_src, SIZE * sizeof(int)), cudaSuccess);
    ASSERT_EQ(cudaMalloc(&d_dst, SIZE * sizeof(int)), cudaSuccess);
    ASSERT_EQ(cudaMemcpy(d_src, h_src, SIZE * sizeof(int), cudaMemcpyHostToDevice), cudaSuccess);
    ASSERT_EQ(cudaMemcpy(d_dst, h_dst, SIZE * sizeof(int), cudaMemcpyHostToDevice), cudaSuccess);

    cudaGraph_t local_graph;
    ASSERT_EQ(cudaGraphCreate(&local_graph, 0), cudaSuccess);

    cudaGraphNode_t memcpy_node;
    ASSERT_EQ(cudaGraphAddMemcpyNode(&memcpy_node, local_graph, nullptr, 0, d_dst, d_src, SIZE * sizeof(int), cudaMemcpyDeviceToDevice), cudaSuccess);

    // Disable the memcpy node
    ASSERT_EQ(cudaGraphNodeSetEnabled(memcpy_node, 0), cudaSuccess);

    cudaGraphExec_t local_exec_graph;
    ASSERT_EQ(cudaGraphInstantiate(&local_exec_graph, local_graph, nullptr, nullptr, 0), cudaSuccess);
    
    ASSERT_EQ(cudaGraphLaunch(local_exec_graph, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    // Verify that the memory was NOT copied
    ASSERT_EQ(cudaMemcpy(h_dst, d_dst, SIZE * sizeof(int), cudaMemcpyDeviceToHost), cudaSuccess);
    for(size_t i = 0; i < SIZE; ++i) {
        ASSERT_EQ(h_dst[i], 0);
    }

    // Now, enable the node and re-run
    ASSERT_EQ(cudaGraphNodeSetEnabled(memcpy_node, 1), cudaSuccess);
    // Re-instantiate the graph because the enabled state is part of the executable's definition
    cudaGraphExec_t local_exec_graph_2;
    ASSERT_EQ(cudaGraphInstantiate(&local_exec_graph_2, local_graph, nullptr, nullptr, 0), cudaSuccess);

    ASSERT_EQ(cudaGraphLaunch(local_exec_graph_2, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    // Verify that the memory WAS copied this time
    ASSERT_EQ(cudaMemcpy(h_dst, d_dst, SIZE * sizeof(int), cudaMemcpyDeviceToHost), cudaSuccess);
    for(size_t i = 0; i < SIZE; ++i) {
        ASSERT_EQ(h_dst[i], HOST_VALUE);
    }

    delete[] h_src;
    delete[] h_dst;
    ASSERT_EQ(cudaFree(d_src), cudaSuccess);
    ASSERT_EQ(cudaFree(d_dst), cudaSuccess);
    ASSERT_EQ(cudaGraphExecDestroy(local_exec_graph), cudaSuccess);
    ASSERT_EQ(cudaGraphExecDestroy(local_exec_graph_2), cudaSuccess);
    ASSERT_EQ(cudaGraphDestroy(local_graph), cudaSuccess);
}

// Test for cudaUserObjectDestructorSync flag
TEST_F(UserObjectTest, DestructorSyncFlag) {
    std::atomic<bool> destroyed_flag(false);
    LifetimeTestUserData* user_data = new LifetimeTestUserData{&destroyed_flag};
    
    cudaUserObject_t user_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, user_data, lifetime_test_destroy, 1, cudaUserObjectDestructorSync), cudaSuccess);

    // The final release should block until the destructor is complete.
    // Since there are no outstanding operations, this should be immediate,
    // but it guarantees completion before the function returns.
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectDestructorSync), cudaSuccess);

    // The flag must be true immediately after the release call returns.
    ASSERT_TRUE(destroyed_flag.load());
}

// Test creating a user object with a null destructor
TEST_F(UserObjectTest, NullDestructor) {
    TestUserData* user_data = new TestUserData{1, nullptr, 0};
    cudaUserObject_t user_obj = nullptr;
    
    // This should succeed. The runtime will simply not call a destructor.
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, user_data, nullptr, 1, cudaUserObjectNoDestructorSync), cudaSuccess);
    
    ASSERT_EQ(cudaUserObjectRetain(user_obj), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);

    // We must clean up the user_data manually since there was no destructor
    delete user_data;
}

// Test sharing a single UserObject across multiple graphs
TEST_F(UserObjectTest, SharedObjectAcrossGraphs) {
    std::atomic<bool> destroyed_flag(false);
    LifetimeTestUserData* user_data = new LifetimeTestUserData{&destroyed_flag};
    
    cudaUserObject_t user_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, user_data, lifetime_test_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);

    // Create first graph and exec
    cudaGraph_t graph1;
    cudaGraphExec_t exec1;
    ASSERT_EQ(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    ASSERT_EQ(cudaEventRecord(nullptr, stream), cudaSuccess); // Dummy op
    ASSERT_EQ(cudaStreamEndCapture(stream, &graph1), cudaSuccess);
    ASSERT_EQ(cudaGraphInstantiate(&exec1, graph1, nullptr, nullptr, 0), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRetain(user_obj), cudaSuccess); // Graph 1 takes a reference

    // Create second graph and exec
    cudaGraph_t graph2;
    cudaGraphExec_t exec2;
    ASSERT_EQ(cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal), cudaSuccess);
    ASSERT_EQ(cudaEventRecord(nullptr, stream), cudaSuccess); // Dummy op
    ASSERT_EQ(cudaStreamEndCapture(stream, &graph2), cudaSuccess);
    ASSERT_EQ(cudaGraphInstantiate(&exec2, graph2, nullptr, nullptr, 0), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRetain(user_obj), cudaSuccess); // Graph 2 takes a reference

    // Release original reference. Object should not be destroyed.
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);
    ASSERT_FALSE(destroyed_flag.load());

    // Destroy first graph exec. Object should not be destroyed.
    ASSERT_EQ(cudaGraphExecDestroy(exec1), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess); // Release for graph 1
    ASSERT_FALSE(destroyed_flag.load());

    // Destroy second graph exec. Object should now be destroyed.
    ASSERT_EQ(cudaGraphExecDestroy(exec2), cudaSuccess);
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess); // Release for graph 2
    ASSERT_TRUE(destroyed_flag.load());

    ASSERT_EQ(cudaGraphDestroy(graph1), cudaSuccess);
    ASSERT_EQ(cudaGraphDestroy(graph2), cudaSuccess);
}

// Kernel for integration tests
__global__ void set_value_kernel(int* ptr, int value) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        *ptr = value;
    }
}

// Test disabling a node that other nodes depend on
TEST_F(UserObjectTest, DisabledNodeWithDependencies) {
    int* d_a, *d_b;
    int h_b = 0;
    ASSERT_EQ(cudaMalloc(&d_a, sizeof(int)), cudaSuccess);
    ASSERT_EQ(cudaMalloc(&d_b, sizeof(int)), cudaSuccess);
    ASSERT_EQ(cudaMemset(d_a, 0, sizeof(int)), cudaSuccess);
    ASSERT_EQ(cudaMemset(d_b, 0, sizeof(int)), cudaSuccess);

    cudaGraph_t local_graph;
    ASSERT_EQ(cudaGraphCreate(&local_graph, 0), cudaSuccess);

    // Node A: Kernel that sets d_a to 1
    cudaGraphNode_t node_a;
    cudaKernelNodeParams params_a = {0};
    int val_a = 1;
    void* args_a[] = {&d_a, &val_a};
    params_a.func = (void*)set_value_kernel;
    params_a.gridDim = {1,1,1};
    params_a.blockDim = {1,1,1};
    params_a.kernelParams = args_a;
    ASSERT_EQ(cudaGraphAddKernelNode(&node_a, local_graph, nullptr, 0, &params_a), cudaSuccess);

    // Node B: Memcpy from d_a to d_b, dependent on Node A
    cudaGraphNode_t node_b;
    ASSERT_EQ(cudaGraphAddMemcpyNode(&node_b, local_graph, &node_a, 1, d_b, d_a, sizeof(int), cudaMemcpyDeviceToDevice), cudaSuccess);

    // Disable Node A
    ASSERT_EQ(cudaGraphNodeSetEnabled(node_a, 0), cudaSuccess);

    cudaGraphExec_t local_exec;
    ASSERT_EQ(cudaGraphInstantiate(&local_exec, local_graph, nullptr, nullptr, 0), cudaSuccess);
    ASSERT_EQ(cudaGraphLaunch(local_exec, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    // Verify that d_b is still 0, because Node A was disabled, so Node B should not have run.
    ASSERT_EQ(cudaMemcpy(&h_b, d_b, sizeof(int), cudaMemcpyDeviceToHost), cudaSuccess);
    ASSERT_EQ(h_b, 0);

    ASSERT_EQ(cudaFree(d_a), cudaSuccess);
    ASSERT_EQ(cudaFree(d_b), cudaSuccess);
    ASSERT_EQ(cudaGraphExecDestroy(local_exec), cudaSuccess);
    ASSERT_EQ(cudaGraphDestroy(local_graph), cudaSuccess);
}

// Test disabling a kernel node specifically
TEST_F(UserObjectTest, DisabledKernelNodeIntegration) {
    int* d_ptr;
    int h_val = 0;
    const int KERNEL_VAL = 555;
    ASSERT_EQ(cudaMalloc(&d_ptr, sizeof(int)), cudaSuccess);
    ASSERT_EQ(cudaMemset(d_ptr, 0, sizeof(int)), cudaSuccess);

    cudaGraph_t local_graph;
    ASSERT_EQ(cudaGraphCreate(&local_graph, 0), cudaSuccess);

    cudaGraphNode_t kernel_node;
    cudaKernelNodeParams params = {0};
    int val = KERNEL_VAL;
    void* args[] = {&d_ptr, &val};
    params.func = (void*)set_value_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    params.kernelParams = args;
    ASSERT_EQ(cudaGraphAddKernelNode(&kernel_node, local_graph, nullptr, 0, &params), cudaSuccess);

    // Disable the kernel node
    ASSERT_EQ(cudaGraphNodeSetEnabled(kernel_node, 0), cudaSuccess);

    cudaGraphExec_t local_exec;
    ASSERT_EQ(cudaGraphInstantiate(&local_exec, local_graph, nullptr, nullptr, 0), cudaSuccess);
    ASSERT_EQ(cudaGraphLaunch(local_exec, stream), cudaSuccess);
    ASSERT_EQ(cudaStreamSynchronize(stream), cudaSuccess);

    // Verify the kernel did not run
    ASSERT_EQ(cudaMemcpy(&h_val, d_ptr, sizeof(int), cudaMemcpyDeviceToHost), cudaSuccess);
    ASSERT_EQ(h_val, 0);

    ASSERT_EQ(cudaFree(d_ptr), cudaSuccess);
    ASSERT_EQ(cudaGraphExecDestroy(local_exec), cudaSuccess);
    ASSERT_EQ(cudaGraphDestroy(local_graph), cudaSuccess);
}

// Test to verify asynchronous destruction behavior
TEST_F(UserObjectTest, AsynchronousDestruction) {
    std::atomic<bool> destroyed_flag(false);
    LifetimeTestUserData* user_data = new LifetimeTestUserData{&destroyed_flag};
    
    cudaUserObject_t user_obj = nullptr;
    ASSERT_EQ(cudaUserObjectCreate(&user_obj, user_data, lifetime_test_destroy, 1, cudaUserObjectNoDestructorSync), cudaSuccess);

    // Launch a long-running kernel on the stream
    cudaStream_t local_stream;
    ASSERT_EQ(cudaStreamCreate(&local_stream), cudaSuccess);
    void* kernel_args[] = {};
    cudaLaunchKernel((void*)dummy_kernel, dim3(1), dim3(1), kernel_args, 0, local_stream);
    
    // Immediately release the object. The destructor should not run yet
    // because the kernel is still "in flight" on the stream.
    ASSERT_EQ(cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync), cudaSuccess);
    ASSERT_FALSE(destroyed_flag.load());

    // Now, synchronize the stream. This ensures all previous work is done.
    ASSERT_EQ(cudaStreamSynchronize(local_stream), cudaSuccess);

    // After synchronization, the destructor should have been called.
    // We add a small sleep to give the callback thread time to execute.
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    ASSERT_TRUE(destroyed_flag.load());

    ASSERT_EQ(cudaStreamDestroy(local_stream), cudaSuccess);
}
