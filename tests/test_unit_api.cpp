#include <gtest/gtest.h>
#include <cuda_runtime.h>

#define CUDA_CHECK(err) \
    do { \
        cudaError_t err_ = (err); \
        if (err_ != cudaSuccess) { \
            FAIL() << "CUDA error: " << cudaGetErrorString(err_) << " at " << __FILE__ << ":" << __LINE__; \
        } \
    } while(0)

// Test fixture for API tests
class ApiTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Calling cudaFree(nullptr) is a standard, lightweight way to initialize the
        // CUDA context and check for any existing sticky errors on the current thread
        // before any test logic begins.
        CUDA_CHECK(cudaFree(nullptr));
    }
};

TEST_F(ApiTest, GraphCreationAndDestruction) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    ASSERT_NE(graph, nullptr);
    CUDA_CHECK(cudaGraphDestroy(graph));
}

// --- DUMMY KERNEL ---
__global__ void dummy_kernel() {}
// --- END DUMMY ---

TEST_F(ApiTest, AddKernelNode) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    cudaGraphNode_t kernelNode;
    cudaKernelNodeParams kernelParams = {0};
    kernelParams.func = (void*)dummy_kernel;
    kernelParams.gridDim = {1, 1, 1};
    kernelParams.blockDim = {1, 1, 1};
    kernelParams.sharedMemBytes = 0;
    kernelParams.kernelParams = nullptr;
    kernelParams.extra = nullptr;

    CUDA_CHECK(cudaGraphAddKernelNode(&kernelNode, graph, nullptr, 0, &kernelParams));
    ASSERT_NE(kernelNode, nullptr);
    
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(ApiTest, AddMemcpyNode) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    char* d_src;
    char* d_dst;
    CUDA_CHECK(cudaMalloc(&d_src, 128));
    CUDA_CHECK(cudaMalloc(&d_dst, 128));

    cudaGraphNode_t memcpyNode;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpyNode, graph, nullptr, 0, d_dst, d_src, 128, cudaMemcpyDeviceToDevice));
    ASSERT_NE(memcpyNode, nullptr);

    CUDA_CHECK(cudaFree(d_src));
    CUDA_CHECK(cudaFree(d_dst));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(ApiTest, AddMemcpyNode2D) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    const size_t width = 128;
    const size_t height = 128;
    char* d_src;
    char* d_dst;
    size_t pitch;

    CUDA_CHECK(cudaMallocPitch(&d_src, &pitch, width, height));
    CUDA_CHECK(cudaMallocPitch(&d_dst, &pitch, width, height));

    cudaGraphNode_t memcpyNode;
    CUDA_CHECK(cudaGraphAddMemcpyNode2D(&memcpyNode, graph, nullptr, 0, d_dst, pitch, d_src, pitch, width, height, cudaMemcpyDeviceToDevice));
    ASSERT_NE(memcpyNode, nullptr);

    CUDA_CHECK(cudaFree(d_src));
    CUDA_CHECK(cudaFree(d_dst));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(ApiTest, AddMemsetNode) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    char* d_ptr;
    CUDA_CHECK(cudaMalloc(&d_ptr, 128));

    cudaGraphNode_t memsetNode;
    cudaMemsetParams memsetParams = {0};
    memsetParams.dst = d_ptr;
    memsetParams.value = 0;
    memsetParams.pitch = 0;
    memsetParams.elementSize = sizeof(char);
    memsetParams.width = 128;
    memsetParams.height = 1;

    CUDA_CHECK(cudaGraphAddMemsetNode(&memsetNode, graph, nullptr, 0, &memsetParams));
    ASSERT_NE(memsetNode, nullptr);

    CUDA_CHECK(cudaFree(d_ptr));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(ApiTest, CloneGraph) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));
    
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {};
    params.func = (void*)dummy_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph, nullptr, 0, &params));

    cudaGraph_t cloned_graph;
    CUDA_CHECK(cudaGraphClone(&cloned_graph, graph));
    ASSERT_NE(cloned_graph, nullptr);
    ASSERT_NE(cloned_graph, graph);

    // Verify the cloned graph has nodes
    size_t num_nodes = 0;
    CUDA_CHECK(cudaGraphGetNodes(cloned_graph, nullptr, &num_nodes));
    ASSERT_EQ(num_nodes, 1);

    CUDA_CHECK(cudaGraphDestroy(graph));
    CUDA_CHECK(cudaGraphDestroy(cloned_graph));
}

TEST_F(ApiTest, AddChildGraphNode) {
    // 1. Create the child graph
    cudaGraph_t child_graph;
    CUDA_CHECK(cudaGraphCreate(&child_graph, 0));
    cudaGraphNode_t child_node;
    cudaKernelNodeParams params = {};
    params.func = (void*)dummy_kernel;
    params.gridDim = {1,1,1};
    params.blockDim = {1,1,1};
    CUDA_CHECK(cudaGraphAddKernelNode(&child_node, child_graph, nullptr, 0, &params));
    
    // 2. Create the parent graph
    cudaGraph_t parent_graph;
    CUDA_CHECK(cudaGraphCreate(&parent_graph, 0));

    // 3. Add the child graph as a node in the parent graph
    cudaGraphNode_t child_graph_node;
    CUDA_CHECK(cudaGraphAddChildGraphNode(&child_graph_node, parent_graph, nullptr, 0, child_graph));
    ASSERT_NE(child_graph_node, nullptr);

    // 4. Verify parent graph has one node (the child graph)
    size_t num_nodes = 0;
    CUDA_CHECK(cudaGraphGetNodes(parent_graph, nullptr, &num_nodes));
    ASSERT_EQ(num_nodes, 1);

    CUDA_CHECK(cudaGraphDestroy(parent_graph));
    CUDA_CHECK(cudaGraphDestroy(child_graph));
}

void CUDART_CB host_callback(void* data) {
    bool* flag = static_cast<bool*>(data);
    *flag = true;
}

TEST_F(ApiTest, AddHostNode) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    bool host_flag = false;
    cudaHostNodeParams host_params = {};
    host_params.fn = host_callback;
    host_params.userData = &host_flag;

    cudaGraphNode_t host_node;
    CUDA_CHECK(cudaGraphAddHostNode(&host_node, graph, nullptr, 0, &host_params));
    ASSERT_NE(host_node, nullptr);
    
    cudaGraphExec_t exec;
    CUDA_CHECK(cudaGraphInstantiate(&exec, graph, nullptr, nullptr, 0));
    
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(exec, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));

    ASSERT_TRUE(host_flag);

    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(exec));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(ApiTest, AddEventNodes) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    cudaEvent_t event;
    CUDA_CHECK(cudaEventCreate(&event));

    cudaGraphNode_t record_node;
    CUDA_CHECK(cudaGraphAddEventRecordNode(&record_node, graph, nullptr, 0, event));
    ASSERT_NE(record_node, nullptr);

    cudaGraphNode_t wait_node;
    CUDA_CHECK(cudaGraphAddEventWaitNode(&wait_node, graph, &record_node, 1, event));
    ASSERT_NE(wait_node, nullptr);

    size_t num_deps = 0;
    CUDA_CHECK(cudaGraphNodeGetDependencies(wait_node, nullptr, &num_deps));
    ASSERT_EQ(num_deps, 1);

    cudaGraphNode_t read_deps[1];
    CUDA_CHECK(cudaGraphNodeGetDependencies(wait_node, read_deps, &num_deps));
    ASSERT_EQ(read_deps[0], record_node);

    CUDA_CHECK(cudaEventDestroy(event));
    CUDA_CHECK(cudaGraphDestroy(graph));
}

TEST_F(ApiTest, AddEmptyNode) {
    cudaGraph_t graph;
    CUDA_CHECK(cudaGraphCreate(&graph, 0));

    cudaGraphNode_t empty_node;
    CUDA_CHECK(cudaGraphAddEmptyNode(&empty_node, graph, nullptr, 0));
    ASSERT_NE(empty_node, nullptr);

    // Verify the graph has one node
    size_t num_nodes = 0;
    CUDA_CHECK(cudaGraphGetNodes(graph, nullptr, &num_nodes));
    ASSERT_EQ(num_nodes, 1);

    CUDA_CHECK(cudaGraphDestroy(graph));
}
