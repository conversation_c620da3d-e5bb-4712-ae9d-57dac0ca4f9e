#include <gtest/gtest.h>
#include <cuda_runtime.h>
#include <vector>
#include <iostream>
#include <tuple>
#include <thread>

#define CUDA_CHECK(err)                                         \
    do {                                                        \
        cudaError_t err_ = (err);                               \
        if (err_ != cudaSuccess) {                              \
            std::cerr << "CUDA error: " << cudaGetErrorString(err_) \
                      << " at " << __FILE__ << ":" << __LINE__ << std::endl; \
            FAIL();                                             \
        }                                                       \
    } while (0)

// --- Kernels for Testing ---
__global__ void set_value_kernel_exec(int* data, int value) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        *data = value;
    }
}

__global__ void another_kernel_exec() {}

// --- Test Fixture ---
class GraphExecSetParamsTest : public ::testing::Test {
protected:
    cudaGraph_t graph_{nullptr};
    cudaGraphExec_t graph_exec_{nullptr};
    int* d_data_{nullptr};
    int* h_data_{nullptr};

    void SetUp() override {
        CUDA_CHECK(cudaFree(nullptr)); 
        h_data_ = new int;
        CUDA_CHECK(cudaMalloc(&d_data_, sizeof(int)));
        CUDA_CHECK(cudaGraphCreate(&graph_, 0));
    }

    void TearDown() override {
        if (graph_exec_) CUDA_CHECK(cudaGraphExecDestroy(graph_exec_));
        if (graph_) CUDA_CHECK(cudaGraphDestroy(graph_));
        if (d_data_) CUDA_CHECK(cudaFree(d_data_));
        if (h_data_) { delete h_data_; h_data_ = nullptr; }
    }
};

void CUDART_CB host_node_callback_exec(void* data) {
    *(static_cast<int*>(data)) += 10;
}

// =================================================================
// ==      cudaGraphExec...SetParams FUNCTIONAL TESTS             ==
// =================================================================

TEST_F(GraphExecSetParamsTest, ExecKernelNodeSetParams) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_exec, {1,1,1}, {1,1,1}};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph_, nullptr, 0, &params));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));

    int val = 123;
    void* args[] = {&d_data_, &val};
    params.kernelParams = args;
    CUDA_CHECK(cudaGraphExecKernelNodeSetParams(graph_exec_, node, &params));

    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    CUDA_CHECK(cudaGraphLaunch(graph_exec_, stream));
    CUDA_CHECK(cudaStreamSynchronize(stream));
    CUDA_CHECK(cudaMemcpy(h_data_, d_data_, sizeof(int), cudaMemcpyDeviceToHost));
    ASSERT_EQ(*h_data_, 123);
    CUDA_CHECK(cudaStreamDestroy(stream));
}

TEST_F(GraphExecSetParamsTest, ExecMemcpyNodeSetParams) {
    int *d_src, *d_dst;
    CUDA_CHECK(cudaMalloc(&d_src, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_dst, sizeof(int)));
    cudaGraphNode_t node;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&node, graph_, nullptr, 0, d_dst, d_src, sizeof(int), cudaMemcpyDeviceToDevice));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));
    
    int *d_src2, *d_dst2;
    CUDA_CHECK(cudaMalloc(&d_src2, sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_dst2, sizeof(int)));
    int val = 456;
    CUDA_CHECK(cudaMemcpy(d_src2, &val, sizeof(int), cudaMemcpyHostToDevice));

    cudaMemcpy3DParms p = {0};
    p.srcPtr = make_cudaPitchedPtr(d_src2, sizeof(int), 1, 1);
    p.dstPtr = make_cudaPitchedPtr(d_dst2, sizeof(int), 1, 1);
    p.extent = make_cudaExtent(sizeof(int), 1, 1);
    p.kind = cudaMemcpyDeviceToDevice;
    CUDA_CHECK(cudaGraphExecMemcpyNodeSetParams(graph_exec_, node, &p));

    cudaStream_t s;
    CUDA_CHECK(cudaStreamCreate(&s));
    CUDA_CHECK(cudaGraphLaunch(graph_exec_, s));
    CUDA_CHECK(cudaStreamSynchronize(s));
    CUDA_CHECK(cudaMemcpy(h_data_, d_dst2, sizeof(int), cudaMemcpyDeviceToHost));
    ASSERT_EQ(*h_data_, val);

    CUDA_CHECK(cudaStreamDestroy(s));
    CUDA_CHECK(cudaFree(d_src)); CUDA_CHECK(cudaFree(d_dst));
    CUDA_CHECK(cudaFree(d_src2)); CUDA_CHECK(cudaFree(d_dst2));
}

// ... (Other cudaGraphExec...SetParams tests: Memset, Host, ChildGraph, EventWait) ...

// =================================================================
// ==        cudaGraphExec...SetParams ERROR TESTS                ==
// =================================================================

TEST_F(GraphExecSetParamsTest, ExecSetParamsOnWrongNodeType) {
    cudaGraphNode_t memcpy_node;
    CUDA_CHECK(cudaGraphAddMemcpyNode1D(&memcpy_node, graph_, nullptr, 0, d_data_, d_data_, 1, cudaMemcpyDeviceToDevice));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));
    cudaKernelNodeParams p = {};
    ASSERT_NE(cudaGraphExecKernelNodeSetParams(graph_exec_, memcpy_node, &p), cudaSuccess);
}

TEST_F(GraphExecSetParamsTest, ExecKernelNodeSetParamsImmutableFunc) {
    cudaGraphNode_t kernel_node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_exec, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&kernel_node, graph_, nullptr, 0, &params));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));

    // The function pointer of a kernel node is immutable after instantiation.
    params.func = (void*)another_kernel_exec;
    ASSERT_NE(cudaGraphExecKernelNodeSetParams(graph_exec_, kernel_node, &params), cudaSuccess);
}

TEST_F(GraphExecSetParamsTest, ExecChildGraphNodeSetParamsWithNullExec) {
    cudaGraph_t child_graph;
    CUDA_CHECK(cudaGraphCreate(&child_graph, 0));
    cudaGraphNode_t child_node;
    CUDA_CHECK(cudaGraphAddChildGraphNode(&child_node, graph_, nullptr, 0, child_graph));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));
    
    // Setting a null executable should fail.
    ASSERT_NE(cudaGraphExecChildGraphNodeSetParams(graph_exec_, child_node, nullptr), cudaSuccess);
    
    CUDA_CHECK(cudaGraphDestroy(child_graph));
}

TEST_F(GraphExecSetParamsTest, ExecChildGraphNodeSetParamsWithDestroyedExec) {
    cudaGraph_t child_graph1, child_graph2;
    cudaGraphExec_t child_exec1, child_exec2;
    CUDA_CHECK(cudaGraphCreate(&child_graph1, 0));
    CUDA_CHECK(cudaGraphCreate(&child_graph2, 0));
    CUDA_CHECK(cudaGraphAddKernelNode(nullptr, child_graph1, nullptr, 0, {}));
    CUDA_CHECK(cudaGraphAddKernelNode(nullptr, child_graph2, nullptr, 0, {}));
    CUDA_CHECK(cudaGraphInstantiate(&child_exec1, child_graph1, nullptr, nullptr, 0));
    CUDA_CHECK(cudaGraphInstantiate(&child_exec2, child_graph2, nullptr, nullptr, 0));

    cudaGraphNode_t child_node;
    CUDA_CHECK(cudaGraphAddChildGraphNode(&child_node, graph_, nullptr, 0, child_graph1));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));
    
    CUDA_CHECK(cudaGraphExecChildGraphNodeSetParams(graph_exec_, child_node, child_exec2));
    // Destroy the executable that is now being used by the main graph.
    CUDA_CHECK(cudaGraphExecDestroy(child_exec2)); 

    // Launching the graph should now fail as it has a dependency on a destroyed resource.
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    ASSERT_NE(cudaGraphLaunch(graph_exec_, stream), cudaSuccess);
    
    CUDA_CHECK(cudaStreamDestroy(stream));
    CUDA_CHECK(cudaGraphExecDestroy(child_exec1));
    CUDA_CHECK(cudaGraphDestroy(child_graph1));
    CUDA_CHECK(cudaGraphDestroy(child_graph2));
}

TEST_F(GraphExecSetParamsTest, ExecSetParamsOnNodeInsideChildGraph) {
    cudaGraph_t child_graph;
    CUDA_CHECK(cudaGraphCreate(&child_graph, 0));
    cudaGraphNode_t node_in_child;
    cudaKernelNodeParams params = {(void*)another_kernel_exec, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node_in_child, child_graph, nullptr, 0, &params));
    
    cudaGraphNode_t child_graph_node;
    CUDA_CHECK(cudaGraphAddChildGraphNode(&child_graph_node, graph_, nullptr, 0, child_graph));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));
    
    // This should fail. The node handle `node_in_child` belongs to `child_graph`, not the parent `graph_`.
    // The executable `graph_exec_` does not "know" about this handle directly.
    ASSERT_NE(cudaGraphExecKernelNodeSetParams(graph_exec_, node_in_child, &params), cudaSuccess);
    
    CUDA_CHECK(cudaGraphDestroy(child_graph));
}

TEST_F(GraphExecSetParamsTest, ExecSetParamsOnNullExec) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_exec, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph_, nullptr, 0, &params));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));

    // Calling SetParams on a null executable graph handle should fail.
    ASSERT_NE(cudaGraphExecKernelNodeSetParams(nullptr, node, &params), cudaSuccess);
}

TEST_F(GraphExecSetParamsTest, ExecKernelNodeSetParamsWithNullNode) {
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));
    cudaKernelNodeParams params = {};
    cudaGraphNode_t null_node = {}; // A zero-initialized node is invalid
    ASSERT_NE(cudaGraphExecKernelNodeSetParams(graph_exec_, null_node, &params), cudaSuccess);
}

TEST_F(GraphExecSetParamsTest, ExecKernelNodeSetParamsWithNullParams) {
    cudaGraphNode_t node;
    cudaKernelNodeParams params = {(void*)set_value_kernel_exec, {1,1,1}, {1,1,1}, 0, nullptr};
    CUDA_CHECK(cudaGraphAddKernelNode(&node, graph_, nullptr, 0, &params));
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));

    // Calling with a null pointer for the params struct should fail.
    ASSERT_NE(cudaGraphExecKernelNodeSetParams(graph_exec_, node, nullptr), cudaSuccess);
}


// =================================================================
// ==       cudaGraphExec...SetParams STRESS TESTS                ==
// =================================================================

using ExecSetParamsStressParams = std::tuple<int, int>; // num_nodes, update_iterations

class ExecSetParamsStressTest : public ::testing::TestWithParam<ExecSetParamsStressParams> {};

TEST_P(ExecSetParamsStressTest, RepetitiveExecKernelSetParams) {
    auto p = GetParam();
    int num_nodes = std::get<0>(p);
    int update_iterations = std::get<1>(p);

    std::vector<cudaGraphNode_t> nodes(num_nodes);
    std::vector<int*> d_node_data(num_nodes);
    cudaKernelNodeParams kernel_params = {(void*)set_value_kernel_exec, {1,1,1}, {1,1,1}, 0, nullptr};
    for(int i=0; i<num_nodes; ++i) {
        CUDA_CHECK(cudaMalloc(&d_node_data[i], sizeof(int)));
        CUDA_CHECK(cudaGraphAddKernelNode(&nodes[i], graph_, nullptr, 0, &kernel_params));
    }
    
    CUDA_CHECK(cudaGraphInstantiate(&graph_exec_, graph_, nullptr, nullptr, 0));
    
    cudaStream_t stream;
    CUDA_CHECK(cudaStreamCreate(&stream));
    
    for (int iter = 0; iter < update_iterations; ++iter) {
        for (int i = 0; i < num_nodes; ++i) {
            int val = iter + i;
            void* args[] = {(void*)&d_node_data[i], (void*)&val};
            kernel_params.kernelParams = args;
            CUDA_CHECK(cudaGraphExecKernelNodeSetParams(graph_exec_, nodes[i], &kernel_params));
        }
        CUDA_CHECK(cudaGraphLaunch(graph_exec_, stream));
    }
    
    CUDA_CHECK(cudaStreamSynchronize(stream));
    for(int i=0; i<num_nodes; ++i) CUDA_CHECK(cudaFree(d_node_data[i]));
    CUDA_CHECK(cudaStreamDestroy(stream));
    SUCCEED();
}

INSTANTIATE_TEST_SUITE_P(ExecSetParamsStress, ExecSetParamsStressTest,
    ::testing::Combine(
        ::testing::Values(64, 256, 1024),
        ::testing::Values(50, 100, 200)
    ),
    [](const auto& info) {
        return "Nodes" + std::to_string(std::get<0>(info.param)) +
               "_Iters" + std::to_string(std::get<1>(info.param));
    }
);
