# 自研 Graph Runtime 详细性能评测方案

## 1. 核心目标

对自研 Graph Runtime 的生命周期进行阶段性拆解，并对每个阶段进行精确的性能量化，以识别潜在的性能瓶颈、评估优化效果，并与 CUDA 等现有实现进行对标。

## 2. Graph Runtime 关键阶段拆解

一个典型的 Graph Runtime 执行过程可分为以下五个核心阶段，我们将对每个阶段进行独立测量：

*   **阶段一：图构建 (Graph Construction)**
    *   **定义**: 指用户通过调用 API（例如 `add_node`, `set_dependency`）来定义计算图的拓扑结构的过程。
    *   **测量指标**: CPU 壁钟时间 (Wall-clock time)。
    *   **性能关注点**: 此阶段通常在 CPU 上完成，其开销应尽可能低，以保证良好的用户体验。

*   **阶段二：图实例化/编译 (Graph Instantiation/Compilation)**
    *   **定义**: Runtime 将用户定义的逻辑图转化为硬件可执行格式的过程。这通常包括验证、优化（如节点融合、内存优化）、资源分配和代码编译等步骤。
    *   **测量指标**: CPU 壁钟时间。
    *   **性能关注点**: 这是整个流程中最耗时的步骤之一。其性能直接影响应用的“首次执行”延迟。

*   **阶段三：参数更新 (Parameter Update)**
    *   **定义**: 在不改变图拓扑结构的前提下，更新图中某个节点的参数（例如，修改核函数的输入/输出数据指针或某些标量值）。
    *   **测量指标**: CPU 壁钟时间。
    *   **性能关注点**: 这是为动态应用设计的核心功能。此操作的开销必须极低，否则会失去相比于“重新构建并实例化图”的优势。

*   **阶段四：图启动 (Graph Launch)**
    *   **定义**: 将一个已实例化的图提交到 GPU 执行队列的 API 调用过程。
    *   **测量指标**: CPU 壁钟时间。
    *   **性能关注点**: 这是重复执行路径上的核心操作（“hot path”）。其 CPU 开销必须达到微秒（us）级别。

*   **阶段五：图执行 (Graph Execution)**
    *   **定义**: GPU 实际执行整个计算图所有任务所花费的时间。
    *   **测量指标**: GPU 设备时间 (Device time)。
    *   **性能关注点**: 这反映了 Runtime 优化后，工作流在硬件上的实际执行效率。

## 3. 各阶段性能测量技术

*   **CPU 密集型阶段 (构建, 实例化, 更新, 启动)**:
    *   **工具**: C++ `std::chrono::high_resolution_clock`。
    *   **方法**: 在调用相应阶段的 API 前后记录时间戳，并计算差值。为获得稳定结果，可将 API 调用置于循环中，然后取平均值。

*   **GPU 密集型阶段 (执行)**:
    *   **工具**: 目标平台提供的原生 GPU 事件/时间戳 API（功能对标 `cudaEvent_t`）。
    *   **方法**: 在 `Launch` API 调用前后，向 GPU 命令流中插入 `start` 和 `stop` 事件，并使用平台 API 查询两个事件间的精确时间差。

## 4. 测试用例设计

为了全面评估，建议构造以下不同类型的计算图来进行测试：

*   **宽图 (Wide Graph)**: 包含大量可以并行执行的独立节点。
    *   **目的**: 重点评估 **图启动 (Launch)** 阶段的 CPU 开销，以及 Runtime 对大规模并行任务的调度能力。
*   **深图 (Deep Graph)**: 包含一条长长的、前后依赖的节点链。
    *   **目的**: 重点评估 **图实例化 (Instantiation)** 阶段对依赖分析的效率，以及 **图执行 (Execution)** 阶段因优化而减少的依赖间隙。
*   **混合图 (Complex Graph)**: 结合了并行和依赖，结构复杂。
    *   **目的**: 模拟真实应用的复杂场景，综合评估 Runtime 的各项能力。
*   **可更新图 (Updatable Graph)**: 包含需要频繁更新参数的节点。
    *   **目的**: 重点评估 **参数更新 (Update)** 阶段的开销，并与“重新实例化”的开销进行对比。

## 5. 实施建议

建议创建一个专用的微基准测试框架（Micro-benchmark Framework），其中每个测试都精确地隔离并测量上述一个阶段的性能。测试结果应以清晰的表格形式输出，便于对比和分析。
