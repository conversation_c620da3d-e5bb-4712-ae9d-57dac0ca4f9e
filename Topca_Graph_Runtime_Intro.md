# Topca Graph Runtime 权威指南

## 1. 概述

Topca Graph Runtime 是一个高性能的 CUDA® 工作流管理系统，其 API 设计与 NVIDIA® CUDA® Graph 高度兼容。它通过将一系列连续的 CUDA 操作（如内核启动、内存拷贝）封装成一个单一的、可重用的图对象，从根本上改变了与 GPU 的交互方式。

### 1.1 为什么使用图？

在传统的 CUDA 流模型中，每次启动一个内核，CPU 驱动程序都必须执行一系列设置和验证操作。对于执行时间很短的内核，这种 CPU 开销可能占到端到端执行时间的很大一部分。

Topca Graph Runtime 将工作提交分为三个阶段：**定义**、**实例化**和**执行**。

1.  **定义 (Definition)**: 您只需定义一次工作流的拓扑结构（操作和它们之间的依赖关系）。
2.  **实例化 (Instantiation)**: 运行时接收这个拓扑结构，进行一次性的全面验证和优化，并生成一个高度优化的可执行图。大部分前期设置成本都在此阶段支付。
3.  **执行 (Execution)**: 以极低的 CPU 开销重复启动这个可执行图。

这种模型不仅**显著降低了 CPU 的启动开销**，还使整个工作流对 CUDA 系统可见，从而实现了在流模型中不可能实现的**全局优化**。

## 2. 图的结构

一个图是一个**有向无环图 (DAG)**，由节点 (Node) 和边 (Edge) 组成。

*   **节点 (Nodes)**: 代表一个操作。
*   **边 (Edges)**: 代表操作之间的依赖关系。一个节点只有在其所有依赖的节点都完成后才能开始执行。

### 2.1 节点类型

Topca Graph Runtime 支持多种节点类型，允许构建复杂的工作流：

*   **内核 (Kernel)**: 执行一个 CUDA 内核。
*   **内存拷贝 (Memory Copy)**: 在主机和设备之间或设备内部传输数据 (1D, 2D, 3D)。
*   **内存设置 (Memset)**: 向设备内存写入一个固定值。
*   **主机函数 (Host Function)**: 在图的执行过程中回调一个 CPU 函数。
*   **事件 (Event)**: 记录或等待一个 CUDA 事件，用于同步。
*   **子图 (Child Graph)**: 将一个图作为节点嵌入到另一个图中，实现模块化。
*   **空节点 (Empty Node)**: 一个无操作的节点，通常用作依赖关系的汇合点。

## 3. 创建图：两种主要方法

创建图主要有两种方式：流捕获和手动构建。

### 3.1 流捕获 (Stream Capture) - 推荐方法

流捕获是创建图最简单、最直观的方法。您可以像往常一样在 CUDA 流上编写代码，Topca Graph Runtime 会自动将这些操作捕获成一个图。

**工作流程:**

1.  调用 `cudaStreamBeginCapture()` 开始在一个流上进行捕获。
2.  将您想要包含在图中的所有 CUDA 操作（内核、`memcpy` 等）照常启动到该流上。您甚至可以从多个流进行捕获，并使用事件来定义它们之间的依赖关系。
3.  调用 `cudaStreamEndCapture()` 结束捕获，并获得一个 `cudaGraph_t` 对象。

**代码示例:**

```cpp
cudaGraph_t graph;
cudaStream_t stream;
cudaStreamCreate(&stream);

// --- 1. 开始捕获 ---
cudaStreamBeginCapture(stream, cudaStreamCaptureModeGlobal);

// --- 2. 像往常一样启动内核和内存操作 ---
my_kernel_1<<<grid, block, 0, stream>>>(...);
cudaMemcpyAsync(dst, src, size, cudaMemcpyDeviceToDevice, stream);
my_kernel_2<<<grid, block, 0, stream>>>(...);

// --- 3. 结束捕获并获取图 ---
cudaStreamEndCapture(stream, &graph);
```

### 3.2 手动构建 (Manual Construction)

在某些高级应用场景下，您可能需要动态地、逐个地构建图。Topca Graph Runtime 提供了一套完整的 API 用于手动添加节点和定义依赖关系。

**适用场景:**

*   当图的结构在运行时才能确定时。
*   需要对图的拓扑结构进行精细控制。
*   从现有图中移除或替换节点。

**API 示例:**

```cpp
cudaGraph_t graph;
cudaGraphCreate(&graph, 0);

cudaGraphNode_t nodeA, nodeB, nodeC, nodeD;
// ... (为每个节点定义参数)

// 添加节点
cudaGraphAddKernelNode(&nodeA, graph, nullptr, 0, &paramsA);
cudaGraphAddKernelNode(&nodeB, graph, nullptr, 0, &paramsB);
cudaGraphAddKernelNode(&nodeC, graph, nullptr, 0, &paramsC);
cudaGraphAddKernelNode(&nodeD, graph, nullptr, 0, &paramsD);

// 添加依赖关系 (边)
cudaGraphAddDependencies(graph, &nodeB, &nodeA, 1); // A -> B
cudaGraphAddDependencies(graph, &nodeC, &nodeA, 1); // A -> C
cudaGraphAddDependencies(graph, &nodeD, &nodeB, 1); // B -> D
cudaGraphAddDependencies(graph, &nodeD, &nodeC, 1); // C -> D
```

## 4. 实例化和执行图

创建了 `cudaGraph_t` 对象后，您需要先将其“实例化”为一个可执行图 (`cudaGraphExec_t`)，然后才能启动它。

1.  **实例化**: 调用 `cudaGraphInstantiate()`。在此阶段，Topca Graph Runtime 会对图进行验证和深度优化，生成一个高效的执行计划。
2.  **执行**: 调用 `cudaGraphLaunch()` 将实例化的图提交到指定的流上执行。这个过程的 CPU 开销极低。

**代码示例:**

```cpp
// --- 1. 实例化图 ---
cudaGraphExec_t exec_graph;
cudaGraphInstantiate(&exec_graph, graph, nullptr, nullptr, 0);

// --- 2. 在流上启动执行 ---
// 可以在一个循环中高效地重复启动
for (int i = 0; i < 100; ++i) {
    cudaGraphLaunch(exec_graph, stream);
}

cudaStreamSynchronize(stream);

// --- 清理 ---
cudaGraphExecDestroy(exec_graph);
cudaGraphDestroy(graph);
```

## 5. 更新已实例化的图

在许多应用中，工作流的拓扑结构是固定的，但每次执行时的数据或参数会发生变化。为了避免每次都重新实例化图所带来的开销，Topca Graph Runtime 提供了轻量级的图更新机制。

### 5.1 全图更新

您可以创建一个拓扑结构与原始图完全相同的新图，但其中包含更新后的节点参数。然后使用 `cudaGraphExecUpdate()` 将这些更改应用到已实例化的图上。

**注意**: 这种方法要求新图的拓扑结构、节点类型以及依赖关系顺序必须与原图完全一致，否则更新将失败。

### 5.2 单个节点更新

当只有少数节点需要更新时，直接更新单个节点是更高效的方法。这避免了创建新图和进行拓扑比较的开销。

**API 示例:**

```cpp
// 更新一个已实例化的图中特定内核节点的参数
cudaGraphExecKernelNodeSetParams(exec_graph, kernel_node, &new_kernel_params);

// 更新一个内存拷贝节点的目标地址和大小
cudaGraphExecMemcpyNodeSetParams(exec_graph, memcpy_node, &new_memcpy_params);
```

### 5.3 启用/禁用节点

您还可以动态地启用或禁用已实例化图中的某些节点 (`cudaGraphNodeSetEnabled`)。被禁用的节点在功能上等同于一个空节点，其参数会被保留，直到它被重新启用。这允许您创建一个包含所有可能操作的“超级图”，并根据需要在每次启动时定制其行为。

## 6. 流捕获的限制与错误处理

虽然流捕获功能强大，但有一些操作是不被允许的，否则会导致捕获失败：

*   **禁止同步**: 在 `cudaStreamBeginCapture` 和 `cudaStreamEndCapture` 之间，禁止调用任何形式的同步 API，如 `cudaStreamSynchronize()` 或 `cudaDeviceSynchronize()`。
*   **依赖关系必须闭合**: 如果捕获涉及多个流，所有被捕获的流最终都必须通过事件重新汇合到开始捕获的原始流上。
*   **禁止与非捕获的事件交互**: 捕获中的流不能等待一个在捕获之外记录的事件。

## 7. 高级特性：使用用户对象管理资源

在异步的图执行环境中管理外部资源（如自定义分配的内存、文件句柄等）的生命周期是一个挑战。Topca Graph Runtime 支持 **CUDA 用户对象 (User Objects)** 来解决这个问题。

用户对象类似于 C++ 的 `std::shared_ptr`，它将一个用户定义的对象和其析构函数与一个引用计数关联起来。

**工作流程:**

1.  使用 `cudaUserObjectCreate()` 创建一个用户对象，并提供一个自定义的析构回调函数。
2.  将此用户对象的引用与一个图关联（通常通过在图执行期间保持其引用）。
3.  当图不再需要该对象时（例如，当可执行图被销毁且所有相关的 GPU 工作都已完成时），其引用计数会减少。
4.  当引用计数降为零时，运行时会自动调用您提供的析构函数，安全地释放资源。

这确保了即使在复杂的异步执行流中，资源也能被正确、安全地释放，避免了内存泄漏和悬空指针。

## 8. 总结

通过利用 Topca Graph Runtime，您可以将应用程序的性能提升到一个新的水平。它通过一次定义、多次执行的模型，最大限度地减少了 CPU 开销，并为 CUDA 运行时提供了前所未有的优化机会。无论是通过便捷的流捕获还是灵活的手动构建，Topca Graph Runtime 都为构建高效、可扩展的 CUDA 应用程序提供了强大的工具。
