# 使用 CUDA 用户对象 (User Objects) 管理资源

## 1. 问题背景：异步环境中的资源生命周期

在典型的 CUDA 应用中，我们经常需要为 GPU 操作分配一些临时资源，例如一块用于中间计算的设备内存。在传统的同步或简单的异步模型中，管理这些资源的生命周期相对直接。

然而，在 CUDA Graphs 的异步执行环境中，这个问题变得复杂起来。一个图可能会被启动多次，而我们希望资源（例如一个特定的内存缓冲区）能在所有使用它的图执行完毕后被自动、安全地释放，而不是过早或过晚。

以下是一些在图模型中难以处理的传统资源管理模式：

-   **同步创建，异步销毁**:
    ```cpp
    // 这种模式在图中不可行，因为 cudaLaunchHostFunc 这样的回调
    // 在流捕获期间通常是不被允许或难以管理的。
    void libraryWork(cudaStream_t stream) {
        Resource *resource = new Resource(...);
        launchWork(stream, resource);
        cudaLaunchHostFunc(stream, [](void *p) { delete (Resource*)p; }, resource);
    }
    ```
-   **基于事件的资源池**:
    这种模式需要 CPU 的参与来检查事件状态并回收资源，这与图模型旨在减少 CPU 干预的目标相悖。

## 2. 解决方案：CUDA 用户对象

为了解决这个问题，CUDA 引入了**用户对象 (User Objects)** 的概念。它提供了一种机制，可以将用户管理的 CPU 端对象（及其关联的 GPU 资源）的生命周期与 CUDA Graph 的生命周期安全地绑定在一起。

### 工作原理

用户对象的核心是一个**引用计数 (reference count)** 机制，非常类似于 C++ 的 `std::shared_ptr`。

1.  **创建**: 您可以创建一个 `cudaUserObject_t`，并将一个指向您自己管理的任何数据结构（例如一个包含 `cudaMalloc`'d 指针的 C++ 对象）的指针与它关联起来。同时，您需要提供一个**析构回调函数 (destructor callback)**。
2.  **引用计数**: 用户对象内部维护一个引用计数。您可以通过 `cudaUserObjectRetain` 和 `cudaUserObjectRelease` 来增加或减少这个计数。
3.  **与图关联**: 最关键的是，一个图（或更准确地说，一个可执行图 `cudaGraphExec_t`）可以持有用户对象的一个或多个引用。当图被实例化时，它会增加相关用户对象的引用计数。
4.  **自动销毁**: 当用户对象最后的引用被释放时（无论是被用户代码释放，还是因为持有它的最后一个可执行图被销毁且其所有 GPU 工作都已完成），CUDA 运行时会自动在一个内部线程中调用您在创建时提供的析构回调函数。

### 使用流程与代码示例

```cpp
// 1. 定义您的数据结构和析构函数
struct MyResource {
    float* device_buffer;
};

void CUDART_CB cleanup_my_resource(void* p) {
    MyResource* res = static_cast<MyResource*>(p);
    if (res) {
        cudaFree(res->device_buffer);
        delete res;
    }
}

// --- 在您的设置代码中 ---

// 2. 创建并初始化您的资源
MyResource* my_res = new MyResource();
cudaMalloc(&my_res->device_buffer, 1024 * sizeof(float));

// 3. 创建用户对象，初始引用计数为 1
cudaUserObject_t user_obj;
cudaUserObjectCreate(&user_obj, my_res, cleanup_my_resource, 1, cudaUserObjectNoDestructorSync);

// 4. 将用户对象的引用“移动”给图
// (注意：CUDA 没有直接的 cudaGraphAddUserObject API，
// 这种关联通常是在图捕获或实例化期间，通过保留对资源的引用来实现的。
// 以下代码概念性地展示了引用计数的转移)
cudaGraph_t graph = ...; // 创建或捕获一个使用 my_res->device_buffer 的图

// 概念上，图现在持有一个引用，所以我们可以释放我们自己持有的初始引用。
// 在实际操作中，这通常意味着在将图交给某个长期运行时管理后，
// 调用 release。
cudaUserObjectRelease(user_obj, cudaUserObjectNoDestructorSync);

// --- 执行与销毁 ---

cudaGraphExec_t exec;
cudaGraphInstantiate(&exec, graph, ...); // 实例化过程会确保图持有对资源的引用

// ... 多次启动 exec ...
cudaGraphLaunch(exec, stream);

// 当不再需要可执行图时
cudaGraphExecDestroy(exec); // 销毁 exec 会释放它持有的用户对象引用

// 一旦所有持有引用的图都销毁了，并且所有 GPU 工作都完成了，
// cleanup_my_resource 回调函数将被自动调用。
```

## 3. 关键点与限制

-   **线程安全**: 引用计数的增减是线程安全的。
-   **析构函数限制**: 在析构回调函数中，禁止调用大多数 CUDA API，以避免死锁或阻塞 CUDA 的内部线程。您可以做的是发出信号，让另一个您自己的工作线程去执行必要的 CUDA API 调用。
-   **同步**: `cudaUserObjectCreate` 和 `cudaUserObjectRelease` 都有一个标志参数，用于指定析构函数是否需要同步。`cudaUserObjectNoDestructorSync` 表示异步执行，而 `cudaUserObjectDestructorSync` 会阻塞调用线程，直到析构函数执行完毕。

通过使用用户对象，您可以编写出更清晰、更健壮的代码，将资源管理的复杂性委托给 CUDA 运行时，从而专注于核心的计算逻辑。
