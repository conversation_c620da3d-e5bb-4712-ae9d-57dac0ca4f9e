# 从设备端启动图 (Device Graph Launch)

## 1. 动机：实现设备端的动态控制流

在许多高级工作流中，程序需要根据在 GPU 上计算出的数据来动态地决定下一步的操作。传统的做法是将这些决策逻辑放在主机端（CPU），但这通常需要将数据从设备拷贝回主机，在主机上做出决策，然后再启动新的 GPU 工作。这种与主机的往返通信会带来显著的延迟，成为性能瓶颈。

为了解决这个问题，CUDA 提供了**从设备端启动图 (Device Graph Launch)** 的功能。它允许一个正在运行的 CUDA 内核根据其计算结果，直接在设备端启动一个预先定义的图，从而完全避免了与主机的交互。

## 2. 创建可从设备端启动的图

一个可以从设备端启动的图，在概念上被称为**设备图 (Device Graph)**。

### 创建要求

要创建一个设备图，必须满足以下条件：
1.  **实例化标志**: 在调用 `cudaGraphInstantiate()` 时，必须传入 `cudaGraphInstantiateFlagDeviceLaunch` 标志。
2.  **单一设备**: 图中的所有节点都必须位于同一个 GPU 设备上。
3.  **节点类型限制**: 图中只能包含以下类型的节点：
    -   内核节点 (Kernel nodes)
    -   内存拷贝节点 (Memcpy nodes)
    -   内存设置节点 (Memset nodes)
    -   子图节点 (Child graph nodes)
    *特别注意：不允许包含主机节点 (Host nodes) 或事件节点 (Event nodes)。*
4.  **内核限制**:
    -   图中的内核不允许使用 CUDA 动态并行 (CUDA Dynamic Parallelism)。
    -   合作式启动 (Cooperative launches) 是允许的（只要不与 MPS 一起使用）。
5.  **内存拷贝限制**:
    -   只允许在设备内存和/或固定的、设备映射的主机内存之间进行拷贝。
    -   不允许涉及 CUDA 数组 (`cudaArray_t`) 的拷贝。

### 上传到设备

在设备端启动一个图之前，必须先将其相关的资源上传到设备内存中。这可以通过以下两种方式之一完成：
1.  **显式上传**: 在实例化之后，调用 `cudaGraphUpload()`。
2.  **隐式上传**: 在设备端启动之前，先从主机端启动一次该图 (`cudaGraphLaunch`)，这会自动触发上传操作。

## 3. 从设备端启动

在设备端（即在另一个内核中），调用 `cudaGraphLaunch()` 的方式与在主机端几乎完全相同。关键的区别在于 `stream` 参数。在设备端，这个参数不再是一个常规的 CUDA 流，而是用于指定三种不同的**启动模式 (Launch Modes)** 之一的特殊常量。

### 3.1 启动模式

#### 1. Fire and Forget Launch (发射后不管)
-   **流参数**: `cudaStreamGraphFireAndForget`
-   **行为**: 立即异步启动指定的图。启动它的父图**不会**等待这个新启动的子图完成。子图在一个独立的环境中执行。
-   **用途**: 用于派生出并行的、独立的计算任务。

#### 2. Tail Launch (尾部追加启动)
-   **流参数**: `cudaStreamGraphTailLaunch`
-   **行为**: 这是一种**串行化**机制。当启动它的父图（及其所有派生的子图）完全执行完毕后，这个被“尾部追加”的图才会开始执行。如果多次调用尾部追加，这些图会按照追加的顺序依次执行。
-   **用途**: 用于实现设备端的同步和串行依赖关系，例如循环。

#### 3. Sibling Launch (同级启动)
-   **流参数**: `cudaStreamGraphFireAndForgetAsSibling`
-   **行为**: 与 Fire and Forget 类似，也是异步启动。但不同的是，它被视为启动图的父环境的“孩子”，而不是启动图本身的“孩子”。这意味着它的完成状态与启动它的图无关。
-   **用途**: 用于复杂的、需要精细控制执行环境依赖关系的场景。

### 3.2 在循环中重新启动自身

一个常见的用例是创建一个循环。这可以通过让一个图在执行结束时，使用**尾部追加启动 (Tail Launch)** 来重新启动自身来实现。

为了获取当前正在执行的图的句柄，CUDA 提供了一个只能在设备端调用的函数：
`cudaGraphExec_t cudaGetCurrentGraphExec();`

#### 示例：设备端循环

```cpp
__device__ int relaunchCount = 0;
const int relaunchMax = 100;

__global__ void relaunchSelfKernel() {
    // 通常只让一个线程执行启动逻辑
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        if (relaunchCount < relaunchMax) {
            // 获取当前图的句柄
            cudaGraphExec_t currentGraph = cudaGetCurrentGraphExec();
            // 尾部追加启动自身，实现下一次迭代
            cudaGraphLaunch(currentGraph, cudaStreamGraphTailLaunch);
        }
        relaunchCount++;
    }
}
```
这个内核本身会成为一个图的一部分。当这个图被启动时，`relaunchSelfKernel` 会执行，并安排图自身在当前执行完成后再次运行，从而形成一个循环，直到 `relaunchCount` 达到最大值。
