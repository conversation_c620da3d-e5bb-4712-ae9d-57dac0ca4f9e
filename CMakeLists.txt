cmake_minimum_required(VERSION 3.20)
project(CudaGraphRuntimeTestSuite LANGUAGES CXX CUDA)

# Set C++ standard for the whole project
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# Enable testing for CTest
enable_testing()

# --- Find dependencies ---
find_package(CUDA REQUIRED)

# --- Configure GoogleTest using FetchContent at the top level ---
include(FetchContent)
FetchContent_Declare(
  googletest
  URL https://github.com/google/googletest/archive/refs/tags/v1.14.0.zip
)
# Make it available to the whole project
FetchContent_MakeAvailable(googletest)


# --- Add the runtime library (if you have one) ---
# add_library(CudaGraphRuntime src/runtime.cpp)
# target_include_directories(CudaGraphRuntime PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/include)
# target_link_libraries(CudaGraphRuntime PRIVATE CUDA::cudart)


# --- Add the tests subdirectory ---
add_subdirectory(tests)
