# CUDA Graph 结构

一个 CUDA Graph 在本质上是一个**有向无环图 (Directed Acyclic Graph, DAG)**。它由两种基本元素构成：**节点 (Nodes)** 和 **边 (Edges)**。

-   **节点 (Node)**: 代表图中的一个独立**操作**。每个节点都是一个工作单元，例如一次内核启动或一次内存拷贝。
-   **边 (Edge)**: 代表节点之间的**依赖关系**。一条从节点 A 指向节点 B 的边意味着，操作 B 必须在操作 A 完成之后才能开始执行。

CUDA 系统会根据这些依赖关系来调度节点的执行。任何时候，只要一个节点的所有依赖项都已完成，该节点就可以被调度执行。

## 节点类型 (Node Types)

一个图节点可以是以下多种类型之一，这使得构建功能丰富且复杂的工作流成为可能：

-   **内核 (Kernel)**:
    代表一次 CUDA 内核的启动。这是最常见的节点类型，用于执行核心的并行计算任务。

-   **CPU 函数调用 (CPU Function Call)**:
    也称为主机节点 (Host Node)，它允许在图的执行过程中，从 GPU 回调一个在 CPU 上执行的函数。这对于执行一些无法在 GPU 上完成的逻辑或与外部系统交互非常有用。

-   **内存拷贝 (Memory Copy)**:
    代表一次内存传输操作，例如 `cudaMemcpy`。它可以是在主机和设备之间，也可以是在两个不同的设备内存区域之间。

-   **内存设置 (Memset)**:
    代表一次 `cudaMemset` 操作，用于将一块设备内存区域设置为一个固定的值。

-   **空节点 (Empty Node)**:
    一个无操作的节点。它本身不执行任何工作，但可以作为多个依赖关系的汇合点或分叉点，对于构建复杂的图拓扑结构非常有用。

-   **事件操作 (Event Operations)**:
    -   **等待事件 (Wait on Event)**: 使图的执行流暂停，直到一个特定的 CUDA 事件被触发。
    -   **记录事件 (Record an Event)**: 在图的执行流中某个点触发一个 CUDA 事件。
    这两个节点类型是实现与外部流或图执行同步的关键。

-   **外部信号量操作 (External Semaphore Operations)**:
    用于与外部（非 CUDA）的同步机制进行交互，通过操作内存中的信号量来实现。

-   **子图 (Child Graph)**:
    允许将一个完整的、独立的图作为节点嵌入到另一个父图中。这极大地促进了图的模块化和代码重用，使得可以构建层次化的复杂工作流。
